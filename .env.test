# ENV
PYTHON_VERSION="313" # which dockerfile to use. see in dockerfiles/python*/Dockerfile

# 应用基础配置
PROJECT_NAME="Aitools"
API_V1_STR="/api/v1"
SECRET_KEY="09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7"
ACCESS_TOKEN_EXPIRE_MINUTES="11520"
REFRESH_TOKEN_EXPIRE_MINUTES="11520"
PASSWORD_RESET_TOKEN_EXPIRE_MINUTES="10"
# API认证配置
API_USERNAME="ubuntu"
API_PASSWORD="debian"
API_ALGORITHM="HS256"

# 管理员配置
ADMIN_USERNAME="<EMAIL>"
ADMIN_PASSWORD="qinjun666"

# 微信配置
WECHAT_APP_ID="wx7c7e559acf0a4316"
WECHAT_APP_SECRET="604b63f91fabc5c71edbdcf54edcc750"
WECHAT_AUTO_CREATE_USER="true"
WECHAT_ENABLED="true"
# CORS配置
BACKEND_CORS_ORIGINS="http://localhost:3000,http://localhost:8080,http://localhost"

# 数据库配置

SQLALCHEMY_DATABASE_URI="sqlite+aiosqlite://"

# Redis配置
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DB="0"
REDIS_PASSWORD=""

# 租户配置
MULTI_TENANT_ENABLED="false"
DEFAULT_TENANT_ID="default"

