name: 运行单元测试

on:
  push:

  pull_request:
    branches:
      - master

  schedule:
    # 每周日UTC时间8:05运行
    - cron: "5 8 * * 0"

# 如果在前一个工作流运行时触发新的工作流，这将取消前一个工作流
concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  run-tests:
    runs-on: ${{ matrix.os }}
    strategy:
      # 使用矩阵策略在多个操作系统和Python版本上运行测试
      matrix:
        os: [ubuntu-latest, macos-latest]
        python-version: ["3.11", "3.12", "3.13"]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: 安装uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"

      - name: 安装依赖
        run: |
          echo "安装依赖..."
          uv sync

      - name: 检查代码格式
        run: |
          echo "检查代码格式..."
          make lint-check

      - name: 运行测试并生成覆盖率报告
        run: |
          echo "运行测试并生成覆盖率报告..."
          cd svc && uv run pytest -vv --cov=. --cov-report=xml && cd ..

      - name: 上传覆盖率报告到Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./svc/coverage.xml
          fail_ci_if_error: false
          token: ${{ secrets.CODECOV_TOKEN }}
          verbose: true

      - name: 上传测试结果
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{ matrix.os }}-${{ matrix.python-version }}
          path: |
            svc/coverage.xml
            svc/.coverage
