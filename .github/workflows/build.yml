name: 构建和发布Docker镜像

on:
  push:
    branches:
      - master
    tags:
      - "v*"
  pull_request:
    branches:
      - master
  schedule:
    # 每周日UTC时间8:05运行
    - cron: "5 8 * * 0"

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    strategy:
      matrix:
        python-version: [313] # 使用Python 3.13作为主要版本

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录到GitHub容器注册表
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short

      - name: 构建并测试容器
        run: |
          sed -i "s/PYTHON_VERSION=.*/PYTHON_VERSION=${{ matrix.python-version }}/" '.env'
          echo "构建Python ${{ matrix.python-version }}版本的容器"
          docker compose up -d

      - name: 等待并检查容器健康状态
        run: |
          attempts=0
          max_attempts=10

          while [ $attempts -lt $max_attempts ]; do
            if curl -s http://localhost:8000/health | grep -q "healthy"; then
              echo "容器健康检查成功！"
              docker compose logs
              break
            fi
            
            attempts=$((attempts+1))
            echo "等待容器启动，尝试 $attempts/$max_attempts..."
            sleep 3
          done

          if [ $attempts -eq $max_attempts ]; then
            echo "容器健康检查失败，查看日志："
            docker compose logs
            exit 1
          fi

      - name: 构建并推送Docker镜像
        if: github.event_name != 'pull_request'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            PYTHON_VERSION=${{ matrix.python-version }}

      - name: 清理资源
        if: always()
        run: docker compose down
