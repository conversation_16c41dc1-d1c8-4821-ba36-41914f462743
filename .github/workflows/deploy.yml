name: 部署应用

on:
  workflow_run:
    workflows: ["构建和发布Docker镜像"]
    branches: [master]
    types: [completed]

  # 允许手动触发部署
  workflow_dispatch:
    inputs:
      environment:
        description: "部署环境"
        required: true
        default: "staging"
        type: choice
        options:
          - staging
          - production

jobs:
  deploy-staging:
    name: 部署到测试环境
    if:
      ${{ github.event.workflow_run.conclusion == 'success' || (github.event_name ==
      'workflow_dispatch' && github.event.inputs.environment == 'staging') }}
    runs-on: ubuntu-latest
    environment: staging

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: 设置known_hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan ${{ secrets.STAGING_HOST }} >> ~/.ssh/known_hosts

      - name: 登录到GitHub容器注册表
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 准备部署文件
        run: |
          # 创建部署目录
          mkdir -p deploy

          # 复制必要的配置文件
          cp docker-compose.yml deploy/
          cp prometheus.yml deploy/
          cp -r grafana deploy/

          # 创建.env文件
          cat > deploy/.env << EOF
          REGISTRY=ghcr.io
          IMAGE_NAME=${{ github.repository }}
          IMAGE_TAG=sha-$(git rev-parse --short HEAD)
          POSTGRES_USER=${{ secrets.POSTGRES_USER }}
          POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_DB=${{ secrets.POSTGRES_DB }}
          DATABASE_URL=postgresql+asyncpg://${{ secrets.POSTGRES_USER }}:${{ secrets.POSTGRES_PASSWORD }}@db:5432/${{ secrets.POSTGRES_DB }}
          REDIS_URL=redis://redis:6379/0
          SECRET_KEY=${{ secrets.SECRET_KEY }}
          BACKEND_CORS_ORIGINS='["http://localhost:3000","http://localhost:8080","http://localhost"]'
          EOF

      - name: 部署到测试服务器
        run: |
          # 将部署文件复制到服务器
          scp -r deploy/* ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }}:${{ secrets.STAGING_PATH }}

          # 登录到服务器并启动应用
          ssh ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} "cd ${{ secrets.STAGING_PATH }} && docker compose pull && docker compose up -d"

      - name: 验证部署
        run: |
          # 等待应用启动
          sleep 10

          # 检查应用是否正常运行
          curl -s -o /dev/null -w "%{http_code}" https://${{ secrets.STAGING_DOMAIN }}/health | grep 200

  deploy-production:
    name: 部署到生产环境
    if:
      ${{ github.event_name == 'workflow_dispatch' && github.event.inputs.environment ==
      'production' }}
    runs-on: ubuntu-latest
    environment: production
    needs: [deploy-staging] # 确保先部署到测试环境

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: 设置known_hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan ${{ secrets.PRODUCTION_HOST }} >> ~/.ssh/known_hosts

      - name: 登录到GitHub容器注册表
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 准备部署文件
        run: |
          # 创建部署目录
          mkdir -p deploy

          # 复制必要的配置文件
          cp docker-compose.yml deploy/
          cp prometheus.yml deploy/
          cp -r grafana deploy/

          # 创建.env文件
          cat > deploy/.env << EOF
          REGISTRY=ghcr.io
          IMAGE_NAME=${{ github.repository }}
          IMAGE_TAG=sha-$(git rev-parse --short HEAD)
          POSTGRES_USER=${{ secrets.POSTGRES_USER }}
          POSTGRES_PASSWORD=${{ secrets.POSTGRES_PASSWORD }}
          POSTGRES_DB=${{ secrets.POSTGRES_DB }}
          DATABASE_URL=postgresql+asyncpg://${{ secrets.POSTGRES_USER }}:${{ secrets.POSTGRES_PASSWORD }}@db:5432/${{ secrets.POSTGRES_DB }}
          REDIS_URL=redis://redis:6379/0
          SECRET_KEY=${{ secrets.SECRET_KEY }}
          BACKEND_CORS_ORIGINS='["https://${{ secrets.PRODUCTION_DOMAIN }}"]'
          EOF

      - name: 部署到生产服务器
        run: |
          # 将部署文件复制到服务器
          scp -r deploy/* ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }}:${{ secrets.PRODUCTION_PATH }}

          # 登录到服务器并启动应用
          ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "cd ${{ secrets.PRODUCTION_PATH }} && docker compose pull && docker compose up -d"

      - name: 验证部署
        run: |
          # 等待应用启动
          sleep 10

          # 检查应用是否正常运行
          curl -s -o /dev/null -w "%{http_code}" https://${{ secrets.PRODUCTION_DOMAIN }}/health | grep 200
