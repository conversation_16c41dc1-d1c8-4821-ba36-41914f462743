# 系统模式

## 系统架构

AItools 采用分层架构和模块化设计，主要包括以下核心层次：

1.  **API 层 (svc/apps/\*/routers)**：处理 HTTP 请求和响应，使用 FastAPI 的 APIRouter 定义路
    由，Pydantic 模型进行参数验证，统一异常处理 (svc/core/exceptions)。
2.  **服务层 (svc/apps/\*/services)**：实现业务逻辑，继承自 `BaseService`
    (svc/core/services/base.py)，封装核心功能，提供给 API 层调用。使用 `Result` 对象
    (svc/core/services/result.py) 封装返回结果和错误状态。
3.  **数据访问层 (svc/apps/\*/repositories)**：负责数据库操作，使用 UserRepository,
    RoleRepository 等仓库模式实现。继承自 `BaseRepository` (svc/core/repositories/base.py)
    类，支持通用的 CRUD 操作，依赖 SQLAlchemy 2.0 异步 API。
4.  **模型层 (svc/apps/\*/models)**：定义 SQLAlchemy 数据模型，继承自 `Base`
    (svc/core/database/session.py)。
5.  **核心基础设施层 (svc/core)**：提供跨层功能，如配置管理 (config)、异步数据库会话
    (database)、安全认证 (security)、事件系统 (events)、中间件 (middleware)、缓存 (cache)、
    国际化 (i18n) 等。

整体架构支持异步编程模型 (async/await)，通过 FastAPI 的依赖注入 (Depends) 实现组件间的松耦合
设计。

## 关键技术决策

-   选择 FastAPI 作为基础框架：利用其高性能、异步支持和类型提示功能。
-   采用 SQLAlchemy 2.0 作为 ORM 工具：提供异步数据库操作和类型安全的查询构建。
-   实现自定义事件总线 (svc/core/events/event_bus.py)：使用发布-订阅模式处理系统内部通信，支
    持本地和远程事件处理 (通过 task_manager 集成，但具体实现未完全展示)。
-   基于 JWT 的认证机制 (svc/core/security, svc/apps/auth)：提供安全可靠的用户认证和会话管理
    。
-   集成 Prometheus 和 Grafana (可选)：实现实时监控和性能指标收集。
-   多级缓存策略 (svc/core/cache, svc/core/services/base.py)：结合内存缓存和 Redis 分布式缓
    存提升性能，提供函数级和资源级缓存装饰器/工具。
-   实现基于角色的权限控制 (svc/apps/auth)：细粒度的资源访问管理。
-   使用 Pydantic 进行数据验证和序列化 (svc/apps/\*/schemas)。
-   自动路由注册 (svc/core/router/router_factory.py)：简化模块路由配置。

## 设计模式

-   **依赖注入模式**：通过 FastAPI 的 Depends 机制实现组件注入 (如 `get_db`,
    `get_user_service`)，提高可测试性和解耦。
-   **服务层模式**：使用 `BaseService` 泛型基类封装业务逻辑，统一错误处理和结果返回。
-   **仓库模式**：在数据访问层 (repositories) 封装数据库操作逻辑。
-   **工厂模式**：使用 `ResultFactory` 创建统一结果对象。
-   **事件驱动模式**：使用 `EventBus` 实现发布/订阅，解耦组件。
-   **中间件模式**：基于 Starlette BaseHTTPMiddleware 实现请求处理流程
    (svc/core/middleware)，如日志、认证、安全头。
-   **策略模式** (潜在)：缓存策略可在 `BaseService` 中选择。
-   **单例模式** (通过 @lru_cache)：如 `get_settings`。
-   **结果模式**：使用 `Result` 对象封装操作结果和错误状态，统一处理成功和失败情况。

## 组件关系

系统由多个核心组件和业务模块组成，通过依赖注入和事件通信实现松耦合：

-   **FastAPI App (svc/main.py)**: 应用入口，集成各组件。
-   **配置管理器 (svc/core/config/settings.py)**：负责加载和提供系统配置，被其他组件依赖。
-   **数据库会话管理器 (svc/core/database/session.py)**：管理数据库连接和会话生命周期
    (`get_db` 依赖)。
-   **事件总线 (svc/core/events/event_bus.py)**：连接发布者和订阅者，实现组件间通信。
-   **缓存管理器 (svc/core/cache, svc/core/services/base.py)**：提供统一的缓存接口和实现。
-   **安全模块 (svc/core/security, svc/apps/auth)**：处理认证和授权逻辑。
-   **路由注册器 (svc/core/router/router_factory.py)**：自动发现和注册 API 路由。
-   **中间件注册器 (svc/core/middleware/registry.py)**: 统一管理和加载中间件。
-   **业务模块 (svc/apps/\*)**: 如 auth, billing, marketing, system，包含各自的路由、服务、
    模型、仓库等。

各组件通过接口定义（Python 类型提示）和依赖注入框架实现解耦。

## 仓库模块分析与重构方案

BaseRepository 是系统的核心数据访问基础设施，提供了统一的 CRUD 操作和数据查询能力。当前实现
具有良好的基础架构，但存在一些可优化点。

### 当前实现特点

-   **泛型设计**：使用 TypeVar 和 Generic 提供类型安全，支持三种类型参数：ModelType（必需）
    、CreateSchemaType 和 UpdateSchemaType（可选）
-   **标准 CRUD 操作**：提供
    get_by_id、get_one、get_list、count、get_paginated、create、update、delete 等标准方法
-   **查询构建**：\_build_query 方法支持动态条件构建和排序
-   **分页支持**：同时支持 offset/limit 分页和页码分页

### 发现的问题

1. **参数传递不一致**：

    - 服务层与仓库层接口参数形式不匹配
    - 存在字典参数与独立参数混用的情况，导致类型错误
    - get_paginated 方法在接收 skip/limit 和 page/page_size 时容易出现参数类型错误

2. **过滤能力有限**：

    - 仅支持相等（==）条件过滤
    - 不支持复杂条件如模糊匹配、范围查询等

3. **错误处理不足**：

    - 缺少专门的错误类型和处理机制
    - 返回类型不一致（有时返回 None，有时返回布尔值）

4. **缓存集成缺失**：
    - 仓库层未内置缓存机制，缓存逻辑分散在服务层

### 重构建议

1. **参数传递标准化**：

    - 使用 Pydantic 模型封装参数，替代原始字典
    - 统一方法签名和参数传递方式
    - 明确区分页码分页和偏移分页

2. **高级查询支持**：

    - 实现 FilterOperator 枚举，支持等于、不等于、大于、小于、包含等操作符
    - 添加复合过滤条件支持，如 AND、OR 组合
    - 提供模糊匹配和范围查询功能

3. **异常处理增强**：

    - 引入专用的 RepositoryError 异常体系
    - 提供 EntityNotFoundError 等具体异常类型
    - 统一返回类型和错误处理方式

4. **缓存策略整合**：

    - 创建 CacheableRepository 子类，集成缓存功能
    - 提供标准化的缓存键生成和失效策略
    - 与服务层缓存协调工作

5. **文档和测试改进**：
    - 增加更详细的方法文档
    - 提供使用示例和最佳实践
    - 添加完整的单元测试覆盖

### 实现示例

```python
# 高级过滤条件支持
class FilterOperator(str, Enum):
    EQ = "eq"     # 等于
    NE = "ne"     # 不等于
    GT = "gt"     # 大于
    GE = "ge"     # 大于等于
    LT = "lt"     # 小于
    LE = "le"     # 小于等于
    IN = "in"     # 在列表中
    LIKE = "like" # 模糊匹配
    ILIKE = "ilike" # 不区分大小写模糊匹配

class QueryFilter(BaseModel):
    field: str
    op: FilterOperator = FilterOperator.EQ
    value: Any

# 具有缓存能力的仓库基类
class CacheableRepository(BaseRepository[ModelType, CreateSchemaType, UpdateSchemaType]):
    """支持缓存的仓库基类"""

    def __init__(self, model: Type[ModelType], cache_ttl: int = 3600):
        super().__init__(model)
        self.cache_ttl = cache_ttl

    async def get_cache_key(self, id: Any) -> str:
        """生成缓存键"""
        return f"{self.model.__name__}:{id}"

    async def get_by_id(self, db: AsyncSession, id: Any, id_field: str = "id") -> Optional[ModelType]:
        """带缓存的ID查询"""
        if hasattr(self, "redis") and self.redis:
            # 尝试从缓存获取
            cache_key = await self.get_cache_key(id)
            cached_item = await self.get_cached_resource(cache_key)
            if cached_item:
                return cached_item

        # 从数据库获取
        item = await super().get_by_id(db, id, id_field)

        # 存入缓存
        if item and hasattr(self, "redis") and self.redis:
            await self.cache_resource(cache_key, item)

        return item
```

### 迁移策略

1. **渐进式实施**：

    - 先修复参数传递问题
    - 逐步引入新功能，保持向后兼容
    - 最后进行完整重构

2. **测试驱动**：

    - 为每个修改编写单元测试
    - 确保覆盖边界情况和错误处理

3. **文档更新**：
    - 更新 API 文档
    - 提供迁移指南
    - 记录最佳实践

这些改进将使仓库模块更加健壮、类型安全，并提供更强大的查询能力，同时保持与服务层的良好协作。

## 认证模块详细架构

认证模块 (svc/apps/auth) 是系统的核心安全组件，具有以下主要组成部分：

1. **数据模型 (models)**:

    - `User`: 核心用户模型，包含用户基本信息、密码哈希、登录状态等
    - `Role`: 角色模型，定义系统角色
    - `Permission`: 权限模型，定义细粒度权限
    - `user_role`: 用户-角色多对多关联表

2. **数据验证与传输 (schemas)**:

    - 认证相关：`AuthCredentials`, `TokenData`, `LoginRequest` 等
    - 用户相关：`UserCreate`, `UserUpdate`, `UserResponse` 等
    - 角色相关：`RoleCreate`, `RoleUpdate`, `RoleResponse` 等

3. **服务层 (services)**:

    - `AuthService`: 提供用户认证、令牌管理、密码重置功能
    - `UserService`: 提供用户管理功能，包括创建、更新、删除用户
    - `RoleService`: 提供角色和权限管理功能

4. **数据访问层 (repositories)**:

    - `UserRepository`: 封装用户数据库操作
    - `RoleRepository`: 封装角色数据库操作

5. **API 路由 (routers)**:

    - 认证路由：登录、注销、刷新令牌、密码重置等
    - 用户路由：用户 CRUD 操作
    - 角色路由：角色和权限管理

6. **依赖项 (dependencies.py)**:
    - `get_current_user`: 从请求中提取并验证当前用户
    - `get_current_active_user`: 确保用户处于活跃状态
    - `get_current_superuser`: 验证超级用户权限
    - 服务依赖：`get_auth_service`, `get_user_service`, `get_role_service`

这些组件协同工作，提供完整的用户认证、授权和管理功能，确保系统安全性和可扩展性。

## 关键实现路径

-   **用户认证流程 (详细)**：

    1. 客户端发起登录请求，携带用户名和密码 -> 路由
       `svc/apps/auth/routers/auth.py::login_api`
    2. 请求数据通过 `LoginParams` Pydantic 模型验证
    3. 调用 `AuthService.login` 方法，参数为验证后的 `LoginParams`
    4. `AuthService.login` 调用 `AuthService.authenticate` 验证用户凭据
        - 首先尝试从 Redis 缓存获取用户信息
        - 如缓存未命中，通过 `UserRepository` 查询数据库
        - 验证密码哈希是否匹配
        - 检查用户状态（是否激活、是否锁定）
        - 记录登录尝试并更新用户状态
    5. 若认证成功，通过 `TokenService` 生成 JWT 访问令牌和刷新令牌
    6. 通过 `event_bus.emit` 触发登录成功事件
    7. 返回包含令牌的 `TokenData` 对象
    8. 请求通过依赖注入的 `UserService` 获取完整的用户信息和角色
    9. 组装并返回登录响应

-   **受保护路由访问 (svc/apps/auth/routers/users.py)**: 请求 -> 认证中间件
    (svc/core/middleware/auth.py) -> 验证 JWT -> `get_current_user` 依赖
    (svc/apps/auth/dependencies.py) -> 获取用户 -> 业务处理。
-   **角色和权限管理流程**:
    1. API 请求访问 `svc/apps/auth/routers/roles.py` 角色管理端点
    2. 请求通过 `get_current_superuser` 依赖验证管理员权限
    3. 请求参数通过 Pydantic 模型（如 `RoleCreate`）验证
    4. 调用 `RoleService` 的相应方法（如 `create_role`）
    5. `RoleService` 通过 `RoleRepository` 访问数据库执行操作
    6. 操作成功后，角色数据被缓存到 Redis
    7. 返回 `Result` 对象封装的操作结果
-   **数据处理流程 (通用)**：API 请求 -> 路由处理函数 -> Pydantic 模型验证 -> 服务层方法调用
    (依赖注入) -> 仓库层方法调用 -> SQLAlchemy 异步数据库操作 -> 返回 `Result` 对象 ->
    FastAPI 响应。
-   **事件处理流程 (svc/core/events/event_bus.py)**：业务逻辑触发 `event_bus.emit` ->
    `_emit_local` (同步/异步执行本地处理器) / `_emit_remote` (通过 `task_manager` 发送到队列
    )。
-   **系统健康检查流程 (svc/apps/system/routers/health.py)**：请求 -> 路由处理函数 ->
    `SystemService.get_health_status` -> 收集系统组件状态 -> 返回健康状态信息。
-   **系统信息流程 (svc/apps/system/routers/info.py)**：请求 -> 路由处理函数 ->
    `SystemService.get_system_info` -> 获取系统运行信息 -> 返回系统信息。

## 数据流

1.  **API 请求流**：客户端 -> Nginx/LB (可选) -> Uvicorn -> FastAPI -> 中间件链 -> API 路由
    -> Pydantic 验证 -> 服务层 -> 仓库层 -> SQLAlchemy -> 数据库。
2.  **缓存流**：服务层查询 -> `BaseService.get_cached_resource/collection` -> Redis/内存缓存
    检查 -> 命中则返回 / 未命中则查询数据库 -> `BaseService.cache_resource/collection` 更新
    缓存 -> 返回结果。
3.  **事件流**：事件源 (如用户创建服务) -> `event_bus.emit` -> EventBus 分发 -> 本地处理器执
    行 / 远程任务入队。
4.  **认证流详解**：
    -   **登录流程**：登录请求 -> `AuthService.login` -> 验证凭据 -> 生成 JWT -> 发布登录成
        功事件 -> 返回令牌
    -   **令牌校验**：请求头包含 Bearer 令牌 -> 认证中间件提取令牌 -> 解析 JWT ->
        `get_current_user` 解码令牌并获取用户 ID -> `UserService` 获取用户对象 -> 验证用户状
        态
    -   **权限验证**：请求 -> `get_current_user` -> 获取用户角色和权限 -> 检查操作所需权限
        -> 允许/拒绝请求
    -   **令牌刷新**：刷新请求 -> `AuthService.validate_token` 验证刷新令牌 -> 生成新的访问
        令牌 -> 返回
    -   **密码重置**：请求重置 -> 生成并存储重置令牌 -> 发送重置邮件 -> 用户确认 -> 验证令牌
        -> 更新密码
5.  **健康检查流**：健康检查请求 -> 健康检查路由 -> `SystemService.get_health_status` -> 检
    查数据库、Redis 等组件状态 -> 收集状态信息 -> 返回统一健康状态。

## 扩展点

系统预留了多个扩展点，允许在不修改核心代码的情况下进行功能扩展：

1.  **添加新业务模块 (svc/apps)**: 创建新目录，遵循模块结构 (routers, services, models,
    etc.)，路由会自动注册。
2.  **添加新中间件 (svc/core/middleware)**: 创建继承自 `BaseMiddleware` 的类，并在
    `middleware_config` 中注册。
3.  **添加新事件处理器 (svc/core/events)**: 使用 `@event_bus.on_local` 或
    `@event_bus.on_remote` 装饰器注册新处理器。
4.  **添加新服务 (svc/apps/\*/services)**: 创建继承自 `BaseService` 的类。
5.  **添加新仓库 (svc/apps/\*/repositories)**: 创建数据访问类。
6.  **自定义缓存策略**: 在服务层实现特定的缓存逻辑或覆盖 `BaseService` 方法。
7.  **添加新认证方式**: 修改或扩展 `svc/core/security` 和 `svc/apps/auth`，例如：
    -   实现新的认证提供商（如 OIDC、LDAP）
    -   添加新的社交登录（已有微信登录基础实现）
    -   实现双因素认证
8.  **扩展权限系统**: 通过添加新权限和角色，实现更细粒度的访问控制。
9.  **添加新的健康检查项**: 修改 `SystemService.get_health_status` 方法，添加新的组件检查。

## 测试模式

系统采用多层次测试策略，确保功能正确性和代码质量：

1. **单元测试**：测试独立组件功能

    - 服务层测试：如 `RoleService` 全面测试（`test_role_service.py`）
    - 仓库层测试：验证数据访问功能
    - 模型测试：确保数据模型关系正确

2. **集成测试**：测试组件间交互

    - API 测试：验证 HTTP 端点功能
    - 数据库集成测试：确保 ORM 映射正确
    - 缓存集成测试：验证缓存与数据库交互

3. **测试工具和技术**：
    - pytest 和 pytest-asyncio：异步测试支持
    - 测试夹具（fixtures）：提供测试依赖，如测试数据库会话、测试用户
    - 模拟对象（mocks）：隔离测试依赖，如 Redis 缓存
    - 参数化测试：测试不同输入场景
