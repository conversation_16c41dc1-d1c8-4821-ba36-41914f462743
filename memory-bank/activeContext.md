# 活动上下文

## 当前工作重点 (基于代码现状推断)

-   完善系统模块：完成审计日志功能，扩展已有的健康检查和系统信息 API。
-   增强认证模块：优化 `auth` 模块的角色和权限管理，增强安全特性，提高缓存效率。
-   实现业务模块：完成 `billing`, `marketing` 模块的核心功能。
-   后台任务处理：集成并配置一个 Task Queue (如 Celery, ARQ) 以支持远程事件和异步任务。
-   监控与告警：完善 Prometheus 指标收集，配置 Grafana 仪表盘，并设置告警规则。
-   增强测试：提高单元测试和集成测试的覆盖率，特别是针对核心基础设施、`auth` 模块的角色服务
    和权限控制。
-   完善文档：补充开发者文档，特别是关于如何添加新模块、使用事件系统和缓存的细节，以及
    `auth` 模块的详细使用指南。

## 最近变更 (基于文件时间戳和上次记录)

-   ~2023-04-20: 重构数据库种子脚本 `seed_data.py`，确保所有营销活动和订阅计划均使用管理员作
    为默认创建者（设置 `creator_id` 为 1），修复邀请记录和奖励记录创建的功能，优化日志记录，
    提高脚本的可靠性和一致性。
-   ~2023-04-19: 完成 `auth` 模块的详细分析，包括用户认证、角色管理和权限控制系统的实现细节
    ，为增强测试覆盖做准备。
-   ~2023-04-18: 实现系统模块的审计日志功能，包括数据模型、API 和服务层逻辑，支持记录和查询
    系统操作。
-   ~2023-04-17: 更新项目优先级，将现有功能完善放在首位，复杂新功能优先级降低。
-   ~2023-04-16: 优化 auth 模块，改进类型标注和依赖注入，修复 Permission 模型导出，增强代码
    可维护性。
-   ~2023-04-15: 修复系统模块的 ConfigService，解决了 BaseRepository 导入路径问题和参数不足
    问题。
-   ~2023-04-10: 实现 system 模块的健康检查和系统信息 API，修复 ConfigItem 模型实现。
-   ~2023-04-07: 完成代码库分析和 Memory Bank 更新。
-   ~2023-04-05: (上次记录) 集成 Prometheus 监控系统，新增性能指标收集 (部分实现)。
-   ~2023-04-04: (上次记录) 优化身份验证和权限控制模块 (`auth` 模块相对完善)。
-   ~2023-04-03: (上次记录) 实现事件驱动系统 (svc/core/events)。
-   ~2023-03-31: (上次记录) 完成 API 层和服务层基础架构 (svc/core/services/base.py,
    svc/core/router)。
-   ~2023-03-29: (上次记录) 重构数据库会话管理 (svc/core/database/session.py)。

## 下一步计划 (优先级已调整)

1.  提高测试覆盖率，特别是针对 `auth` 模块的 `RoleService`、`AuthService` 和权限控制系统，参
    考已实现的 `test_role_service.py` 作为模板。
2.  完善 `auth` 模块的开发者文档，详细说明用户认证流程、角色和权限管理，以及与缓存系统的集成
    。
3.  优化 `auth` 模块的缓存策略，特别是针对频繁访问的用户和角色数据，提高性能和资源利用效率。
4.  增强 `auth` 模块的安全特性，包括防暴力破解、会话管理和密码策略。
5.  完善监控集成，为 `auth` 模块添加关键性能指标，配置 Grafana 仪表盘以可视化认证和授权相关
    指标。
6.  完善系统模块的配置管理功能。
7.  测试种子数据脚本的功能，确认数据初始化正确，特别是营销活动和订阅计划的创建者关联。

## 活动决策和考虑 (更新)

-   认证架构：`auth` 模块采用分层设计，包括 models、schemas、services、repositories 和
    routers，保持了清晰的职责分离。
-   监控方案：Prometheus + Grafana 是选定的方向，但集成尚不完整。
-   数据库：坚持使用 PostgreSQL 和 SQLAlchemy 2.0 异步。
-   事件总线：当前自定义实现满足基本需求，但远程处理需 Task Queue 支持。
-   缓存：`BaseService` 提供了基础，但 `auth` 模块中的用户和角色缓存策略需要根据访问模式进一
    步优化。
-   代码质量：继续使用 Ruff 和 Mypy 强制执行，优先进行类型注解和文档完善。
-   依赖管理：考虑统一使用 `uv` 或 `pip-tools`。
-   功能优先级：优先完善现有功能，提高系统稳定性和可用性，新功能开发推迟到核心功能完善后。
-   审计日志：已完成基础实现，支持记录系统关键操作并提供查询功能，满足系统安全审计需求。
-   认证安全：`auth` 模块已实现基本的安全机制，包括密码哈希、JWT 认证和登录失败锁定，但需要
    进一步增强。
-   数据一致性：所有系统生成的数据应该有明确的创建者，特别是营销活动和订阅计划这类重要业务数
    据。默认使用管理员作为创建者确保数据完整性和一致性。

## 重要模式和偏好 (基于代码)

-   异步优先：所有 I/O 密集型操作均使用 `async/await`。
-   依赖注入：广泛使用 FastAPI Depends，特别是 `auth` 模块中的
    `get_current_user`、`get_auth_service` 等依赖。
-   类型安全：强制使用类型注解 (Mypy)，提高代码可靠性。
-   分层架构：清晰的 API -> 服务 -> 仓库 -> 模型分层，`auth` 模块是典型示例。
-   模块化：按功能划分 `apps` 目录，核心功能在 `core`。
-   统一错误处理：通过 `svc/core/exceptions` 和 `Result` 对象。
-   仓库模式：用于数据访问层，`auth` 模块中的 `UserRepository` 和 `RoleRepository` 是典型实
    现。
-   服务层模式：`BaseService` 提供通用逻辑，`auth` 模块的服务类扩展了这些功能。
-   自动路由注册：简化开发流程。
-   结果模式：使用 `Result` 对象封装操作结果和错误状态，提高错误处理的一致性。
-   结构化日志：使用标准化的日志格式，确保系统运行状态可追踪和诊断。

## 学习和项目洞察 (更新)

-   `auth` 模块的分层架构展示了如何有效组织复杂功能，将认证、用户管理和权限控制分离为不同服
    务类。
-   基于角色的访问控制（RBAC）模型提供了灵活且强大的权限管理方案，特别适合多租户系统。
-   缓存策略对认证性能至关重要，`auth` 模块实现了用户和角色数据的缓存，减少数据库查询。
-   Result 模式极大提高了错误处理的一致性，特别是在 `auth` 模块这样的关键组件中。
-   服务类方法的功能单一原则有助于提高可测试性，`RoleService` 和 `UserService` 的方法设计体
    现了这点。
-   完善的测试对安全关键型模块如 `auth` 尤为重要，单元测试应覆盖正常和异常路径。
-   FastAPI 结合 SQLAlchemy 2.0 异步提供了强大的性能基础。
-   依赖注入极大提升了代码的可测试性和模块化。
-   自定义事件总线提供了灵活性，但需要健壮的后台任务系统配合。
-   统一的服务基类 (`BaseService`) 和结果对象 (`Result`) 提高了代码一致性。
-   自动路由注册简化了模块添加，但也可能隐藏路由冲突，需注意测试。
-   项目结构清晰，易于扩展，但需要持续维护文档和测试以保持可维护性。
-   完善的类型注解和文档对大型项目的长期可维护性至关重要。
-   功能完善往往比功能增加更重要，确保核心功能稳定可靠是项目成功的基础。
-   审计日志对系统安全和合规性至关重要，完整的操作记录便于故障排查和安全审计。
-   种子数据脚本应该遵循与应用程序相同的数据一致性规则，确保测试和开发环境的数据质量与生产一
    致。
