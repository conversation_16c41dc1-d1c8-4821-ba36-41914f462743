# 技术上下文

## 技术栈

-   **后端技术**：Python 3.11+, FastAPI, Uvicorn, Gunicorn
-   **数据库技术**：PostgreSQL, SQLAlchemy 2.0 (异步), Alembic (数据库迁移)
-   **缓存技术**：Redis, 内存缓存 (由 BaseService 管理)
-   **安全认证**：JWT, Passlib, Bcrypt (svc/core/security)
-   **事件系统**：自定义事件总线 (svc/core/events/event_bus.py), Pyee
-   **监控工具**：Prometheus, Grafana (集成待确认)
-   **测试工具**：Pytest, Coverage, Pytest-asyncio
-   **部署工具**：Docker, Docker Compose, GitHub Actions (配置见 .github/workflows)
-   **数据验证**: Pydantic V2
-   **国际化**: 基于 Python 标准库 `gettext` (svc/core/i18n)

## 开发环境

-   **环境要求**：Python 3.11+, <PERSON>er, Docker Compose
-   **IDE 推荐**：VSCode (Cursor) + Python 扩展
-   **代码质量工具**：Ruff, My<PERSON> (配置见 pyproject.toml)
-   **版本控制**：Git, GitHub
-   **环境配置**：使用 .env 文件管理 (加载逻辑在 svc/core/config/settings.py)
    -   支持 .env, .env.<environment>, .env.local
-   **虚拟环境**：推荐使用 Python venv 或 uv
-   **快速启动**: `make run-local` 或 `make run-container`

## 技术约束

-   服务需要支持异步编程，所有 I/O 操作必须使用异步实现 (async/await)
-   数据库操作需使用 SQLAlchemy 2.0 的异步 API (svc/core/database/session.py)
-   所有函数必须包含类型注解 (mypy 强制)
-   错误处理必须统一使用框架提供的异常处理机制 (svc/core/exceptions)
-   配置管理必须通过 Settings 类进行 (svc/core/config/settings.py)，支持环境变量覆盖
-   所有 API 端点必须有完整的 OpenAPI 文档 (FastAPI 自动生成)

## 依赖项 (主要)

-   **fastapi**: Web 框架
-   **pydantic**: 数据验证和设置管理
-   **sqlalchemy**: ORM 和数据库操作 (异步)
-   **asyncpg**: PostgreSQL 异步驱动
-   **alembic**: 数据库迁移
-   **python-jose**: JWT 实现
-   **bcrypt**: 密码哈希
-   **redis**: Redis 客户端 (异步)
-   **prometheus-client**: 监控指标
-   **pyee**: 事件处理
-   **arq**: 异步队列
-   **pytest**: 测试框架
-   **uv**: Python 包管理 (可选, 用于快速依赖管理)
-   **ruff**: Linter 和 Formatter
-   **mypy**: 静态类型检查 (完整列表见 `pyproject.toml` 或 `requirements.txt`)

## 工具使用模式

-   **CI/CD 流程**：GitHub Actions 用于自动化测试、构建和部署
-   **代码质量检查**：提交前运行 Ruff 和 Mypy (可能通过 pre-commit hook)
-   **类型检查**：使用 Mypy 进行静态类型分析
-   **测试策略**：分层测试，单元测试专注于业务逻辑，集成测试验证 API 行为 (目录:
    `svc/tests`)
-   **文档生成**：使用 FastAPI 的自动文档功能 (Swagger UI `/docs`, ReDoc `/redoc`)
-   **依赖管理**：推荐使用 `uv sync` 或 `pip-tools`
-   **监控策略**：通过 Prometheus 收集指标，Grafana 展示可视化报表 (需要配置运行)

## 第三方服务

-   **PostgreSQL**：主数据库存储
-   **Redis**：缓存和分布式锁
-   **Prometheus**：指标收集 (可选运行)
-   **Grafana**：监控可视化 (可选运行)
-   **Docker Hub**：容器镜像存储 (可选)

## 安全考量

-   使用 HTTPS 确保传输安全 (需要部署时配置)
-   所有密码使用 BCrypt 加密存储 (svc/core/security/password.py)
-   API 访问需要基于角色的权限控制 (svc/apps/auth)
-   实现请求限流防止 DoS 攻击 (svc/core/middleware/rate_limit.py, 待启用配置)
-   敏感配置使用环境变量，避免硬编码 (通过 .env 文件)
