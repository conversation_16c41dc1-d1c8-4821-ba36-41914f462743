#!/usr/bin/env python3
"""
依赖包更新工具

该脚本用于根据依赖检查报告自动更新依赖包。
它会读取由check_dependencies.py生成的dependencies_report.md文件，
解析其中的更新建议，并根据优先级执行依赖包更新。

使用方法:
    python scripts/update_dependencies.py [--priority high|medium|low|all] [--report path/to/report.md]

参数:
    --priority: 更新的优先级，可以是high、medium、low或all，默认为high
    --report: 依赖报告的路径，默认为scripts/dependencies_report.md
    --dry-run: 仅打印将要执行的命令，不实际执行
    --skip-backup: 跳过备份requirements.txt文件
"""

import os
import sys
import re
import argparse
import subprocess
from typing import List, Dict, Tuple

def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="依赖包更新工具")
    parser.add_argument(
        "--priority", 
        choices=["high", "medium", "low", "all"],
        default="high",
        help="更新的优先级，可以是high、medium、low或all，默认为high"
    )
    parser.add_argument(
        "--report", 
        default=None,
        help="依赖报告的路径，默认为scripts/dependencies_report.md"
    )
    parser.add_argument(
        "--dry-run", 
        action="store_true",
        help="仅打印将要执行的命令，不实际执行"
    )
    parser.add_argument(
        "--skip-backup", 
        action="store_true",
        help="跳过备份requirements.txt文件"
    )
    return parser.parse_args()

def parse_report(report_path: str) -> Dict[str, List[str]]:
    """
    解析依赖报告文件，提取不同优先级的更新命令
    
    Args:
        report_path: 报告文件路径
        
    Returns:
        Dict[str, List[str]]: 不同优先级的更新命令字典
    """
    if not os.path.exists(report_path):
        print(f"错误：报告文件 {report_path} 不存在")
        sys.exit(1)
        
    # 提取不同优先级的命令
    commands = {
        "high": [],
        "medium": [],
        "low": []
    }
    
    with open(report_path, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 提取安全漏洞更新命令（归类为高优先级）
    vulnerability_section = re.search(r'### 安全漏洞更新\n```bash\n(.*?)```', content, re.DOTALL)
    if vulnerability_section:
        commands["high"].append(vulnerability_section.group(1).strip())
        
    # 提取高优先级更新命令
    high_section = re.search(r'### 高优先级更新\n```bash\n(.*?)```', content, re.DOTALL)
    if high_section:
        commands["high"].append(high_section.group(1).strip())
        
    # 提取中优先级更新命令
    medium_section = re.search(r'### 中优先级更新\n```bash\n(.*?)```', content, re.DOTALL)
    if medium_section:
        commands["medium"].append(medium_section.group(1).strip())
    
    # 提取低优先级更新命令
    low_section = re.search(r'### 低优先级更新\n```bash\n(.*?)```', content, re.DOTALL)
    if low_section:
        commands["low"].append(low_section.group(1).strip())
    
    return commands

def execute_update(commands: Dict[str, List[str]], priority: str, dry_run: bool) -> None:
    """
    执行依赖更新命令
    
    Args:
        commands: 更新命令字典
        priority: 更新优先级
        dry_run: 是否仅打印命令而不执行
    """
    # 确定要运行的命令列表
    cmds_to_run = []
    
    if priority == "high" or priority == "all":
        cmds_to_run.extend(commands["high"])
        
    if priority == "medium" or priority == "all":
        cmds_to_run.extend(commands["medium"])
    
    if priority == "low" or priority == "all":
        cmds_to_run.extend(commands["low"])
    
    # 过滤空命令
    cmds_to_run = [cmd for cmd in cmds_to_run if cmd]
    
    # 如果没有找到命令
    if not cmds_to_run:
        print(f"没有{priority}优先级的依赖需要更新。")
        return
    
    # 执行命令
    for i, cmd in enumerate(cmds_to_run):
        print(f"\n正在执行更新命令 {i+1}/{len(cmds_to_run)}:")
        print(f"$ {cmd}")
        
        if dry_run:
            print("（模拟运行，未实际执行）")
            continue
            
        try:
            # 使用shell=True是必要的，因为命令可能包含管道或重定向
            result = subprocess.run(cmd, shell=True, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            print(f"命令成功执行，退出码: {result.returncode}")
            
            # 输出执行结果的摘要
            if result.stdout:
                output_lines = result.stdout.splitlines()
                if len(output_lines) > 10:
                    print("输出摘要 (前5行和后5行):")
                    for line in output_lines[:5]:
                        print(f"  {line}")
                    print("  ...")
                    for line in output_lines[-5:]:
                        print(f"  {line}")
                else:
                    print("输出:")
                    for line in output_lines:
                        print(f"  {line}")
                        
            if result.stderr:
                print("警告/错误:")
                for line in result.stderr.splitlines():
                    print(f"  {line}")
        except subprocess.CalledProcessError as e:
            print(f"错误：命令执行失败，退出码: {e.returncode}")
            if e.stderr:
                print("错误输出:")
                print(e.stderr)
            if input("是否继续执行剩余命令？(y/n): ").lower() != 'y':
                break

def update_requirements(report_path: str, dry_run: bool, skip_backup: bool = False) -> None:
    """
    更新requirements.txt文件
    
    Args:
        report_path: 报告文件路径
        dry_run: 是否仅打印命令而不执行
        skip_backup: 是否跳过备份
    """
    with open(report_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取更新后的requirements.txt内容
    updated_req_section = re.search(r'## 更新后的requirements\.txt\n```\n(.*?)```', content, re.DOTALL)
    if not updated_req_section:
        print("未找到更新后的requirements.txt内容")
        return
        
    updated_content = updated_req_section.group(1)
    
    # 获取项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    requirements_path = os.path.join(project_root, "requirements.txt")
    
    # 确保requirements.txt存在
    if not os.path.exists(requirements_path):
        print(f"错误：找不到requirements.txt文件: {requirements_path}")
        return
    
    # 备份原始文件
    backup_path = requirements_path + ".bak"
    
    if dry_run:
        print("\n将更新requirements.txt文件（模拟运行，未实际执行）")
        if not skip_backup:
            print(f"原始文件将备份为: {backup_path}")
        return
    
    # 创建备份
    if not skip_backup:
        try:
            with open(requirements_path, 'r', encoding='utf-8') as src:
                with open(backup_path, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            print(f"原始requirements.txt已备份到 {backup_path}")
        except Exception as e:
            print(f"错误：备份requirements.txt失败: {str(e)}")
            return
    
    # 比较并显示差异
    with open(requirements_path, 'r', encoding='utf-8') as src:
        current_content = src.read()
    
    # 检查是否有实际变更
    if current_content.strip() == updated_content.strip():
        print("requirements.txt文件没有变化，跳过更新")
        return
    
    # 显示变更差异
    print("\n变更摘要:")
    current_lines = current_content.splitlines()
    updated_lines = updated_content.splitlines()
    
    # 简单的差异比较
    for line in updated_lines:
        if line.strip() and not any(line.strip() == old_line.strip() for old_line in current_lines):
            if '==' in line:
                print(f"  + {line}")
    
    # 写入更新后的内容
    try:
        with open(requirements_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        print(f"requirements.txt已更新")
    except Exception as e:
        print(f"错误：更新requirements.txt失败: {str(e)}")
        # 尝试恢复备份
        if not skip_backup:
            try:
                with open(backup_path, 'r', encoding='utf-8') as src:
                    with open(requirements_path, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                print("已恢复原始requirements.txt")
            except:
                print("警告：无法恢复原始requirements.txt")

def main():
    """主函数"""
    args = parse_args()
    
    # 确定报告文件路径
    if args.report:
        report_path = args.report
    else:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        report_path = os.path.join(script_dir, "dependencies_report.md")
    
    print(f"依赖更新工具\n")
    print(f"报告文件: {report_path}")
    print(f"更新优先级: {args.priority}")
    
    if args.dry_run:
        print("模式: 模拟运行（不会实际执行更新命令）")
    
    if args.skip_backup:
        print("注意: 将跳过备份requirements.txt文件")
    
    # 解析报告文件
    commands = parse_report(report_path)
    
    # 执行更新
    execute_update(commands, args.priority, args.dry_run)
    
    # 更新requirements.txt
    update_requirements(report_path, args.dry_run, args.skip_backup)
    
    print("\n依赖更新完成！")
    
    if not args.dry_run:
        print("\n建议运行测试以确保更新没有引入兼容性问题:")
        print("$ pytest")
        
        if args.priority in ["high", "medium"]:
            remaining = []
            if args.priority == "high" and (commands["medium"] or commands["low"]):
                remaining.append("中优先级" if commands["medium"] else "")
                remaining.append("低优先级" if commands["low"] else "")
                remaining = [r for r in remaining if r]
            elif args.priority == "medium" and commands["low"]:
                remaining.append("低优先级")
            
            if remaining:
                print(f"\n提示: 还有{' 和 '.join(remaining)}的依赖未更新，可以使用以下命令更新:")
                if "中优先级" in remaining and "低优先级" not in remaining:
                    print("$ python scripts/update_dependencies.py --priority medium")
                elif "中优先级" not in remaining and "低优先级" in remaining:
                    print("$ python scripts/update_dependencies.py --priority low")
                else:
                    print("$ python scripts/update_dependencies.py --priority all")

if __name__ == "__main__":
    main() 