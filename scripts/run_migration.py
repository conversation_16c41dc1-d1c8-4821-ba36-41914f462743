"""
执行UUID到BigInteger的数据库迁移脚本。
"""

import asyncio
import logging
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from svc.core.config.settings import get_settings
from svc.core.database.migrations.uuid_to_bigint import migrate_database, rollback_migration

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_migration():
    """执行数据库迁移"""
    settings = get_settings()
    engine = create_async_engine(settings.db_uri)
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )

    try:
        async with async_session() as session:
            logger.info("开始执行数据库迁移...")
            await migrate_database(session)
            logger.info("数据库迁移完成！")
    except Exception as e:
        logger.error(f"迁移失败: {str(e)}")
        logger.info("开始回滚...")
        async with async_session() as session:
            await rollback_migration(session)
        logger.info("回滚完成")
        raise
    finally:
        await engine.dispose()

if __name__ == "__main__":
    asyncio.run(run_migration()) 