#!/bin/bash
# FastAPI Nano 预提交钩子脚本
# 将此文件放在 .git/hooks/pre-commit 并确保其具有执行权限

echo "执行 FastAPI Nano 预提交检查..."

# 获取项目根目录
ROOT_DIR=$(git rev-parse --show-toplevel)
cd "$ROOT_DIR" || exit 1

# 获取暂存区中修改的文件
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.py$')

if [ -z "$STAGED_FILES" ]; then
    echo "没有Python文件被修改，跳过安全检查"
else
    echo "检测到修改的Python文件，执行安全检查..."
    
    # 创建临时文件存储修改的文件列表
    TEMP_FILE=$(mktemp)
    echo "$STAGED_FILES" > "$TEMP_FILE"
    
    # 执行安全检查，仅检查修改的文件
    echo "执行安全检查..."
    python scripts/security_checker.py --path "$TEMP_FILE" --output "scripts/security_report_precommit.md"
    SEC_CHECK_EXIT=$?
    
    # 清理临时文件
    rm "$TEMP_FILE"
    
    # 如果安全检查失败，提示用户
    if [ $SEC_CHECK_EXIT -ne 0 ]; then
        echo "安全检查发现问题，请查看 scripts/security_report_precommit.md"
        echo "您仍然可以使用 'git commit --no-verify' 强制提交"
        exit 1
    fi
fi

# 检查依赖文件是否被修改
if git diff --cached --name-only | grep -q "requirements.txt"; then
    echo "检测到requirements.txt被修改，执行依赖安全检查..."
    
    # 执行依赖安全检查
    python scripts/check_dependencies.py --security-only
    DEP_CHECK_EXIT=$?
    
    # 如果依赖检查失败，提示用户
    if [ $DEP_CHECK_EXIT -ne 0 ]; then
        echo "依赖检查发现安全问题，请查看 scripts/dependencies_report.md"
        echo "您仍然可以使用 'git commit --no-verify' 强制提交"
        exit 1
    fi
fi

# 运行代码格式化工具（可选）
if command -v black &> /dev/null; then
    echo "运行代码格式化检查..."
    if [ -n "$STAGED_FILES" ]; then
        black --check $STAGED_FILES
        if [ $? -ne 0 ]; then
            echo "代码格式检查失败，请运行 'black' 格式化您的代码后重新提交"
            exit 1
        fi
    fi
fi

# 运行类型检查工具（可选）
if command -v mypy &> /dev/null; then
    echo "运行类型检查..."
    if [ -n "$STAGED_FILES" ]; then
        mypy $STAGED_FILES
        if [ $? -ne 0 ]; then
            echo "类型检查失败，请修复类型错误后重新提交"
            exit 1
        fi
    fi
fi

echo "预提交检查通过！"
exit 0 