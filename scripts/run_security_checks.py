#!/usr/bin/env python3
"""
安全和依赖检查启动脚本

该脚本整合了安全检查和依赖检查工具，提供简单的命令行界面让用户执行安全检查和依赖更新。

使用方法：
    python scripts/run_security_checks.py [选项]

选项：
    --all            执行所有检查（安全检查和依赖检查）
    --security       仅执行安全检查
    --dependencies   仅执行依赖检查
    --update         在检查后执行依赖更新（默认仅更新高优先级）
    --update-all     在检查后更新所有依赖
    --path PATH      指定要检查的路径（安全检查）
    --exclude DIRS   排除目录，逗号分隔（安全检查）
    --verbose        显示详细输出
    --help           显示帮助信息
"""

import os
import sys
import argparse
import subprocess
from typing import List, Optional

def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="安全和依赖检查工具")
    parser.add_argument(
        "--all",
        action="store_true",
        help="执行所有检查（安全检查和依赖检查）"
    )
    parser.add_argument(
        "--security",
        action="store_true",
        help="仅执行安全检查"
    )
    parser.add_argument(
        "--dependencies",
        action="store_true",
        help="仅执行依赖检查"
    )
    parser.add_argument(
        "--update",
        action="store_true",
        help="在检查后执行依赖更新（默认仅更新高优先级）"
    )
    parser.add_argument(
        "--update-all",
        action="store_true",
        help="在检查后更新所有依赖"
    )
    parser.add_argument(
        "--path",
        default=None,
        help="指定要检查的路径（安全检查）"
    )
    parser.add_argument(
        "--exclude",
        default=None,
        help="排除目录，逗号分隔（安全检查）"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细输出"
    )
    
    args = parser.parse_args()
    
    # 如果没有指定检查类型，则默认执行所有检查
    if not (args.all or args.security or args.dependencies):
        args.all = True
        
    return args

def run_security_check(path: Optional[str] = None, exclude: Optional[str] = None, verbose: bool = False) -> int:
    """执行安全检查"""
    print("\n" + "=" * 70)
    print("                     开始执行安全检查")
    print("=" * 70)
    
    # 构建命令
    script_dir = os.path.dirname(os.path.abspath(__file__))
    command = [os.path.join(script_dir, "security_checker.py")]
    
    if path:
        command.extend(["--path", path])
    
    if exclude:
        command.extend(["--exclude", exclude])
        
    if verbose:
        command.append("--verbose")
    
    # 执行安全检查
    try:
        result = subprocess.run([sys.executable] + command, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"安全检查执行失败，退出码: {e.returncode}")
        return e.returncode

def run_dependency_check(verbose: bool = False, security_only: bool = False) -> int:
    """执行依赖检查"""
    print("\n" + "=" * 70)
    print("                     开始执行依赖检查")
    print("=" * 70)
    
    # 构建命令
    script_dir = os.path.dirname(os.path.abspath(__file__))
    command = [os.path.join(script_dir, "check_dependencies.py")]
    
    if verbose:
        command.append("--verbose")
        
    if security_only:
        command.append("--security-only")
    
    # 执行依赖检查
    try:
        result = subprocess.run([sys.executable] + command, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"依赖检查执行失败，退出码: {e.returncode}")
        return e.returncode

def run_dependency_update(update_all: bool = False, verbose: bool = False) -> int:
    """执行依赖更新"""
    print("\n" + "=" * 70)
    print("                     开始执行依赖更新")
    print("=" * 70)
    
    # 构建命令
    script_dir = os.path.dirname(os.path.abspath(__file__))
    command = [os.path.join(script_dir, "update_dependencies.py")]
    
    if update_all:
        command.extend(["--priority", "all"])
        
    if verbose:
        print(f"执行命令: {' '.join(command)}")
    
    # 执行依赖更新
    try:
        result = subprocess.run([sys.executable] + command, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"依赖更新执行失败，退出码: {e.returncode}")
        return e.returncode

def main() -> int:
    """主函数"""
    args = parse_args()
    
    exit_code = 0
    
    # 打印启动信息
    print("\n安全和依赖检查工具")
    print("-" * 30)
    
    if args.verbose:
        print("运行模式:")
        if args.all:
            print("- 执行所有检查")
        if args.security:
            print("- 执行安全检查")
        if args.dependencies:
            print("- 执行依赖检查")
        if args.update:
            print("- 执行依赖更新（高优先级）")
        if args.update_all:
            print("- 执行依赖更新（所有）")
    
    # 执行安全检查
    if args.all or args.security:
        security_result = run_security_check(args.path, args.exclude, args.verbose)
        if security_result != 0:
            exit_code = security_result
            
        # 打印安全检查结果位置
        script_dir = os.path.dirname(os.path.abspath(__file__))
        report_path = os.path.join(script_dir, "security_report.md")
        if os.path.exists(report_path):
            print(f"\n安全检查报告已保存至: {report_path}")
    
    # 执行依赖检查
    if args.all or args.dependencies:
        dependency_result = run_dependency_check(args.verbose)
        if dependency_result != 0 and exit_code == 0:
            exit_code = dependency_result
            
        # 打印依赖检查结果位置
        script_dir = os.path.dirname(os.path.abspath(__file__))
        report_path = os.path.join(script_dir, "dependencies_report.md")
        if os.path.exists(report_path):
            print(f"\n依赖检查报告已保存至: {report_path}")
    
    # 执行依赖更新
    if args.update or args.update_all:
        update_result = run_dependency_update(args.update_all, args.verbose)
        if update_result != 0 and exit_code == 0:
            exit_code = update_result
    
    # 打印总结
    print("\n" + "=" * 70)
    print("                      检查总结")
    print("=" * 70)
    
    if exit_code == 0:
        print("所有检查和更新操作已成功完成！")
    else:
        print(f"检查过程中出现错误，退出码: {exit_code}")
    
    print("\n执行后续操作建议:")
    if args.all or args.security:
        print("1. 查看安全检查报告，修复发现的安全问题")
    if args.all or args.dependencies:
        print("2. 查看依赖检查报告，更新过时的依赖")
    
    if not (args.update or args.update_all) and (args.all or args.dependencies):
        print("\n如需更新依赖，可以运行:")
        print("- 仅更新高优先级依赖: python scripts/run_security_checks.py --update")
        print("- 更新所有依赖: python scripts/run_security_checks.py --update-all")
    
    print("\n通过以下命令查看具体工具的使用帮助:")
    print("- python scripts/security_checker.py --help")
    print("- python scripts/check_dependencies.py --help")
    print("- python scripts/update_dependencies.py --help")
    
    return exit_code

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1) 