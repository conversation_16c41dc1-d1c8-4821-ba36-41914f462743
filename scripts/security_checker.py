#!/usr/bin/env python3
"""
安全检查工具

该脚本用于检查代码库中的安全问题，如硬编码密钥、不安全的加密实践、明文密码等。
它将搜索项目文件，分析代码模式，并报告潜在的安全风险。

使用方法:
    python scripts/security_checker.py [--path /path/to/check] [--exclude dir1,dir2]

参数:
    --path: 要检查的路径，默认为项目根目录
    --exclude: 要排除的目录，逗号分隔，默认为venv,__pycache__,.git,.pytest_cache
    --verbose: 详细输出模式
    --output: 输出文件路径，默认为"security_report.md"
"""

import os
import sys
import re
import argparse
import json
from typing import List, Dict, Any, Tuple, Set
from datetime import datetime
import fnmatch
from pathlib import Path

# 安全检查规则
SECURITY_RULES = [
    {
        "id": "SEC001",
        "name": "硬编码密钥",
        "severity": "高",
        "description": "检测到硬编码的API密钥、认证凭证或密码",
        "patterns": [
            r'(?i)api[_-]?key\s*=\s*[\'\"][a-zA-Z0-9]{32,}[\'\"]',
            r'(?i)secret[_-]?key\s*=\s*[\'\"][a-zA-Z0-9]{16,}[\'\"]',
            r'(?i)password\s*=\s*[\'\"][^\'\"]{8,}[\'\"]',
            r'(?i)passwd\s*=\s*[\'\"][^\'\"]{8,}[\'\"]',
            r'(?i)AUTH_TOKEN\s*=\s*[\'\"][a-zA-Z0-9_\-\.=]{8,}[\'\"]',
            r'(?i)access[_-]?key\s*=\s*[\'\"][A-Za-z0-9+/]{8,}[\'\"]',
            r'(?i)client[_-]?secret\s*=\s*[\'\"][A-Za-z0-9_\-\.=]{8,}[\'\"]',
            r'(?i)jwt[_-]?secret\s*=\s*[\'\"][^\'\"]{8,}[\'\"]'
        ],
        "exclude_patterns": [
            r'(?i)password\s*=\s*[\'\"].*\$\{.*\}.*[\'\"]',  # 排除环境变量引用
            r'(?i)password\s*=\s*os\.environ\.get',  # 排除环境变量获取
            r'(?i)password\s*=\s*settings\.',  # 排除从配置获取
            r'(?i)password\s*=\s*[\'\"]{{.*}}[\'\"]',  # 排除模板变量
            r'(?i)example|sample|test|dummy'  # 排除示例代码
        ],
        "file_patterns": ["*.py", "*.json", "*.yml", "*.yaml", "*.env", "*.ini", "*.cfg"]
    },
    {
        "id": "SEC002",
        "name": "不安全的哈希算法",
        "severity": "中",
        "description": "使用不安全的哈希算法（MD5、SHA1）用于密码或安全凭证",
        "patterns": [
            r'hashlib\.md5\(',
            r'hashlib\.sha1\(',
            r'import md5',
            r'(?i)md5\s*\(',
            r'from hashlib import md5',
            r'from hashlib import sha1',
            r'hashlib\.algorithms_guaranteed\.md5',
            r'hashlib\.algorithms_guaranteed\.sha1'
        ],
        "exclude_patterns": [
            r'(?i)file|checksum|content|download|video|audio|image|media|fingerprint'
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC003",
        "name": "SQL注入风险",
        "severity": "高",
        "description": "检测到潜在的SQL注入风险",
        "patterns": [
            r'f\"SELECT.*FROM.*WHERE.*{',
            r'f\'SELECT.*FROM.*WHERE.*{',
            r'\"\s*\+.*\+\s*\".*SELECT',
            r'\"\s*%\s*\(.*\).*SELECT',
            r'text\(\".*SELECT',
            r'text\(f\".*SELECT',
            r'execute\(\s*f\"',
            r'execute\(\s*\".*%\s*\(',
            r'execute\(\s*\".*\+',
            r'executemany\(\s*f\"',
            r'executemany\(\s*\".*%\s*\(',
            r'raw_connection\(\s*f\"',
            r'raw\(\s*f\"',
            r'f\"INSERT INTO.*VALUES.*{',
            r'f\"UPDATE.*SET.*WHERE.*{',
            r'f\"DELETE FROM.*WHERE.*{',
            r'cursor\.execute\(f\"',
            r'connection\.execute\(f\"',
            r'engine\.execute\(f\"',
            r'session\.execute\(f\"',
            r'session\.exec\(f\"',
            r'db\.execute\(f\"'
        ],
        "exclude_patterns": [
            r'[\'\"]WHERE\s+id\s*=\s*\{\s*\}\s*[\'\"]\.format\(\s*id\s*\)',
            r'await\s+db\.execute\(\s*select\(',
            r'await\s+db\.execute\(\s*insert\(',
            r'await\s+db\.execute\(\s*update\(',
            r'sqlalchemy\.text\('
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC004",
        "name": "未验证的重定向",
        "severity": "中",
        "description": "检测到未验证的URL重定向",
        "patterns": [
            r'redirect\(\s*request\.',
            r'redirect\(\s*.*\+\s*request\.',
            r'redirect\(\s*.*\[\s*[\'\"].*[\'\"]',
            r'RedirectResponse\(\s*url=request\.'
        ],
        "exclude_patterns": [
            r'validate_redirect_url',
            r'is_safe_redirect',
            r'is_safe_url',
            r'allowed_hosts',
            r'check_url_allowed'
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC005",
        "name": "路径遍历漏洞",
        "severity": "高",
        "description": "检测到潜在的路径遍历漏洞",
        "patterns": [
            r'open\(\s*.*\+',
            r'open\(\s*f[\'"]',
            r'open\(\s*request\.',
            r'open\(\s*os\.path\.join\(\s*.*,\s*.*request\.',
            r'os\.path\.join\(\s*.*,\s*.*request\.',
            r'with\s+open\(\s*f[\'"]',
            r'with\s+open\(\s*.*\+',
            r'with\s+open\(\s*request\.',
            r'file_path\s*=\s*.*request\.'
        ],
        "exclude_patterns": [
            r'\.replace\([\'\"]\.\.[\'\"]',
            r'\.replace\([\'\"]\/\.\.[\'\"]',
            r'pathlib\.Path\(',
            r'os\.path\.abspath\(',
            r'os\.path\.normpath\(',
            r'os\.path\.realpath\(',
            r'validate_filepath'
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC006",
        "name": "XML解析漏洞",
        "severity": "高",
        "description": "检测到不安全的XML解析，可能导致XXE攻击",
        "patterns": [
            r'xml\.dom\.minidom\.parseString\(',
            r'ElementTree\.parse\(',
            r'untrusted_xml',
            r'lxml\.etree\.parse\(',
            r'lxml\.etree\.fromstring\(',
            r'xml\.etree\.ElementTree\.parse\(',
            r'xml\.etree\.ElementTree\.fromstring\(',
            r'xmlrpclib\.loads',
            r'xmlrpc\.client\.loads'
        ],
        "exclude_patterns": [
            r'resolve_entities\s*=\s*False',
            r'disallow_dtd\s*=\s*True',
            r'no_network\s*=\s*True',
            r'XMLParser\(.*resolve_entities\s*=\s*False'
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC007",
        "name": "命令注入风险",
        "severity": "高",
        "description": "检测到可能的命令注入漏洞",
        "patterns": [
            r'os\.system\(\s*.*\+',
            r'os\.system\(\s*f[\'"]',
            r'os\.system\(\s*.*request\.',
            r'os\.popen\(\s*.*\+',
            r'os\.popen\(\s*f[\'"]',
            r'subprocess\.Popen\(\s*.*\+',
            r'subprocess\.Popen\(\s*f[\'"]',
            r'subprocess\.Popen\(\s*.*request\.',
            r'subprocess\.call\(\s*.*\+',
            r'subprocess\.call\(\s*f[\'"]',
            r'subprocess\.run\(\s*.*\+',
            r'subprocess\.run\(\s*f[\'"]',
            r'subprocess\.run\(\s*.*request\.',
            r'subprocess\.check_output\(\s*.*\+',
            r'subprocess\.check_output\(\s*f[\'"]',
            r'eval\(\s*.*request\.',
            r'exec\(\s*.*request\.',
            r'shell\s*=\s*True'
        ],
        "exclude_patterns": [
            r'shlex\.quote\(',
            r'shlex\.split\(',
            r'subprocess\.run\(\s*\[\s*',
            r'subprocess\.Popen\(\s*\[\s*',
            r'validate_command',
            r'escape_shell'
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC008",
        "name": "不安全的反序列化",
        "severity": "高",
        "description": "检测到不安全的反序列化操作",
        "patterns": [
            r'pickle\.loads\(',
            r'pickle\.load\(',
            r'cPickle\.loads\(',
            r'cPickle\.load\(',
            r'yaml\.load\(.*Loader=None',
            r'yaml\.load\([^,]',
            r'eval\(',
            r'marshal\.loads\(',
            r'shelve\.open\(',
            r'dill\.loads\(',
            r'jsonpickle\.decode\('
        ],
        "exclude_patterns": [
            r'yaml\.load\(.*Loader=yaml\.SafeLoader',
            r'yaml\.safe_load\(',
            r'json\.loads\('
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC009",
        "name": "跨站脚本(XSS)风险",
        "severity": "中",
        "description": "检测到可能的XSS漏洞",
        "patterns": [
            r'\.html\(\s*request\..*\)',
            r'\.html\(\s*.*user.*\)',
            r'\.html\(\s*.*input.*\)',
            r'innerHTML\s*=',
            r'document\.write\(',
            r'\.html\(\s*.*untrusted.*\)',
            r'dangerouslySetInnerHTML',
            r'jinja2\.Template\(\s*.*request\.',
            r'mark_safe\(\s*.*request\.',
            r'\.format\(\s*.*request\.'
        ],
        "exclude_patterns": [
            r'escape\(',
            r'html\.escape\(',
            r'cgi\.escape\(',
            r'bleach\.clean\(',
            r'sanitize\('
        ],
        "file_patterns": ["*.py", "*.js", "*.jsx", "*.ts", "*.tsx", "*.html"]
    },
    {
        "id": "SEC010",
        "name": "敏感信息日志记录",
        "severity": "中",
        "description": "检测到可能在日志中记录敏感信息",
        "patterns": [
            r'log.*\(\s*.*password',
            r'log.*\(\s*.*secret',
            r'log.*\(\s*.*token',
            r'log.*\(\s*.*credential',
            r'log.*\(\s*f[\'"].*{.*password',
            r'log.*\(\s*f[\'"].*{.*secret',
            r'log.*\(\s*f[\'"].*{.*token',
            r'print\(\s*.*password',
            r'print\(\s*.*secret',
            r'print\(\s*.*token',
            r'logger\.(debug|info|warning|error|critical)\(\s*.*password',
            r'logger\.(debug|info|warning|error|critical)\(\s*.*token',
            r'logger\.(debug|info|warning|error|critical)\(\s*.*secret'
        ],
        "exclude_patterns": [
            r'mask_password',
            r'mask_secret',
            r'password\s*=\s*[\'\"]<redacted>[\'\"]',
            r'password\s*=\s*[\'\"]******[\'\"]',
            r'secret\s*=\s*[\'\"]<redacted>[\'\"]',
            r'masked_password',
            r'filter_sensitive'
        ],
        "file_patterns": ["*.py"]
    },
    {
        "id": "SEC011",
        "name": "调试配置泄露",
        "severity": "中",
        "description": "检测到可能在生产环境中启用的调试配置",
        "patterns": [
            r'DEBUG\s*=\s*True',
            r'debug\s*=\s*True',
            r'debug=True',
            r'app\.debug\s*=\s*True',
            r'DEBUG_PROPAGATE_EXCEPTIONS\s*=\s*True',
            r'environ\[[\'\"]FLASK_DEBUG[\'\"]]\s*=\s*[\'\"]1[\'\"]',
            r'environ\[[\'\"]DJANGO_DEBUG[\'\"]]\s*=\s*[\'\"]1[\'\"]',
            r'config\[[\'\"]DEBUG[\'\"]]\s*=\s*True'
        ],
        "exclude_patterns": [
            r'if[\s\(]+.*development.*:',
            r'if[\s\(]+.*not\s+production.*:',
            r'if[\s\(]+debug[\s\)]+:',
            r'if[\s\(]+settings.DEBUG',
            r'if[\s\(]+os\.environ\.get\([\'\"]ENVIRONMENT[\'\"]'
        ],
        "file_patterns": ["*.py", "*.cfg", "*.ini", "*.json", "*.yml", "*.yaml", "*.env"]
    }
]

def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="代码安全检查工具")
    parser.add_argument(
        "--path", 
        default=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        help="要检查的路径，默认为项目根目录"
    )
    parser.add_argument(
        "--exclude", 
        default="venv,__pycache__,.git,.pytest_cache,node_modules,.venv,build,dist,*.egg-info",
        help="要排除的目录或文件模式，逗号分隔"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="打印详细信息"
    )
    parser.add_argument(
        "--output", 
        default="security_report.md",
        help="输出文件路径"
    )
    return parser.parse_args()

def should_exclude(file_path: str, exclude_patterns: List[str]) -> bool:
    """检查文件是否应该被排除"""
    for pattern in exclude_patterns:
        if fnmatch.fnmatch(file_path, pattern) or fnmatch.fnmatch(os.path.basename(file_path), pattern):
            return True
        
        # 检查路径的任何部分是否匹配
        path_parts = Path(file_path).parts
        for part in path_parts:
            if fnmatch.fnmatch(part, pattern):
                return True
                
    return False

def matches_file_pattern(file_path: str, patterns: List[str]) -> bool:
    """检查文件是否匹配指定的模式"""
    for pattern in patterns:
        if fnmatch.fnmatch(file_path, pattern):
            return True
    return False

def check_file(file_path: str, rules: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """检查单个文件的安全问题"""
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            lines = content.split('\n')
            
            for rule in rules:
                # 检查文件是否匹配规则的文件模式
                if not matches_file_pattern(os.path.basename(file_path), rule['file_patterns']):
                    continue
                    
                for pattern in rule['patterns']:
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    
                    for match in matches:
                        # 检查是否应该排除
                        should_skip = False
                        for exclude in rule['exclude_patterns']:
                            if re.search(exclude, content[max(0, match.start() - 50):match.end() + 50]):
                                should_skip = True
                                break
                                
                        if should_skip:
                            continue
                            
                        # 查找行号
                        line_no = content[:match.start()].count('\n') + 1
                        matched_text = match.group(0)
                        
                        # 获取上下文（前后各1行）
                        start_line = max(0, line_no - 2)
                        end_line = min(len(lines) - 1, line_no + 1)
                        context = '\n'.join(lines[start_line:end_line+1])
                        
                        issues.append({
                            'rule_id': rule['id'],
                            'rule_name': rule['name'],
                            'severity': rule['severity'],
                            'description': rule['description'],
                            'file': file_path,
                            'line': line_no,
                            'matched_text': matched_text,
                            'context': context
                        })
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}: {str(e)}")
    
    return issues

def scan_files(base_path: str, exclude_patterns: List[str], verbose: bool) -> List[Dict[str, Any]]:
    """扫描文件并检查安全问题"""
    all_issues = []
    file_count = 0
    
    for root, dirs, files in os.walk(base_path):
        # 修改dirs列表，排除目录
        dirs[:] = [d for d in dirs if not should_exclude(os.path.join(root, d), exclude_patterns)]
        
        for file in files:
            file_path = os.path.join(root, file)
            if should_exclude(file_path, exclude_patterns):
                continue
                
            file_count += 1
            if verbose:
                print(f"正在检查文件: {file_path}")
                
            # 检查文件安全问题
            issues = check_file(file_path, SECURITY_RULES)
            all_issues.extend(issues)
            
            if len(issues) > 0 and verbose:
                for issue in issues:
                    print(f"  发现问题: [{issue['rule_id']}] {issue['rule_name']} at line {issue['line']}")
    
    if verbose:
        print(f"\n总共检查了 {file_count} 个文件，发现 {len(all_issues)} 个安全问题。")
        
    return all_issues

def generate_report(issues: List[Dict[str, Any]]) -> str:
    """生成安全问题报告"""
    # 按严重程度排序
    severity_order = {"高": 0, "中": 1, "低": 2}
    issues = sorted(issues, key=lambda x: (severity_order.get(x['severity'], 99), x['rule_id'], x['file'], x['line']))
    
    # 分组问题
    issues_by_severity = {}
    for issue in issues:
        severity = issue['severity']
        if severity not in issues_by_severity:
            issues_by_severity[severity] = []
        issues_by_severity[severity].append(issue)
    
    # 生成报告
    report = [
        f"# 安全检查报告 ({datetime.now().strftime('%Y-%m-%d')})\n",
        f"## 摘要\n",
        f"- 总计发现 {len(issues)} 个安全问题",
    ]
    
    for severity in ["高", "中", "低"]:
        if severity in issues_by_severity:
            report.append(f"- {severity}级问题: {len(issues_by_severity[severity])} 个")
    
    report.append("\n## 详细问题\n")
    
    # 添加详细问题
    for severity in ["高", "中", "低"]:
        if severity in issues_by_severity:
            report.append(f"### {severity}级问题\n")
            
            for issue in issues_by_severity[severity]:
                report.append(f"#### [{issue['rule_id']}] {issue['rule_name']}")
                report.append(f"- **文件**: {issue['file']}")
                report.append(f"- **行号**: {issue['line']}")
                report.append(f"- **问题**: {issue['description']}")
                report.append(f"- **代码上下文**:")
                report.append("```")
                report.append(issue['context'])
                report.append("```")
                report.append("")
    
    report.append("## 修复建议\n")
    
    # 添加修复建议
    recommendations = {
        "SEC001": "硬编码密钥应移至环境变量或安全的配置管理系统，绝不应出现在代码中。",
        "SEC002": "对于密码或安全凭证，请使用强加密哈希算法（如bcrypt、Argon2、PBKDF2），而不是MD5或SHA1。",
        "SEC003": "使用参数化查询或ORM方法来防止SQL注入，避免直接拼接SQL语句。",
        "SEC004": "始终转义或净化用户输入，使用模板系统的自动转义功能或专用的XSS防护库。",
        "SEC005": "确保生产环境中禁用调试模式，使用配置系统区分开发和生产环境。",
        "SEC006": "使用安全的路径操作方法，如os.path.normpath()和os.path.abspath()，验证用户提供的路径。",
        "SEC007": "使用安全的反序列化方法，如json或yaml.safe_load()，避免pickle等可能执行代码的反序列化。",
        "SEC008": "始终验证JWT签名，并设置适当的过期时间和颁发者检查。",
        "SEC009": "实施强密码策略，要求最少8个字符，并包含大小写字母、数字和特殊字符。",
        "SEC010": "仅允许必要的源访问跨域资源，避免使用'*'通配符。在生产环境中特别注意CORS配置。",
        "SEC011": "确保生产环境中禁用调试模式，使用配置系统区分开发和生产环境。"
    }
    
    seen_rule_ids = set()
    for issue in issues:
        rule_id = issue['rule_id']
        if rule_id not in seen_rule_ids and rule_id in recommendations:
            report.append(f"### {issue['rule_name']}")
            report.append(recommendations[rule_id])
            report.append("")
            seen_rule_ids.add(rule_id)
    
    return "\n".join(report)

def main():
    """主函数"""
    args = parse_args()
    
    print(f"开始安全检查...\n路径: {args.path}")
    print(f"排除模式: {args.exclude}")
    
    exclude_patterns = args.exclude.split(',')
    issues = scan_files(args.path, exclude_patterns, args.verbose)
    
    # 生成报告
    report = generate_report(issues)
    
    # 将报告写入文件
    script_dir = os.path.dirname(os.path.abspath(__file__))
    output_path = os.path.join(script_dir, args.output)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n安全检查完成！")
    print(f"总共发现 {len(issues)} 个安全问题。")
    print(f"报告已保存至: {output_path}")

if __name__ == "__main__":
    main() 