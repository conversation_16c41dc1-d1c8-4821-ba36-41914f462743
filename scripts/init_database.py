#!/usr/bin/env python
"""
数据库初始化脚本
用于创建数据库表结构，不使用迁移
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

from svc.core.database.session import init_db

async def initialize_database():
    """初始化数据库，根据当前模型定义创建表"""
    print("开始初始化数据库...")
    await init_db(testing=False)
    print("数据库初始化完成！")

if __name__ == "__main__":
    asyncio.run(initialize_database()) 