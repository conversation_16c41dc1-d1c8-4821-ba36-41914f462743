#!/usr/bin/env python
"""
数据库重置脚本
用于重新初始化数据库，包括回滚所有迁移、重新应用所有迁移
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def run_command(command, check=True):
    """运行命令并打印输出"""
    print(f"执行命令: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result


def reset_database(recreate=False):
    """重置数据库
    
    Args:
        recreate: 是否重新创建数据库，如果为True，将删除并重新创建数据库；
                 如果为False，将只回滚并重新应用迁移
    """
    if recreate:
        # 获取数据库连接信息
        db_name = os.getenv("POSTGRES_DB", "fastapi_nano")
        db_user = os.getenv("POSTGRES_USER", "postgres")
        db_password = os.getenv("POSTGRES_PASSWORD", "postgres")
        db_server = os.getenv("POSTGRES_SERVER", "localhost")
        
        # 删除数据库
        print(f"删除数据库 {db_name}...")
        run_command(f"PGPASSWORD={db_password} dropdb -h {db_server} -U {db_user} {db_name}", check=False)
        
        # 创建数据库
        print(f"创建数据库 {db_name}...")
        run_command(f"PGPASSWORD={db_password} createdb -h {db_server} -U {db_user} {db_name}")
    else:
        # 回滚所有迁移
        print("回滚所有迁移...")
        run_command("alembic downgrade base")
    
    # 应用所有迁移
    # print("应用所有迁移...")
    # run_command("alembic upgrade head")
    
    print("数据库重置完成！")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="重置数据库")
    parser.add_argument("--recreate", action="store_true", help="是否重新创建数据库")
    args = parser.parse_args()
    
    reset_database(args.recreate) 