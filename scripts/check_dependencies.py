#!/usr/bin/env python3
"""
依赖包检查工具

该脚本用于检查项目依赖包的更新情况和安全漏洞。
它读取requirements.txt文件，检查每个依赖包的最新版本，
报告可能的安全漏洞，并提供更新建议。

使用方法:
    python scripts/check_dependencies.py

输出:
    依赖包的版本对比（当前版本与最新版本）
    潜在的安全漏洞警告
    更新建议和优先级
"""

import sys
import os
import re
import json
import subprocess
import argparse
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class DependencyInfo:
    """依赖包信息"""
    name: str
    current_version: str
    latest_version: str
    is_outdated: bool
    update_priority: str  # "high", "medium", "low"
    vulnerabilities: List[Dict[str, Any]]
    notes: str = ""

def parse_requirements(file_path: str) -> Dict[str, str]:
    """
    解析requirements.txt文件，返回依赖包及其版本
    
    Args:
        file_path: requirements.txt文件路径
        
    Returns:
        Dict[str, str]: 包名到版本的映射
    """
    dependencies = {}
    comment_section = None
    
    with open(file_path, 'r') as file:
        for line in file:
            line = line.strip()
            
            # 跳过空行
            if not line:
                continue
                
            # 处理注释行
            if line.startswith('#'):
                comment_section = line[1:].strip()
                continue
                
            # 处理依赖行
            if '==' in line:
                parts = line.split('==')
                package = parts[0].strip()
                version = parts[1].strip().split('#')[0].strip()  # 移除行内注释
                dependencies[package] = version
    
    return dependencies

def get_latest_version(package: str) -> str:
    """
    获取包的最新版本
    
    Args:
        package: 包名
        
    Returns:
        str: 最新版本号
    """
    try:
        result = subprocess.run(
            [sys.executable, '-m', 'pip', 'index', 'versions', package], 
            capture_output=True, 
            text=True, 
            check=True
        )
        output = result.stdout
        
        # 解析输出找到最新版本
        match = re.search(r'Available versions: (.*)', output)
        if match:
            versions = match.group(1).split(', ')
            if versions:
                return versions[0].strip()
    except subprocess.CalledProcessError:
        pass
    
    return "unknown"

def check_vulnerabilities(package: str, version: str) -> List[Dict[str, Any]]:
    """
    检查包的已知安全漏洞
    
    Args:
        package: 包名
        version: 版本号
        
    Returns:
        List[Dict]: 漏洞信息列表
    """
    # 这里应该使用安全漏洞数据库API，但为了简单起见，我们使用本地检查
    # 在实际应用中，可以使用safety或PyUp.io等服务
    
    # 一些已知有漏洞的包版本（示例）
    known_vulnerabilities = {
        "pyyaml<6.0": "CVE-2020-14343: 允许远程攻击者通过特制的YAML文件执行任意代码",
        "jinja2<3.1.0": "CVE-2022-34749: 模板注入漏洞",
        "sqlalchemy<1.4.36": "CVE-2022-22397: SQL注入漏洞",
        "fastapi<0.95.0": "CVE-2023-27320: 可能的请求伪造漏洞",
        "python-jose<3.3.0": "CVE-2020-1234: JWT验证漏洞",
        "aiohttp<3.8.4": "CVE-2023-24620: SSRF漏洞",
        "requests<2.31.0": "CVE-2023-32681: 可能的Cookie注入",
        "urllib3<1.26.17": "CVE-2023-45803: HTTP/2帧注入漏洞",
        "cryptography<41.0.2": "CVE-2023-38325: 不安全的随机数生成",
        "pillow<10.0.0": "CVE-2023-4863: 图像处理漏洞",
        "werkzeug<2.3.6": "CVE-2023-46136: 内存泄露和拒绝服务",
        "django<4.2.6": "CVE-2023-41164: 点击劫持保护绕过",
        "httpx<0.24.1": "CVE-2023-32681: HTTP请求走私漏洞",
        "pyjwt<2.6.0": "CVE-2022-29217: JWT错误验证漏洞",
        "tornado<6.3.3": "CVE-2023-28370: CRLF注入漏洞"
    }
    
    vulnerabilities = []
    for vuln_spec, description in known_vulnerabilities.items():
        # 解析漏洞规范
        match = re.match(r'^([a-zA-Z0-9_-]+)(<|<=|>|>=|==)([0-9.]+)$', vuln_spec)
        if not match:
            continue
            
        vuln_package, operator, vuln_version = match.groups()
        
        if vuln_package != package:
            continue
            
        # 版本比较
        from packaging import version as packaging_version
        pkg_version = packaging_version.parse(version)
        v_version = packaging_version.parse(vuln_version)
        
        is_vulnerable = False
        if operator == '<' and pkg_version < v_version:
            is_vulnerable = True
        elif operator == '<=' and pkg_version <= v_version:
            is_vulnerable = True
        elif operator == '>' and pkg_version > v_version:
            is_vulnerable = True
        elif operator == '>=' and pkg_version >= v_version:
            is_vulnerable = True
        elif operator == '==' and pkg_version == v_version:
            is_vulnerable = True
            
        if is_vulnerable:
            vulnerabilities.append({
                "id": description.split(':')[0],
                "description": description.split(':', 1)[1] if ':' in description else description,
                "affected_version": vuln_spec,
                "fixed_version": vuln_version
            })
    
    return vulnerabilities

def determine_update_priority(
    package: str, 
    current_version: str,
    latest_version: str,
    vulnerabilities: List[Dict[str, Any]]
) -> str:
    """
    确定包更新的优先级
    
    Args:
        package: 包名
        current_version: 当前版本
        latest_version: 最新版本
        vulnerabilities: 漏洞列表
        
    Returns:
        str: 优先级 ("high", "medium", "low")
    """
    # 有漏洞的包优先级高
    if vulnerabilities:
        return "high"
        
    # 核心依赖优先级较高
    core_packages = [
        "fastapi", "pydantic", "sqlalchemy", "alembic", 
        "uvicorn", "python-jose", "asyncpg", "redis",
        "starlette", "passlib", "aiohttp", "httpx"
    ]
    
    # 安全相关的包优先级较高
    security_packages = [
        "cryptography", "bcrypt", "python-jose", "passlib",
        "pyjwt", "itsdangerous", "oauthlib", "authlib"
    ]
    
    if package in security_packages:
        # 安全相关包的优先级默认较高
        from packaging import version as packaging_version
        current = packaging_version.parse(current_version)
        latest = packaging_version.parse(latest_version)
        
        # 有任何版本差异都优先级高
        if current.major != latest.major or current.minor != latest.minor:
            return "high"
        elif current.micro != latest.micro:
            return "medium"
    
    if package in core_packages:
        # 检查版本差异程度
        from packaging import version as packaging_version
        current = packaging_version.parse(current_version)
        latest = packaging_version.parse(latest_version)
        
        # 主版本不同，优先级高
        if current.major != latest.major:
            return "high"
        # 次版本相差超过1，优先级中
        elif abs(current.minor - latest.minor) >= 1:
            return "medium"
        # 补丁版本相差超过3，优先级中
        elif abs(current.micro - latest.micro) >= 3:
            return "medium"
    
    # 默认优先级低
    return "low"

def check_dependencies() -> List[DependencyInfo]:
    """
    检查所有依赖并返回信息
    
    Returns:
        List[DependencyInfo]: 依赖信息列表
    """
    # 设置项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    requirements_path = os.path.join(project_root, "requirements.txt")
    
    # 解析requirements.txt
    dependencies = parse_requirements(requirements_path)
    
    # 检查每个依赖
    dependency_info_list = []
    for package, current_version in dependencies.items():
        latest_version = get_latest_version(package)
        
        # 检查是否过时
        from packaging import version as packaging_version
        is_outdated = False
        try:
            if latest_version != "unknown":
                current = packaging_version.parse(current_version)
                latest = packaging_version.parse(latest_version)
                is_outdated = latest > current
        except:
            pass
            
        # 检查漏洞
        vulnerabilities = check_vulnerabilities(package, current_version)
        
        # 确定更新优先级
        priority = determine_update_priority(
            package, current_version, latest_version, vulnerabilities
        )
        
        # 添加特殊说明
        notes = ""
        if package == "pydantic" and is_outdated:
            notes = "Pydantic 2.x 有重大API变更，更新需谨慎并全面测试"
        elif package == "sqlalchemy" and is_outdated:
            notes = "SQLAlchemy 2.0与1.x版本有API差异，更新需要调整代码"
            
        info = DependencyInfo(
            name=package,
            current_version=current_version,
            latest_version=latest_version,
            is_outdated=is_outdated,
            update_priority=priority,
            vulnerabilities=vulnerabilities,
            notes=notes
        )
        dependency_info_list.append(info)
    
    # 按优先级排序
    priority_order = {"high": 0, "medium": 1, "low": 2}
    dependency_info_list.sort(
        key=lambda x: (
            priority_order.get(x.update_priority, 3), 
            x.name
        )
    )
    
    return dependency_info_list

def generate_update_recommendations(dependencies: List[DependencyInfo]) -> str:
    """
    生成更新建议
    
    Args:
        dependencies: 依赖信息列表
        
    Returns:
        str: 更新建议文本
    """
    outdated_high = [d for d in dependencies if d.is_outdated and d.update_priority == "high"]
    outdated_medium = [d for d in dependencies if d.is_outdated and d.update_priority == "medium"]
    outdated_low = [d for d in dependencies if d.is_outdated and d.update_priority == "low"]
    
    vulnerable = [d for d in dependencies if d.vulnerabilities]
    
    recommendations = [
        f"# 依赖更新建议 ({datetime.now().strftime('%Y-%m-%d')})\n"
    ]
    
    if vulnerable:
        recommendations.append("## 安全漏洞 - 需立即更新")
        for dep in vulnerable:
            vuln_info = "\n".join([f"   - {v['id']}: {v['description']}" for v in dep.vulnerabilities])
            recommendations.append(
                f"* {dep.name}: {dep.current_version} -> {dep.latest_version}\n"
                f"  漏洞信息:\n{vuln_info}"
            )
        recommendations.append("")
    
    if outdated_high:
        recommendations.append("## 高优先级更新")
        for dep in outdated_high:
            recommendation = f"* {dep.name}: {dep.current_version} -> {dep.latest_version}"
            if dep.notes:
                recommendation += f"\n  注意: {dep.notes}"
            recommendations.append(recommendation)
        recommendations.append("")
    
    if outdated_medium:
        recommendations.append("## 中优先级更新")
        for dep in outdated_medium:
            recommendation = f"* {dep.name}: {dep.current_version} -> {dep.latest_version}"
            if dep.notes:
                recommendation += f"\n  注意: {dep.notes}"
            recommendations.append(recommendation)
        recommendations.append("")
    
    if outdated_low:
        recommendations.append("## 低优先级更新")
        for dep in outdated_low:
            recommendation = f"* {dep.name}: {dep.current_version} -> {dep.latest_version}"
            if dep.notes:
                recommendation += f"\n  注意: {dep.notes}"
            recommendations.append(recommendation)
        recommendations.append("")
    
    if not any([outdated_high, outdated_medium, outdated_low, vulnerable]):
        recommendations.append("所有依赖包均为最新，无需更新。")
    
    # 提供更新命令
    recommendations.append("## 更新命令")
    
    if vulnerable:
        vulnerable_packages = " ".join([f"{d.name}=={d.latest_version}" for d in vulnerable])
        recommendations.append(f"### 安全漏洞更新")
        recommendations.append(f"```bash")
        recommendations.append(f"pip install -U {vulnerable_packages}")
        recommendations.append(f"```\n")
    
    if outdated_high:
        high_priority_packages = " ".join([f"{d.name}=={d.latest_version}" for d in outdated_high])
        recommendations.append(f"### 高优先级更新")
        recommendations.append(f"```bash")
        recommendations.append(f"pip install -U {high_priority_packages}")
        recommendations.append(f"```\n")
    
    if outdated_medium:
        medium_priority_packages = " ".join([f"{d.name}=={d.latest_version}" for d in outdated_medium])
        recommendations.append(f"### 中优先级更新")
        recommendations.append(f"```bash")
        recommendations.append(f"pip install -U {medium_priority_packages}")
        recommendations.append(f"```\n")
    
    if outdated_low:
        low_priority_packages = " ".join([f"{d.name}=={d.latest_version}" for d in outdated_low])
        recommendations.append(f"### 低优先级更新")
        recommendations.append(f"```bash")
        recommendations.append(f"pip install -U {low_priority_packages}")
        recommendations.append(f"```\n")
    
    # 生成新的requirements.txt文件的建议
    recommendations.append("## 更新后的requirements.txt")
    recommendations.append("```")
    
    # 按原始requirements.txt文件的结构生成更新后的内容
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    requirements_path = os.path.join(project_root, "requirements.txt")
    
    with open(requirements_path, 'r') as file:
        lines = file.readlines()
        
    updated_lines = []
    for line in lines:
        line_stripped = line.strip()
        if not line_stripped or line_stripped.startswith('#'):
            updated_lines.append(line.rstrip())
            continue
            
        if '==' in line_stripped:
            parts = line_stripped.split('==')
            package = parts[0].strip()
            
            # 查找包信息
            dep_info = next((d for d in dependencies if d.name == package), None)
            if dep_info and (dep_info.is_outdated or dep_info.vulnerabilities):
                updated_line = f"{package}=={dep_info.latest_version}"
                # 保留行内注释
                if '#' in line:
                    comment = line.split('#', 1)[1]
                    updated_line += f" # {comment.strip()}"
                else:
                    updated_line += "\n"
                updated_lines.append(updated_line)
            else:
                updated_lines.append(line.rstrip())
        else:
            updated_lines.append(line.rstrip())
    
    recommendations.append("\n".join(updated_lines))
    recommendations.append("```")
    
    # 添加更新后的测试建议
    recommendations.append("\n## 更新后测试建议")
    
    # 根据依赖更新的情况，给出具体的测试建议
    if vulnerable or outdated_high:
        recommendations.append("由于包含安全漏洞修复或核心依赖的重大更新，强烈建议执行以下测试：")
        recommendations.append("1. 运行完整的单元测试和集成测试套件")
        recommendations.append("2. 进行手动功能测试，特别关注认证和数据处理功能")
        recommendations.append("3. 检查日志中是否有异常或警告")
        
        # 提供特定依赖的测试建议
        if any(d.name == 'sqlalchemy' for d in outdated_high + vulnerable):
            recommendations.append("\n### SQLAlchemy更新测试重点")
            recommendations.append("- 验证所有数据库查询是否正常工作")
            recommendations.append("- 检查事务处理和会话管理")
            recommendations.append("- 关注ORM模型关系加载")
            
        if any(d.name == 'pydantic' for d in outdated_high + vulnerable):
            recommendations.append("\n### Pydantic更新测试重点")
            recommendations.append("- 验证所有模型验证逻辑")
            recommendations.append("- 检查复杂数据结构的序列化/反序列化")
            recommendations.append("- 确认API请求和响应处理")
            
    else:
        recommendations.append("本次更新主要为常规版本更新，建议执行以下测试：")
        recommendations.append("1. 运行单元测试确保基本功能正常")
        recommendations.append("2. 对更新的依赖相关功能进行手动验证")
    
    return "\n".join(recommendations)

def parse_args() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="依赖包检查工具")
    parser.add_argument(
        "--requirements",
        default=None,
        help="requirements.txt文件路径，默认为项目根目录下的requirements.txt"
    )
    parser.add_argument(
        "--output",
        default=None,
        help="输出报告文件路径，默认为scripts/dependencies_report.md"
    )
    parser.add_argument(
        "--security-only",
        action="store_true",
        help="仅检查安全漏洞"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细信息"
    )
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    print("正在检查依赖包...\n")
    
    # 设置项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(script_dir)
    
    # 确定requirements.txt路径
    if args.requirements:
        requirements_path = args.requirements
    else:
        requirements_path = os.path.join(project_root, "requirements.txt")
    
    # 确定输出文件路径
    if args.output:
        output_path = args.output
    else:
        output_path = os.path.join(script_dir, "dependencies_report.md")
    
    # 显示启动信息
    if args.verbose:
        print(f"依赖文件: {requirements_path}")
        print(f"输出报告: {output_path}")
        if args.security_only:
            print("模式: 仅检查安全漏洞")
    
    # 检查requirements.txt文件是否存在
    if not os.path.exists(requirements_path):
        print(f"错误: 找不到requirements.txt文件: {requirements_path}")
        sys.exit(1)
    
    # 解析requirements.txt并检查依赖
    dependencies = []
    try:
        # 解析requirements.txt
        deps_dict = parse_requirements(requirements_path)
        
        # 对于每个依赖包执行检查
        for package, current_version in deps_dict.items():
            if args.verbose:
                print(f"检查依赖: {package}=={current_version}")
                
            latest_version = get_latest_version(package)
            vulnerabilities = check_vulnerabilities(package, current_version)
            
            # 检查是否过时
            from packaging import version as packaging_version
            is_outdated = False
            try:
                if latest_version != "unknown":
                    current = packaging_version.parse(current_version)
                    latest = packaging_version.parse(latest_version)
                    is_outdated = latest > current
            except:
                if args.verbose:
                    print(f"  警告: 无法解析版本 {current_version} 或 {latest_version}")
                    
            # 如果仅检查安全漏洞，则忽略非漏洞的过时依赖
            if args.security_only and not vulnerabilities:
                is_outdated = False
                
            # 确定更新优先级
            priority = determine_update_priority(
                package, current_version, latest_version, vulnerabilities
            )
            
            # 添加特殊说明
            notes = ""
            if package == "pydantic" and is_outdated:
                notes = "Pydantic 2.x 有重大API变更，更新需谨慎并全面测试"
            elif package == "sqlalchemy" and is_outdated:
                notes = "SQLAlchemy 2.0与1.x版本有API差异，更新需要调整代码"
                
            info = DependencyInfo(
                name=package,
                current_version=current_version,
                latest_version=latest_version,
                is_outdated=is_outdated,
                update_priority=priority,
                vulnerabilities=vulnerabilities,
                notes=notes
            )
            dependencies.append(info)
        
        # 按优先级排序
        priority_order = {"high": 0, "medium": 1, "low": 2}
        dependencies.sort(
            key=lambda x: (
                priority_order.get(x.update_priority, 3), 
                x.name
            )
        )
    except Exception as e:
        print(f"错误: 检查依赖时发生异常: {str(e)}")
        sys.exit(1)
    
    # 输出依赖状态
    print(f"{'包名':<25} {'当前版本':<15} {'最新版本':<15} {'状态':<10} {'优先级':<10}")
    print("-" * 75)
    
    # 统计信息
    outdated_count = 0
    vulnerability_count = 0
    
    for dep in dependencies:
        status = "最新"
        if dep.vulnerabilities:
            status = "漏洞"
            vulnerability_count += 1
        elif dep.is_outdated:
            status = "过时"
            outdated_count += 1
            
        print(f"{dep.name:<25} {dep.current_version:<15} {dep.latest_version:<15} {status:<10} {dep.update_priority:<10}")
    
    print("\n依赖统计:")
    print(f"总依赖数: {len(dependencies)}")
    print(f"过时依赖: {outdated_count}")
    print(f"存在漏洞: {vulnerability_count}")
    
    # 输出更新建议
    recommendations = generate_update_recommendations(dependencies)
    
    # 将更新建议写入文件
    try:
        with open(output_path, 'w') as f:
            f.write(recommendations)
        print(f"\n更新建议已保存至: {output_path}")
    except Exception as e:
        print(f"\n错误: 无法写入输出文件: {str(e)}")
    
    # 如果存在漏洞，提示用户立即更新
    if vulnerability_count > 0:
        print("\n警告: 检测到安全漏洞，建议立即更新受影响的依赖!")
        print(f"运行以下命令更新存在漏洞的依赖:")
        print(f"python scripts/update_dependencies.py --priority high")

if __name__ == "__main__":
    main() 