# 用户注册审计日志记录修复

## 问题描述

用户注册操作没有触发审计日志记录，原因是：

1. **用户注册路径被认证中间件排除**：`/api/v1/auth/register` 在认证中间件的排除路径中
2. **审计中间件只记录已认证用户的操作**：注册接口不需要认证，所以 `request.state.user_id` 为 `None`
3. **审计中间件跳过未认证请求**：代码中有 `if not user_id: return await call_next(request)`

## 解决方案

### 1. 扩展审计中间件支持未认证操作

修改 `svc/core/middleware/audit.py`，添加对特定未认证路径的支持：

#### 新增参数
```python
unauthenticated_paths: Optional[Set[str]] = None  # 需要记录的未认证路径
```

#### 默认未认证审计路径
```python
self.unauthenticated_paths = unauthenticated_paths or {
    "/api/v1/auth/register",  # 用户注册
    "/api/v1/auth/password-reset/request",  # 密码重置请求
    "/api/v1/auth/password-reset",  # 密码重置
    "/api/v1/wechat/login",  # 微信登录
}
```

#### 修改调度逻辑
```python
# 获取用户信息
user_id = getattr(request.state, 'user_id', None)

# 检查是否是需要记录的未认证操作
is_unauthenticated_audit = self._is_unauthenticated_audit_path(request)

# 只记录已认证用户的操作或特定的未认证操作
if not user_id and not is_unauthenticated_audit:
    return await call_next(request)
```

### 2. 改进资源类型解析

优化路径解析逻辑，正确处理 `/api/v1/auth/register` 格式：

```python
def _parse_action_and_resource(self, request: Request) -> tuple[str, str]:
    # ...
    if len(path_parts) >= 3:
        # 处理 /api/v1/auth/register 格式
        if path_parts[0] == 'api' and path_parts[1] in ['v1', 'v2']:
            resource_type = path_parts[2]  # auth, users, products 等
        # ...
```

### 3. 支持从请求体提取用户标识

对于未认证操作，从请求体中提取用户标识：

```python
def _extract_username_from_request(self, request: Request, request_body: Any) -> Optional[str]:
    """从请求中提取用户标识（用于未认证操作）"""
    try:
        if isinstance(request_body, dict):
            # 尝试从不同字段提取用户标识
            return (request_body.get('email') or 
                   request_body.get('username') or 
                   request_body.get('phone') or
                   request_body.get('wechat_user_id'))
        return None
    except Exception:
        return None
```

### 4. 更新中间件配置

在 `svc/core/middleware/__init__.py` 中更新配置：

```python
"audit": {
    "enabled": True,
    "options": {
        "exclude_paths": {
            "/health", "/metrics", "/docs", "/openapi.json", "/redoc",
            "/system/audit", "/favicon.ico"
        },
        "exclude_methods": {"GET", "OPTIONS", "HEAD"},
        "include_request_body": True,  # 启用请求体记录
        "include_response_body": False,
        "max_body_size": 2048,  # 增加大小
        "unauthenticated_paths": {
            "/api/v1/auth/register",
            "/api/v1/auth/password-reset/request", 
            "/api/v1/auth/password-reset",
            "/api/v1/wechat/login"
        }
    }
}
```

## 修复后的功能

### ✅ 支持的未认证操作审计

1. **用户注册** (`POST /api/v1/auth/register`)
   - 记录注册用户的邮箱/用户名
   - 记录注册请求的详细信息
   - 记录成功/失败状态

2. **密码重置请求** (`POST /api/v1/auth/password-reset/request`)
   - 记录请求重置密码的邮箱
   - 记录请求来源信息

3. **密码重置** (`POST /api/v1/auth/password-reset`)
   - 记录密码重置操作
   - 记录相关用户信息

4. **微信登录** (`POST /api/v1/wechat/login`)
   - 记录微信用户ID
   - 记录登录请求信息

### 📊 审计日志格式示例

#### 用户注册成功
```json
{
    "action": "create",
    "resource_type": "auth",
    "user_id": null,
    "username": "<EMAIL>",
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "status": "success",
    "details": {
        "method": "POST",
        "path": "/api/v1/auth/register",
        "duration": 0.156,
        "status_code": 201,
        "request_body": {
            "email": "<EMAIL>",
            "fullname": "New User"
        }
    },
    "message": "API POST /api/v1/auth/register - success"
}
```

#### 用户注册失败
```json
{
    "action": "create",
    "resource_type": "auth",
    "user_id": null,
    "username": "<EMAIL>",
    "status": "failure",
    "details": {
        "method": "POST",
        "path": "/api/v1/auth/register",
        "duration": 0.023,
        "status_code": 400,
        "error": "邮箱已存在"
    },
    "message": "API POST /api/v1/auth/register - failure"
}
```

## 测试验证

### 核心功能测试通过 ✅

1. **未认证路径识别**
   - `/api/v1/auth/register` ✅
   - `/api/v1/auth/password-reset/request` ✅
   - `/api/v1/wechat/login` ✅

2. **用户标识提取**
   - 从邮箱字段提取 ✅
   - 从用户名字段提取 ✅
   - 从微信用户ID提取 ✅

3. **审计日志记录**
   - 用户注册操作记录 ✅
   - 密码重置请求记录 ✅
   - 普通未认证请求跳过 ✅
   - 已认证用户请求正常记录 ✅

## 使用说明

### 自动启用

修复后的审计中间件会自动记录以下未认证操作：
- 用户注册
- 密码重置相关操作
- 微信登录

### 配置自定义未认证路径

如果需要记录其他未认证操作，可以在配置中添加：

```python
app.add_middleware(
    AuditMiddleware,
    enabled=True,
    unauthenticated_paths={
        "/api/v1/auth/register",
        "/api/v1/custom/public-action",  # 自定义路径
    }
)
```

### 查看审计日志

可以通过系统管理接口查看审计日志：
- `GET /api/v1/system/audit` - 查询审计日志
- 支持按操作类型、资源类型、用户等条件过滤

## 安全考虑

1. **敏感信息过滤**：密码等敏感字段不会被记录
2. **请求体大小限制**：限制记录的请求体大小（默认2KB）
3. **异步记录**：不影响业务性能
4. **错误容错**：审计记录失败不影响业务流程

## 总结

通过这次修复，审计中间件现在能够：

- ✅ **完整记录用户注册操作**
- ✅ **支持其他重要的未认证操作**
- ✅ **保持对已认证操作的完整支持**
- ✅ **提供灵活的配置选项**
- ✅ **确保系统安全和性能**

用户注册等重要操作现在都会被正确记录到审计日志中，满足安全合规和操作追踪的需求。
