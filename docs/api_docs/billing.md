# 计费模块 (Billing) API 接口文档

## 订阅计划管理接口

### 创建订阅计划

**请求**:

-   方法: `POST`
-   路径: `/api/subscription-plans`
-   描述: 创建新订阅计划
-   权限: 平台管理员

**请求体**:

```json
{
  "name": "企业版",
  "display_name": "企业版订阅",
  "description": "适合大型企业的订阅计划",
  "price": 999.00,
  "currency": "CNY",
  "billing_cycle": "monthly",
  "features": ["advanced_analytics", "sso", "unlimited_storage"],
  "is_active": true
}
```

**响应**:

-   状态码: 201 Created
-   响应体:

```json
{
  "id": "plan_12345",
  "name": "企业版",
  "display_name": "企业版订阅",
  "description": "适合大型企业的订阅计划",
  "price": 999.00,
  "currency": "CNY",
  "billing_cycle": "monthly",
  "features": ["advanced_analytics", "sso", "unlimited_storage"],
  "is_active": true,
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

### 获取订阅计划列表

**请求**:

-   方法: `GET`
-   路径: `/api/subscription-plans`
-   描述: 获取所有订阅计划列表(分页)
-   权限: 平台管理员或用户

**查询参数**:

-   `skip`: 跳过记录数 (默认: 0)
-   `limit`: 返回记录数 (默认: 100)
-   `is_active`: 按激活状态筛选 (可选)

**响应**:

-   状态码: 200 OK
-   响应体:

```json
{
  "total": 5,
  "items": [
    {
      "id": "plan_12345",
      "name": "企业版",
      "display_name": "企业版订阅",
      "description": "适合大型企业的订阅计划",
      "price": 999.00,
      "currency": "CNY",
      "billing_cycle": "monthly",
      "is_active": true,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    }
  ]
}
```

## 订阅管理接口

### 创建订阅

**请求**:

-   方法: `POST`
-   路径: `/api/subscriptions`
-   描述: 为用户创建新订阅
-   权限: 用户

**请求体**:

```json
{
  "plan_id": "plan_12345",
  "user_id": 123,
  "payment_method": {
    "type": "credit_card",
    "card_number": "****************",
    "expiry": "12/25",
    "cvc": "123"
  },
  "billing_address": {
    "name": "张三",
    "address": "北京市海淀区中关村",
    "city": "北京",
    "state": "北京",
    "postal_code": "100080",
    "country": "中国"
  }
}
```

**响应**:

-   状态码: 201 Created
-   响应体:

```json
{
  "id": "sub_12345",
  "plan_id": "plan_12345",
  "user_id": 123,
  "status": "active",
  "current_period_start": "2023-01-01T00:00:00Z",
  "current_period_end": "2023-01-31T23:59:59Z",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

### 获取用户订阅

**请求**:

-   方法: `GET`
-   路径: `/api/users/{user_id}/subscriptions`
-   描述: 获取指定用户的订阅
-   权限: 用户自己或管理员

**响应**:

-   状态码: 200 OK
-   响应体:

```json
{
  "id": "sub_12345",
  "plan": {
    "id": "plan_12345",
    "name": "企业版",
    "display_name": "企业版订阅"
  },
  "status": "active",
  "current_period_start": "2023-01-01T00:00:00Z",
  "current_period_end": "2023-01-31T23:59:59Z",
  "payment_status": "paid",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

## 发票管理接口

### 获取用户发票列表

**请求**:

-   方法: `GET`
-   路径: `/api/users/{user_id}/invoices`
-   描述: 获取指定用户的发票列表
-   权限: 用户自己或管理员

**查询参数**:

-   `skip`: 跳过记录数 (默认: 0)
-   `limit`: 返回记录数 (默认: 100)
-   `status`: 按状态筛选 (可选)

**响应**:

-   状态码: 200 OK
-   响应体:

```json
{
  "total": 3,
  "items": [
    {
      "id": "inv_12345",
      "subscription_id": "sub_12345",
      "amount": 999.00,
      "currency": "CNY",
      "status": "paid",
      "period_start": "2023-01-01T00:00:00Z",
      "period_end": "2023-01-31T23:59:59Z",
      "paid_at": "2023-01-01T12:30:00Z",
      "pdf_url": "https://example.com/invoices/inv_12345.pdf",
      "created_at": "2023-01-01T12:00:00Z"
    }
  ]
}
```

### 获取发票详情

**请求**:

-   方法: `GET`
-   路径: `/api/invoices/{invoice_id}`
-   描述: 获取发票详情
-   权限: 发票所属用户或管理员

**响应**:

-   状态码: 200 OK
-   响应体:

```json
{
  "id": "inv_12345",
  "subscription_id": "sub_12345",
  "user_id": 123,
  "plan": {
    "id": "plan_12345",
    "name": "企业版",
    "display_name": "企业版订阅"
  },
  "amount": 999.00,
  "currency": "CNY",
  "status": "paid",
  "period_start": "2023-01-01T00:00:00Z",
  "period_end": "2023-01-31T23:59:59Z",
  "paid_at": "2023-01-01T12:30:00Z",
  "payment_method": {
    "type": "credit_card",
    "last4": "4242",
    "expiry": "01/25"
  },
  "billing_address": {
    "name": "张三",
    "address": "北京市海淀区中关村",
    "city": "北京",
    "state": "北京",
    "postal_code": "100080",
    "country": "中国"
  },
  "line_items": [
    {
      "description": "企业版订阅 (2023-01-01 至 2023-01-31)",
      "amount": 999.00,
      "currency": "CNY"
    }
  ],
  "pdf_url": "https://example.com/invoices/inv_12345.pdf",
  "created_at": "2023-01-01T12:00:00Z"
}
```
