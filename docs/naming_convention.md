# FastAPI Nano 命名规范

本文档规定了 FastAPI Nano 项目的统一命名规范，所有代码应遵循以下规则。

## 1. 文件命名

-   使用蛇形命名法（snake_case）
-   所有文件名小写，单词间用下划线分隔
-   例如：`user_service.py`, `auth_middleware.py`

## 2. 类命名

-   使用帕斯卡命名法（PascalCase）
-   每个单词首字母大写，无分隔符
-   例如：`UserService`, `AuthMiddleware`

## 3. 变量命名

-   使用蛇形命名法（snake_case）
-   所有变量名小写，单词间用下划线分隔
-   特殊类型变量命名规则：
    -   布尔变量使用`is_*`或`has_*`前缀，例如：`is_active`, `has_permission`
    -   列表变量使用复数名词，例如：`users`, `permissions`
    -   字典变量使用`*_map`或`*_dict`后缀，例如：`user_map`, `config_dict`
    -   **避免使用 SQLAlchemy 等库的保留字**（如`metadata`），应改用`meta_data`

## 4. 常量命名

-   全大写，单词间用下划线分隔
-   常量应定义在模块顶部
-   例如：`DEFAULT_TIMEOUT`, `MAX_RETRIES`

## 5. 函数命名

-   使用蛇形命名法（snake_case）
-   动词开头，表示动作
-   例如：`get_user_by_id()`, `create_access_token()`

## 6. 枚举命名

-   枚举类名使用帕斯卡命名法（PascalCase）
-   枚举值使用全大写，单词间用下划线分隔
-   例如：
    ```python
    class UserStatus(str, Enum):
        ACTIVE = "active"
        INACTIVE = "inactive"
    ```

## 7. 接口和协议命名

-   使用帕斯卡命名法（PascalCase）
-   接口和协议名以`Interface`或`Protocol`结尾
-   例如：`UserInterface`, `ConfigProtocol`

## 8. 目录命名

-   使用蛇形命名法（snake_case）
-   所有目录名小写，单词间用下划线分隔
-   例如：`user_management`, `auth_service`

## 9. 避免缩写

-   除非是广泛接受的缩写（如`HTTP`，`URL`等），否则使用完整单词
-   例如：使用`user_authentication`而非`user_auth`

## 10. 特殊保留字

以下是应避免使用的保留字：

-   `metadata`（SQLAlchemy 保留字）- 使用`meta_data`替代
-   `type`（Python 内置函数）- 使用`type_name`或`type_info`替代
-   `id`（常见的外键字段）- 在变量名中使用更具描述性的名称，如`user_id`
