# API审计日志中间件使用指南

## 概述

API审计日志中间件是一个自动记录用户API操作的中间件，它可以：

- 自动记录已认证用户的API操作
- 记录请求信息、响应状态、执行时间等
- 支持灵活的配置，可排除特定路径和方法
- 异步记录，不影响API性能
- 与现有审计日志系统集成

## 功能特性

### 自动记录内容

- **用户信息**：用户ID、用户名
- **请求信息**：HTTP方法、路径、查询参数、请求头
- **响应信息**：状态码、响应头、执行时间
- **操作分类**：自动解析操作类型（create/update/delete/read）和资源类型
- **错误信息**：记录异常和错误详情
- **可选内容**：请求体、响应体（可配置）

### 智能过滤

- **路径过滤**：排除健康检查、文档等不需要审计的路径
- **方法过滤**：默认只记录修改操作（POST/PUT/PATCH/DELETE）
- **用户过滤**：只记录已认证用户的操作
- **大小限制**：限制记录的请求/响应体大小

## 安装和配置

### 1. 基本配置

在应用中启用审计中间件：

```python
from fastapi import FastAPI
from svc.core.middleware.audit import AuditMiddleware

app = FastAPI()

# 添加审计中间件
app.add_middleware(
    AuditMiddleware,
    enabled=True,
    exclude_paths={
        "/health", "/metrics", "/docs", "/openapi.json", 
        "/system/audit"  # 避免循环记录
    },
    exclude_methods={"GET", "OPTIONS", "HEAD"},
    include_request_body=False,
    include_response_body=False,
    max_body_size=1024
)
```

### 2. 使用配置文件

通过中间件配置系统启用：

```python
from svc.core.middleware import setup_middlewares, middleware_config

# 修改配置
custom_config = middleware_config.copy()
custom_config["audit"]["enabled"] = True
custom_config["audit"]["options"]["include_request_body"] = True

# 应用配置
setup_middlewares(app, custom_config)
```

### 3. 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | bool | True | 是否启用中间件 |
| `exclude_paths` | Set[str] | 见下方 | 排除的路径集合 |
| `exclude_methods` | Set[str] | {"GET", "OPTIONS", "HEAD"} | 排除的HTTP方法 |
| `include_request_body` | bool | False | 是否记录请求体 |
| `include_response_body` | bool | False | 是否记录响应体 |
| `max_body_size` | int | 1024 | 最大记录体大小（字节） |

**默认排除路径**：
```python
{
    "/health", "/metrics", "/docs", "/openapi.json", "/redoc",
    "/system/audit", "/favicon.ico"
}
```

## 使用要求

### 1. 认证中间件集成

审计中间件需要在认证中间件之后运行，依赖以下request.state属性：

```python
# 认证中间件需要设置这些属性
request.state.user_id = user_id  # 用户ID
request.state.token_payload = {  # Token载荷（可选）
    "username": "user_name"
}
```

### 2. 数据库配置

确保审计日志相关的数据库表已创建：

```sql
-- 审计日志表
CREATE TABLE audit_logs (
    id SERIAL PRIMARY KEY,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    user_id INTEGER,
    username VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'success',
    details JSONB,
    message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
);
```

## 审计日志格式

### 成功操作示例

```json
{
    "id": 1001,
    "action": "create",
    "resource_type": "user",
    "resource_id": null,
    "user_id": 123,
    "username": "admin",
    "ip_address": "*************",
    "user_agent": "Mozilla/5.0...",
    "status": "success",
    "details": {
        "method": "POST",
        "path": "/api/users",
        "duration": 0.156,
        "status_code": 201,
        "query_params": {},
        "request_body": {
            "username": "newuser",
            "email": "<EMAIL>"
        }
    },
    "message": "API POST /api/users - success",
    "created_at": "2024-01-15T10:30:00Z"
}
```

### 失败操作示例

```json
{
    "id": 1002,
    "action": "delete",
    "resource_type": "user",
    "user_id": 123,
    "username": "admin",
    "status": "failure",
    "details": {
        "method": "DELETE",
        "path": "/api/users/999",
        "duration": 0.023,
        "error": "User not found"
    },
    "message": "API DELETE /api/users/999 - failure",
    "created_at": "2024-01-15T10:31:00Z"
}
```

## 性能考虑

### 1. 异步记录

审计日志采用异步记录方式，不会阻塞API响应：

```python
# 中间件在后台异步记录，不影响响应时间
async def _async_record_audit_log(...):
    # 获取独立的数据库会话
    # 记录审计日志
    # 关闭会话
```

### 2. 数据库连接管理

每次记录审计日志都使用独立的数据库会话，避免影响主业务事务。

### 3. 错误处理

审计日志记录失败不会影响业务流程：

```python
try:
    await self._record_audit_log(...)
except Exception as e:
    # 只记录错误日志，不抛出异常
    logger.error(f"记录审计日志失败: {str(e)}")
```

## 最佳实践

### 1. 合理配置排除规则

```python
# 排除不需要审计的路径
exclude_paths = {
    "/health",      # 健康检查
    "/metrics",     # 监控指标
    "/docs",        # API文档
    "/static",      # 静态资源
    "/system/audit" # 审计日志查询（避免循环）
}

# 只记录修改操作
exclude_methods = {"GET", "OPTIONS", "HEAD"}
```

### 2. 谨慎记录请求/响应体

```python
# 只在必要时记录请求体
include_request_body = True  # 仅用于重要操作

# 通常不记录响应体（可能包含敏感信息）
include_response_body = False

# 限制记录大小
max_body_size = 2048  # 2KB
```

### 3. 定期清理审计日志

```python
# 使用审计日志服务的清理功能
from svc.apps.system.services.audit_log import AuditLogService

# 保留90天的审计日志
await audit_service.cleanup_old_logs(days_to_keep=90)
```

## 故障排除

### 1. 审计日志未记录

检查以下项目：
- 中间件是否正确启用
- 用户是否已认证（request.state.user_id存在）
- 请求是否被排除规则过滤
- 数据库连接是否正常

### 2. 性能问题

如果发现性能影响：
- 减少记录的详细信息
- 增加排除规则
- 检查数据库性能
- 考虑使用消息队列异步处理

### 3. 存储空间问题

审计日志增长过快时：
- 设置自动清理策略
- 优化记录内容
- 考虑归档旧数据

## 示例代码

参考 `examples/audit_middleware_example.py` 查看完整的使用示例。
