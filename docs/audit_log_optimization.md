# 审计日志深度优化文档

## 概述

本次深度优化彻底重构了审计日志系统，**不再考虑向后兼容性**，实现了更智能、更统一的审计信息记
录。主要解决了未认证审计日志中用户 ID 和资源信息记录不完整的问题，并大幅提升了代码质量和可维
护性。

## 🎯 核心优化亮点

-   **统一处理逻辑**：移除认证/未认证区分，所有请求使用统一审计流程
-   **智能资源识别**：支持数字 ID、UUID、字母数字组合等多种资源 ID 格式
-   **模块化设计**：为不同资源类型创建专门的提取器
-   **操作关键词过滤**：避免将操作名称误识别为资源 ID
-   **代码结构优化**：更清晰的方法分工和更好的可维护性

## 问题分析

### 原有问题

1. **资源 ID 缺失**：审计中间件没有提取和记录 `resource_id`，导致无法准确定位具体的操作资源
2. **用户标识不完整**：对于未认证请求，用户标识提取逻辑简单，支持的字段有限
3. **资源信息不够详细**：缺少从 URL 路径和请求体中提取具体资源信息的逻辑

### 影响范围

-   未认证的用户注册、登录、密码重置等操作
-   公开 API 的访问记录
-   系统安全审计和问题排查的效率

## 深度优化方案

### 1. 统一审计逻辑

-   **移除认证/未认证区分**：不再区分认证和未认证请求，统一记录所有请求
-   **简化配置**：移除 `unauthenticated_paths` 配置，简化中间件初始化
-   **统一处理流程**：所有请求使用相同的审计处理逻辑

### 2. 智能资源 ID 提取优化

#### 修改内容

-   修改 `_parse_action_and_resource` 方法，从返回 `(action, resource_type)` 改为返回
    `(action, resource_type, resource_id)`
-   增加从 URL 路径中提取资源 ID 的逻辑
-   新增 `_extract_resource_id_from_request` 方法，从请求体中提取资源 ID

#### 提取规则

**从 URL 路径提取**：

-   `/api/v1/users/123` → resource_id = "123"
-   `/api/v1/products/456` → resource_id = "456"
-   支持多层级路径结构

**从请求体提取**：

-   **认证操作** (`auth`)：
    -   注册/登录：提取 `email`、`username`、`phone`
    -   密码重置：提取 `email`
-   **用户操作** (`users`)：提取 `id`、`user_id`、`email`、`username`
-   **产品操作** (`products`)：提取 `id`、`product_id`、`sku`、`code`
-   **通用操作**：提取 `id`、`{resource_type}_id`、`code`、`name`

### 2. 用户标识提取优化

#### 修改内容

-   增强 `_extract_username_from_request` 方法
-   支持更多用户标识字段
-   增加从查询参数中提取用户标识的功能

#### 支持字段

**从请求体提取**：

-   `email`
-   `username`
-   `phone`
-   `mobile`
-   `wechat_user_id`
-   `openid`
-   `unionid`

**从查询参数提取**：

-   `email`
-   `username`
-   `phone`
-   `mobile`

### 3. 审计日志记录优化

#### 修改内容

-   在 `_record_audit_log` 方法中传递 `resource_id` 参数
-   在 `_async_record_audit_log` 方法中增加 `resource_id` 参数
-   对于未认证请求，增加资源信息提取逻辑

## 代码变更

### 主要文件

-   `svc/core/middleware/audit.py`

### 关键方法变更

1. **`_parse_action_and_resource`**

    ```python
    # 原来
    def _parse_action_and_resource(self, request: Request) -> tuple[str, str]:

    # 现在
    def _parse_action_and_resource(self, request: Request) -> tuple[str, str, Optional[str]]:
    ```

2. **`_extract_username_from_request`**

    - 增加更多字段支持
    - 增加查询参数提取

3. **新增 `_extract_resource_id_from_request`**
    - 根据资源类型智能提取资源 ID
    - 支持多种操作场景

## 测试验证

### 测试用例

1. **资源 ID 提取测试**

    - 用户注册：`/api/v1/auth/register` + `{"email": "<EMAIL>"}` → resource_id =
      "<EMAIL>"
    - 用户登录：`/api/v1/auth/login` + `{"email": "<EMAIL>"}` → resource_id =
      "<EMAIL>"
    - 获取用户：`/api/v1/users/123` → resource_id = "123"
    - 创建产品：`/api/v1/products` + `{"sku": "TEST001"}` → resource_id = "TEST001"
    - 密码重置：`/api/v1/auth/reset-password` + `{"email": "<EMAIL>"}` →
      resource_id = "<EMAIL>"

2. **用户名提取测试**
    - 从请求体提取邮箱、用户名、手机号
    - 从查询参数提取用户标识

### 测试结果

所有测试用例均通过，功能正常工作。

## 使用示例

### 优化前的审计日志

```json
{
  "action": "create",
  "resource_type": "auth",
  "resource_id": null,
  "user_id": null,
  "username": null,
  "details": {...}
}
```

### 优化后的审计日志

```json
{
  "action": "create",
  "resource_type": "auth",
  "resource_id": "<EMAIL>",
  "user_id": null,
  "username": "<EMAIL>",
  "details": {...}
}
```

## 兼容性

-   向后兼容，不影响现有功能
-   对已认证用户的审计日志记录无影响
-   仅增强未认证请求的信息记录

## 注意事项

1. **性能影响**：增加了请求体解析和字段提取逻辑，但影响微乎其微
2. **隐私保护**：确保敏感信息（如密码）不会被记录到审计日志中
3. **错误处理**：所有提取逻辑都有异常处理，不会影响正常业务流程

## 🔧 登录接口修复

### 问题发现

在测试登录接口时发现审计日志无法正确提取资源 ID，原因是：

1. **OAuth2PasswordRequestForm 使用表单数据**：登录接口使用
   `application/x-www-form-urlencoded` 格式，不是 JSON
2. **字段名称差异**：OAuth2 标准使用 `username` 字段，但实际可能包含邮箱地址
3. **微信登录特殊结构**：微信登录使用不同的字段结构和嵌套数据

### 修复方案

1. **增强请求体解析**：

    - 支持 `application/json` 格式
    - 支持 `application/x-www-form-urlencoded` 格式
    - 支持 `multipart/form-data` 格式（基础支持）

2. **优化资源 ID 提取**：

    - 登录操作优先提取 `username` 字段（OAuth2 标准）
    - 微信登录提取 `code`、`openid`、`unionid` 等字段
    - 支持嵌套数据结构（如 `user_info.openid`）

3. **代码改进**：

    ```python
    # 新增表单数据解析方法
    def _parse_form_data(self, form_data: str) -> dict

    # 新增微信资源ID提取器
    def _extract_wechat_resource_id(self, request: Request, request_body: dict) -> Optional[str]

    # 增强请求体读取方法
    async def _read_request_body(self, request: Request) -> Optional[Any]
    ```

### 修复验证

✅ **OAuth2 表单登录**：正确提取用户邮箱作为资源 ID ✅ **JSON 格式登录**：正确提取邮箱字段作
为资源 ID ✅ **微信登录**：正确提取 code 作为资源 ID ✅ **表单数据解析**：正确解析 URL 编码
的表单数据

## 🔄 未认证操作成功后的审计日志补充

### 问题描述

未认证操作（如登录、注册）成功后，需要再次记录一条包含用户 ID 的审计日志，以建立完整的审计链
条。

### 解决方案

在认证成功的事件处理器中添加审计日志记录：

```python
@local_handler.register(event_name=AUTH_USER_LOGGED_IN)
async def handle_user_login(event: Event, user_service: UserService = Depends(get_user_service)):
    # 获取用户信息
    user = await user_service.get_resource_by_id(user_id)
    username = user.email if user else None

    # 记录登录成功的审计日志（包含user_id）
    dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
        "user_id": user_id,
        "action": "login_success",
        "resource_type": "auth",
        "resource_id": username,
        "username": username,
        "metadata": {
            "ip_address": ip_address,
            "user_agent": user_agent,
            "login_time": last_login_time
        }
    })
```

### 完整审计链条

现在每次登录都会产生两条审计日志：

1. **未认证请求日志**：

    ```json
    {
      "user_id": null,
      "action": "create",
      "resource_type": "auth",
      "resource_id": "<EMAIL>",
      "username": "<EMAIL>"
    }
    ```

2. **登录成功日志**：
    ```json
    {
      "user_id": 123,
      "action": "login_success",
      "resource_type": "auth",
      "resource_id": "<EMAIL>",
      "username": "<EMAIL>"
    }
    ```

### 追踪能力

通过这两条日志可以：

-   **通过邮箱关联**：两条记录都有相同的 resource_id 和 username
-   **通过时间和 IP**：在相近时间和相同 IP 下的操作
-   **建立完整链条**：从未认证尝试到认证成功的完整过程

### 实现细节

#### 修改的关键文件

1. **`svc/apps/system/events/audit_log_handlers.py`**：

    - 修改 `SYSTEM_AUDIT_LOG_RECORDED` 事件处理器
    - 现在会实际调用 `AuditLogService` 创建审计日志记录
    - 不再只是记录日志信息

2. **`svc/apps/auth/events/auth_handlers.py`**：
    - 在 `handle_user_login` 中添加审计日志记录
    - 登录成功后触发 `SYSTEM_AUDIT_LOG_RECORDED` 事件
    - 包含完整的用户信息和元数据

#### 事件流程

```
1. 用户登录请求 → 审计中间件记录未认证日志
2. 登录验证成功 → 触发 AUTH_USER_LOGGED_IN 事件
3. 事件处理器 → 触发 SYSTEM_AUDIT_LOG_RECORDED 事件
4. 审计日志处理器 → 调用 AuditLogService 创建记录
```

### 验证方法

1. **执行登录操作**
2. **查询审计日志表**：
    ```sql
    SELECT * FROM audit_logs
    WHERE resource_type = 'auth'
    AND (action = 'create' OR action = 'login_success')
    ORDER BY created_at DESC;
    ```
3. **应该看到两条相关记录**：
    - 未认证请求记录 (`user_id=null`, `action=create`)
    - 登录成功记录 (`user_id=实际ID`, `action=login_success`)

## 后续优化建议

1. **配置化**：将资源 ID 提取规则配置化，支持不同业务场景的定制
2. **字段映射**：支持自定义字段映射规则
3. **批量操作**：优化批量操作的审计日志记录
4. **性能监控**：增加审计日志记录的性能监控指标
5. **多部分表单**：完善 `multipart/form-data` 格式的解析支持
