# 营销活动系统设计文档

## 核心业务流程

```mermaid
sequenceDiagram
    participant 管理员
    participant 用户
    participant 系统
    participant 被邀请人

    管理员->>系统: 1.创建营销活动
    系统->>系统: 存储活动规则（阶梯奖励、有效期）

    用户->>系统: 2.查询可用活动
    系统-->>用户: 返回活动列表

    用户->>系统: 3.生成专属邀请链接
    系统-->>用户: 返回带用户ID和活动ID的链接

    被邀请人->>系统: 4.通过链接注册/参与
    系统->>系统: 记录邀请关系 → 更新邀请人数

    系统->>系统: 5.触发奖励计算
    系统->>用户: 6.发放阶梯奖励
    系统->>管理员: 7.活动数据统计


二、核心功能模块
模块	功能说明
活动管理	创建/编辑活动，配置阶梯奖励规则、有效期、参与条件
邀请关系追踪	记录用户邀请链，实时统计邀请人数
奖励引擎	根据实时邀请人数匹配奖励规则，自动发放积分/优惠券/现金等
防刷系统	设备指纹识别、IP限制、邀请有效性验证（避免自邀）
数据看板	展示活动参与数、奖励发放量、ROI等核心指标




```
