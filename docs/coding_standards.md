# FastAPI Nano 编码规范

本文档定义了 FastAPI Nano 项目的编码规范，包括命名规范、类型注解要求和文档编写指南。所有贡献
者应遵循这些规范，确保代码库的一致性和可维护性。

## 目录

-   [命名规范](#命名规范)
-   [类型注解](#类型注解)
-   [文档规范](#文档规范)
-   [代码组织](#代码组织)
-   [错误处理](#错误处理)
-   [最佳实践](#最佳实践)

## 命名规范

### 文件命名

-   使用小写字母加下划线的命名方式（snake_case）
-   避免使用缩写，除非是广泛接受的缩写（如 db, api 等）
-   模块名应简洁明了，反映其功能
-   测试文件应以 `test_` 开头

```python
# 正确示例
auth.py
user_service.py
database_utils.py
test_auth.py

# 错误示例
Auth.py  # 不应使用大写字母
userService.py  # 不应使用驼峰命名
db-utils.py  # 不应使用连字符
```

### 变量命名

-   使用小写字母加下划线的命名方式（snake_case）
-   布尔变量应使用 `is_*` 或 `has_*` 前缀
-   列表变量应使用复数名词
-   字典变量使用 `*_map` 或 `*_dict` 后缀
-   常量应全部大写，使用下划线分隔
-   避免使用单字母变量名，除非是循环计数器或数学公式

```python
# 正确示例
user_id = "12345"
is_active = True
has_permission = False
users = ["user1", "user2"]
role_map = {"admin": ["read", "write"], "user": ["read"]}
MAX_RETRIES = 3

# 错误示例
userId = "12345"  # 不应使用驼峰命名
active = True  # 布尔变量应使用 is_ 前缀
user = ["user1", "user2"]  # 列表应使用复数名词
```

### 函数和方法命名

-   使用小写字母加下划线的命名方式（snake_case）
-   函数名应使用"动词\_名词"格式
-   方法名应清晰表达动作和对象
-   不要在名称中包含返回类型
-   私有方法应以单下划线 `_` 开头

```python
# 正确示例
def get_user_by_id(user_id):
    ...

def create_authentication_token(user):
    ...

def _validate_input(data):
    ...

# 错误示例
def getUserById(userId):  # 不应使用驼峰命名
    ...

def user_list():  # 应使用动词开头
    ...

def get_user_dict():  # 不应在名称中包含返回类型
    ...
```

### 类命名

-   使用 PascalCase（首字母大写）
-   类名应为名词，不应包含下划线
-   抽象类可以使用 `Abstract` 或 `Base` 前缀

```python
# 正确示例
class User:
    ...

class AuthenticationService:
    ...

class BaseModel:
    ...

# 错误示例
class user:  # 应使用首字母大写
    ...

class authentication_service:  # 不应使用下划线
    ...

class userManager:  # 不应混合使用大小写和驼峰
    ...
```

## 类型注解

-   所有函数和方法应包含参数和返回值的类型注解
-   对于复杂类型，优先使用 `from typing import ...`
-   使用 Pydantic 模型进行数据验证和序列化
-   避免使用 `Any` 类型，除非绝对必要
-   使用 `Optional[Type]` 代替 `Type | None`
-   使用联合类型 `Union[Type1, Type2]` 表示多种可能类型
-   使用 `TypeVar` 和 `Generic` 实现泛型编程

```python
# 正确示例
from typing import List, Dict, Optional, Union, Any, TypeVar, Generic

def get_user(user_id: str) -> Optional[User]:
    ...

def process_items(items: List[Item]) -> Dict[str, Result]:
    ...

T = TypeVar('T')

class Repository(Generic[T]):
    def get(self, id: str) -> Optional[T]:
        ...
```

## 文档规范

-   所有模块、类、函数和方法应有文档字符串
-   使用 Google 风格的文档格式
-   文档应包括功能描述、参数说明、返回值说明和异常说明
-   对于复杂函数，应添加使用示例
-   避免在文档中重复代码已经表达的信息

```python
"""
模块级文档，描述模块的整体功能和用途。

可以包含多行文本，详细说明模块提供的功能、
使用方法和注意事项。
"""

def process_data(
    data: Dict[str, Any],
    options: Optional[Dict[str, str]] = None
) -> Result:
    """
    处理数据并返回结果。

    详细描述函数的功能和处理逻辑。

    Args:
        data: 要处理的数据字典
        options: 可选的处理选项

    Returns:
        处理结果对象

    Raises:
        ValueError: 当数据格式不正确时
        ProcessError: 当处理过程出错时

    Examples:
        >>> result = process_data({"id": "123", "value": "test"})
        >>> print(result.status)
        success
    """
    ...
```

## 代码组织

-   每个模块应遵循一致的目录结构
-   导入顺序：标准库 > 第三方库 > 项目内模块
-   相关功能应组织在同一模块中
-   使用 `__init__.py` 文件导出模块公共 API
-   避免循环导入

## 错误处理

-   使用显式异常类型，避免泛泛的 `Exception`
-   优先处理错误情况，提前返回或抛出异常
-   记录异常信息，提供有用的错误消息
-   使用 `try/except` 块包装可能出错的代码

## 最佳实践

-   使用异步函数处理 I/O 操作
-   使用依赖注入处理资源依赖
-   保持函数和方法短小，单一职责
-   使用列表推导式和生成器提高性能
-   遵循 PEP 8 代码风格指南
