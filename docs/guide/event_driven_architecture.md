# 事件驱动架构与业务逻辑重构方案

## 背景

随着系统复杂度增加，相似的业务逻辑经常在多个服务模块中重复出现，导致代码冗余、维护成本上升，
以及一致性难以保障。通过事件驱动架构重构业务逻辑，可以显著提高代码复用性，实现关注点分离，提
升系统可维护性和扩展性。

## 核心原则

1. **关注点分离**：将业务流程拆分为独立事件，每个处理器只负责单一职责
2. **松耦合设计**：服务之间通过事件通信，而非直接依赖调用
3. **单一数据源**：统一的事件定义作为系统状态变更的唯一来源
4. **可扩展性**：新功能可以通过添加事件监听器实现，无需修改现有代码
5. **可测试性**：事件处理器易于单元测试，提高系统质量

## 业务逻辑处理决策流程

如何决定哪些业务适合使用事件驱动方式实现？下面的决策流程图可以帮助我们做出合理判断：

```mermaid
graph TD
    A{需要处理业务逻辑} --> B{需要即时响应?}
    B -->|是| C[同步处理]
    B -->|否| D{是否核心事务?}
    D -->|是| E[同步+事务]
    D -->|否| F{是否多消费者?}
    F -->|是| G[事件系统]
    F -->|否| H[直接调用服务]
```

基于上述决策流程，我们分析项目中的业务场景：

### 同步处理适用场景

-   **用户登录认证** - 需要即时响应的核心功能
-   **活动创建表单提交** - 用户需要立即看到创建结果
-   **奖励规则配置更新** - 管理员需要立即确认更改生效

### 同步+事务适用场景

-   **用户账户充值** - 涉及金融交易，需要事务保证
-   **营销活动资源分配** - 涉及多个资源的分配和锁定
-   **订单支付处理** - 需要保证金额准确性和一致性

### 事件系统适用场景（多消费者）

1. **奖励发放流程**

    - 触发点：用户完成邀请、达成里程碑、参与活动
    - 消费者：奖励服务、通知服务、统计服务
    - 具体业务：
        - 每日签到奖励计算和发放
        - 邀请好友成功后的双方奖励
        - 阶梯式营销活动奖励发放
        - 连续活跃用户的额外奖励

2. **用户行为跟踪**

    - 触发点：用户登录、浏览、点击、购买等行为
    - 消费者：分析服务、推荐服务、营销服务
    - 具体业务：
        - 用户浏览历史记录
        - 用户偏好分析
        - 用户参与活动记录

3. **状态变更通知**

    - 触发点：实体状态变更（订单、活动、奖励等）
    - 消费者：通知服务、日志服务、关联业务服务
    - 具体业务：
        - 活动状态变更（创建、启动、暂停、结束）
        - 订单状态变更（创建、支付、发货、完成）
        - 奖励状态变更（创建、发放、领取、过期）

4. **业务审计日志**

    - 触发点：关键业务操作完成
    - 消费者：审计服务、统计服务、安全服务
    - 具体业务：
        - 管理员操作记录
        - 关键业务变更历史
        - 安全敏感操作审计

5. **业务统计更新**
    - 触发点：业务数据变更
    - 消费者：统计服务、报表服务、缓存服务
    - 具体业务：
        - 活动参与人数统计
        - 奖励发放数量和金额统计
        - 用户增长和留存统计

### 直接调用服务适用场景（单一消费者）

-   **内部数据验证** - 只需简单验证不需要广播
-   **单一服务内的状态更新** - 不涉及跨服务通信
-   **简单查询操作** - 只需获取数据不需要触发后续流程

## 项目具体业务重构建议

### 1. 奖励系统重构

**当前问题**：奖励发放逻辑在多处重复，造成一致性问题

**建议重构的业务流程**：

1. 每日签到奖励
2. 邀请奖励计算与发放
3. 里程碑奖励触发
4. 营销活动参与奖励

**重构建议**：

-   定义统一的奖励事件：`REWARD_CALCULATION_REQUESTED`、`REWARD_ISSUE_REQUESTED`
-   统一奖励计算逻辑到事件处理器中
-   分离奖励计算与发放流程，提高可扩展性

### 2. 通知系统重构

**当前问题**：通知逻辑散布在各个业务模块中，导致格式不一致、重复代码多

**建议重构的业务流程**：

1. 营销活动状态变更通知
2. 奖励发放通知
3. 系统公告推送
4. 活动提醒通知

**重构建议**：

-   定义统一的通知事件：`USER_NOTIFICATION_REQUESTED`
-   创建通知模板管理系统
-   实现多渠道（站内信、邮件、短信）统一分发机制

### 3. 数据统计重构

**当前问题**：统计逻辑与业务逻辑混合，重复计算多，性能问题严重

**建议重构的业务流程**：

1. 营销活动参与度统计
2. 用户行为分析统计
3. 奖励发放统计
4. 邀请转化率统计

**重构建议**：

-   定义统一的统计事件：`STATS_UPDATE_REQUESTED`
-   实现增量统计逻辑
-   引入统计聚合和异步计算机制

### 4. 审计日志重构

**当前问题**：审计逻辑分散，格式不统一，难以追踪关联操作

**建议重构的业务流程**：

1. 管理员操作审计
2. 重要业务变更记录
3. 安全敏感操作跟踪
4. 数据修改历史记录

**重构建议**：

-   定义统一的审计事件：`AUDIT_LOG_REQUESTED`
-   实现统一的审计记录格式
-   提供关联操作追踪能力

### 5. 用户行为跟踪重构

**当前问题**：行为跟踪代码嵌入业务逻辑，影响主流程性能

**建议重构的业务流程**：

1. 用户登录行为记录
2. 页面浏览跟踪
3. 功能使用统计
4. 用户参与记录

**重构建议**：

-   定义统一的行为事件：`USER_BEHAVIOR_TRACKED`
-   分离数据收集与分析逻辑
-   实现批量处理机制提高性能

## 重构目标场景

以下业务场景适合通过事件驱动架构进行重构：

1. **跨服务的通用流程**：需要在多个服务间协作完成的业务流程
2. **多触发点的相同逻辑**：相同逻辑可由多个不同事件触发
3. **高度重复的公共逻辑**：在多个服务中重复出现的业务规则
4. **可异步处理的任务**：不需要立即响应的处理逻辑
5. **需要解耦的复杂流程**：业务流程由多个步骤组成，但步骤之间没有强依赖

## 通用重构方案

### 1. 事件类型标准化

在中心模块（如`core/events/event_types.py`）中定义所有事件类型：

```python
from enum import Enum

class EventType(str, Enum):
    # 用户相关事件
    USER_CREATED = "user.created"
    USER_UPDATED = "user.updated"

    # 活动相关事件
    CAMPAIGN_CREATED = "campaign.created"
    CAMPAIGN_UPDATED = "campaign.updated"
    CAMPAIGN_LAUNCHED = "campaign.launched"
    CAMPAIGN_ENDED = "campaign.ended"

    # 邀请相关事件
    INVITATION_CREATED = "invitation.created"
    INVITATION_COMPLETED = "invitation.completed"

    # 奖励相关事件
    REWARD_RECORD_CREATED = "reward_record.created"
    REWARD_RECORD_ISSUED = "reward_record.issued"

    # 通用操作事件
    AUDIT_LOG = "audit.log"
    USER_NOTIFICATION = "user.notification"
    STATS_UPDATE = "stats.update"
```

### 2. 领域事件处理器模块化

为每个业务域创建专门的事件处理器模块：

```
svc/
  apps/
    marketing/
      events/
        campaign_handlers.py      # 活动相关事件处理器
        invitation_handlers.py    # 邀请相关事件处理器
        reward_handlers.py        # 奖励相关事件处理器
        notification_handlers.py  # 通知相关事件处理器
        stats_handlers.py         # 统计相关事件处理器
        __init__.py               # 统一导出所有处理器
```

### 3. 服务层事件集成模式

在服务层方法中集成事件触发：

```python
async def update_campaign_status(self, campaign_id: int, new_status: str):
    """更新活动状态"""
    # 1. 获取当前状态
    campaign = await self.get_campaign(campaign_id)
    if not campaign:
        return self.create_error_result(error_code=ErrorCode.CAMPAIGN_NOT_FOUND)

    old_status = campaign.status

    # 2. 更新状态
    try:
        campaign = await campaign.update(self.db, status=new_status)

        # 3. 触发事件而非直接调用相关逻辑
        await event_bus.emit(EventType.CAMPAIGN_STATUS_CHANGED, **{
            "campaign_id": campaign_id,
            "old_status": old_status,
            "new_status": new_status,
            "updated_by": self.current_user_id,
            "updated_at": datetime.utcnow().isoformat()
        })

        return self.create_success_result(campaign)
    except Exception as e:
        self.logger.exception(f"更新活动状态失败: {str(e)}")
        await self.db.rollback()
        return self.create_error_result(
            error_code=ErrorCode.OPERATION_FAILED,
            error_message=f"更新活动状态失败: {str(e)}"
        )
```

### 4. 事件处理器模板

每个事件处理器应遵循标准模板：

```python
@event_bus.on_local(EventType.CAMPAIGN_STATUS_CHANGED)
async def handle_campaign_status_changed(
    campaign_id: int,
    old_status: str,
    new_status: str,
    **kwargs
):
    """处理活动状态变更事件"""
    logger.info(f"活动 {campaign_id} 状态从 {old_status} 变更为 {new_status}")

    # 根据状态变化触发不同的后续事件
    if old_status != "active" and new_status == "active":
        # 活动激活
        await event_bus.emit(EventType.CAMPAIGN_LAUNCHED, campaign_id=campaign_id)
    elif old_status == "active" and new_status != "active":
        # 活动结束
        await event_bus.emit(EventType.CAMPAIGN_ENDED, campaign_id=campaign_id)
```

## 重构优先级业务场景

### 场景一：奖励发放流程

**当前问题**：奖励发放逻辑分散在多个地方，导致代码重复和维护困难。

**重构方案**：

1. 定义专门的奖励发放事件：

```python
REWARD_ISSUE_REQUESTED = EventType("reward.issue_requested")
```

2. 重构服务方法，使用事件触发奖励发放：

```python
async def process_user_milestone(self, user_id: int, milestone_type: str):
    """处理用户里程碑（如完成任务、邀请好友等）"""
    # 业务逻辑处理
    # ...

    # 触发奖励发放事件
    await event_bus.emit(EventType.REWARD_ISSUE_REQUESTED, **{
        "user_id": user_id,
        "milestone_type": milestone_type,
        "related_entity_id": related_id,
        "timestamp": datetime.utcnow().isoformat()
    })

    return self.create_success_result(...)
```

3. 实现统一的奖励处理器：

```python
@event_bus.on_local(EventType.REWARD_ISSUE_REQUESTED)
async def handle_reward_issue_request(
    user_id: int,
    milestone_type: str,
    related_entity_id: int,
    **kwargs
):
    """处理奖励发放请求"""
    async with get_session() as db:
        # 1. 查询适用的奖励规则
        reward_service = RewardService(db)
        reward_rules = await reward_service.get_applicable_rules(
            user_id=user_id,
            milestone_type=milestone_type
        )

        # 2. 计算奖励并创建奖励记录
        for rule in reward_rules:
            record_id = await reward_service.create_reward_record(
                user_id=user_id,
                rule_id=rule.id,
                related_entity_id=related_entity_id
            )

            # 3. 触发奖励记录创建事件
            await event_bus.emit(EventType.REWARD_RECORD_CREATED,
                                record_id=record_id,
                                user_id=user_id)
```

### 场景二：用户通知流程

**当前问题**：通知逻辑散布在各个服务中，导致通知格式不一致和代码重复。

**重构方案**：

1. 定义通知事件类型：

```python
USER_NOTIFICATION_REQUESTED = EventType("user.notification_requested")
```

2. 在各服务中触发通知事件：

```python
# 在活动服务中
await event_bus.emit(EventType.USER_NOTIFICATION_REQUESTED, **{
    "user_id": user_id,
    "notification_type": "campaign_ending",
    "channel": "email",
    "data": {
        "campaign_id": campaign_id,
        "campaign_name": campaign.name,
        "end_date": campaign.end_date.isoformat()
    }
})

# 在奖励服务中
await event_bus.emit(EventType.USER_NOTIFICATION_REQUESTED, **{
    "user_id": record.user_id,
    "notification_type": "reward_issued",
    "channel": "in_app",
    "data": {
        "reward_id": record.id,
        "reward_value": record.value,
        "reward_type": record.type
    }
})
```

3. 实现统一的通知处理器：

```python
@event_bus.on_local(EventType.USER_NOTIFICATION_REQUESTED)
async def handle_user_notification_request(
    user_id: int,
    notification_type: str,
    channel: str,
    data: Dict[str, Any],
    **kwargs
):
    """处理用户通知请求"""
    async with get_session() as db:
        notification_service = NotificationService(db)

        # 获取通知模板
        template = await notification_service.get_notification_template(
            notification_type=notification_type,
            channel=channel
        )

        # 发送通知
        await notification_service.send_notification(
            user_id=user_id,
            template_id=template.id,
            channel=channel,
            data=data
        )
```

### 场景三：数据统计更新

**当前问题**：统计逻辑与业务逻辑混合，使代码复杂化。

**重构方案**：

1. 定义统计更新事件：

```python
STATS_UPDATE_REQUESTED = EventType("stats.update_requested")
```

2. 在业务流程中触发统计更新：

```python
# 完成邀请后
await event_bus.emit(EventType.STATS_UPDATE_REQUESTED, **{
    "entity_type": "campaign",
    "entity_id": invitation.campaign_id,
    "metric_type": "invitation_completed",
    "increment_value": 1,
    "user_id": invitation.inviter_id
})

# 发放奖励后
await event_bus.emit(EventType.STATS_UPDATE_REQUESTED, **{
    "entity_type": "campaign",
    "entity_id": record.campaign_id,
    "metric_type": "reward_issued",
    "increment_value": record.value,
    "user_id": record.user_id
})
```

3. 实现统一的统计处理器：

```python
@event_bus.on_local(EventType.STATS_UPDATE_REQUESTED)
async def handle_stats_update_request(
    entity_type: str,
    entity_id: int,
    metric_type: str,
    increment_value: float,
    **kwargs
):
    """处理统计更新请求"""
    async with get_session() as db:
        stats_service = StatsService(db)

        # 更新统计数据
        await stats_service.increment_metric(
            entity_type=entity_type,
            entity_id=entity_id,
            metric_type=metric_type,
            value=increment_value,
            user_id=kwargs.get("user_id")
        )
```

## 事件设计最佳实践

1. **事件命名规范**：

    - 使用`名词.动作`格式，如`user.created`
    - 动作使用过去时态，表示已发生的事实
    - 保持命名简洁明了

2. **事件数据规范**：

    - 包含足够上下文，确保处理器能独立完成工作
    - 包含时间戳，记录事件发生时间
    - 包含用户 ID，记录操作人
    - 避免包含大量冗余数据，只传递必要信息

3. **事件处理器规范**：
    - 遵循单一职责原则
    - 妥善处理异常，不影响主流程
    - 添加充分日志，便于调试追踪
    - 尽可能实现幂等，防止重复处理

## 事务与一致性

在事件驱动架构中，保证数据一致性是关键挑战：

1. **使用数据库事务**：在事件处理器中使用事务，确保数据操作的原子性

```python
async with get_session() as db:
    try:
        # 业务逻辑
        await db.commit()
    except Exception as e:
        await db.rollback()
        logger.error(f"处理失败: {str(e)}")
```

2. **补偿事务模式**：对于分布式事务，考虑使用补偿机制

```python
@event_bus.on_local(EventType.TRANSACTION_FAILED)
async def handle_transaction_failure(
    transaction_id: str,
    operation_type: str,
    **kwargs
):
    """处理事务失败，执行补偿操作"""
    if operation_type == "reward_issue":
        await rollback_reward_issue(transaction_id)
    elif operation_type == "point_transfer":
        await reverse_point_transfer(transaction_id)
```

3. **事件溯源模式**：对关键业务，考虑使用事件溯源保存所有状态变更

## 性能考虑

1. **异步处理**：利用 FastAPI 和 asyncio 的异步特性
2. **批处理**：对于高频事件，考虑批量处理
3. **事件去重**：实现事件幂等性机制，避免重复处理
4. **延迟处理**：非核心流程可以采用延迟处理提高响应速度

## 监控与可观测性

1. **事件日志记录**：记录所有事件的发送和处理
2. **监控指标**：事件处理延迟、失败率、队列长度等
3. **分布式追踪**：使用 trace ID 关联相关事件
4. **告警机制**：对异常情况设置告警阈值

## 总结

通过采用事件驱动架构重构业务逻辑，可以有效降低系统耦合度，提高代码复用性和可维护性。将通用业
务逻辑抽取为事件处理器，不仅简化了服务实现，也为系统未来扩展奠定了灵活基础。事件成为系统中的
"通用语言"，促进了各个模块间的高效协作。
