# 服务层 Result 对象使用指南

## 简介

本文档详细说明如何在服务层中正确使用 Result 对象，以实现统一的返回值格式和标准化的错误处理
。Result 对象是一种封装操作结果的通用容器，可以包含成功的数据或错误信息。

## Result 对象概述

`Result` 是一个泛型类，用于封装任何类型的返回数据：

```python
class Result(BaseModel, Generic[T]):
    is_success: bool = Field(default=True, description="操作是否成功")
    timestamp: str = Field(default=datetime.now().isoformat(), description="时间戳")
    result_code: Optional[int] = Field(default=200, description="错误代码")
    result_msg: Optional[str] = Field(default='ok', description="错误消息")
    data: Optional[T] = Field(default=None, description="返回数据")
```

`ResultFactory` 类提供了创建各种结果实例的静态方法：

```python
class ResultFactory:
    @staticmethod
    def success(data: Optional[Any] = None) -> Result:
        # 创建成功结果

    @staticmethod
    def error(result_code: int, result_msg: str, data: Optional[Any] = None) -> Result:
        # 创建错误结果

    @classmethod
    def resource_not_found(cls, resource_type: str, resource_id: Union[str, int]) -> Result:
        # 资源不存在错误

    @classmethod
    def permission_denied(cls, resource_type: Optional[str] = None,
                          resource_id: Optional[Union[str, int]] = None) -> Result:
        # 权限不足错误

    @classmethod
    def validation_error(cls, errors: Dict[str, Any]) -> Result:
        # 验证错误
```

## 在服务类中使用 Result

### 1. 服务类定义

所有服务类应当继承 `BaseService`，并使用泛型参数指定模型类型和结果类型：

```python
class UserService(BaseService[User, Result[UserResponse]]):
    resource_type = "用户"  # 用于错误消息和资源标识
```

-   第一个泛型参数 `User` 表示服务操作的主要模型类型
-   第二个泛型参数 `Result[UserResponse]` 表示返回结果的类型

### 2. 方法返回类型

服务类中的方法应当明确声明返回类型为 `Result`：

```python
# 返回单个用户
async def get_user(self, user_id: int) -> Result[UserResponse]:
    # 方法实现...

# 返回用户列表和分页信息
async def get_users(self, params: GetUsersParams) -> Result[Dict[str, Any]]:
    # 方法实现...

# 返回操作状态
async def delete_user(self, params: DeleteUserParams) -> Result[Dict[str, Any]]:
    # 方法实现...
```

### 3. 创建成功结果

使用 `create_success_result` 方法创建成功结果：

```python
def create_success_result(self, data: Optional[Any] = None) -> ResultType:
    return cast(ResultType, self.result_factory.success(data))
```

示例：

```python
async def get_user(self, user_id: int) -> Result[UserResponse]:
    user = await self.get_resource_by_id(user_id)
    if not user:
        return self.resource_not_found_result(user_id)

    # 业务逻辑...
    user_response = UserResponse.model_validate(user.to_dict())
    return self.create_success_result(user_response)
```

对于列表结果：

```python
async def get_users(self, params: GetUsersParams) -> Result[Dict[str, Any]]:
    # 查询用户列表
    users, total = await User.get_users(self.db, **params.to_filters())

    # 构建响应数据
    users_data = [UserResponse.model_validate(user.to_dict()) for user in users]
    result_data = {
        "items": users_data,
        "total": total,
        "page": params.page,
        "page_size": params.page_size
    }

    return self.create_success_result(result_data)
```

### 4. 创建错误结果

使用 `create_error_result` 方法创建错误结果：

```python
def create_error_result(
    self,
    error_code: int,
    error_message: str,
    data: Optional[Any] = None
) -> ResultType:
    return cast(ResultType, self.result_factory.error(error_code, error_message, data))
```

示例：

```python
async def create_user(self, params: CreateUserParams) -> Result[UserResponse]:
    # 检查邮箱是否已存在
    existing_user = await User.get_by_email(self.db, params.email)
    if existing_user:
        return self.create_error_result(
            error_code=ErrorCode.USER_EXISTS,
            error_message=f"邮箱 {params.email} 已被使用"
        )

    # 业务逻辑...
```

### 5. 特定错误结果

BaseService 提供了一些常用的错误结果方法：

#### 资源不存在

```python
def resource_not_found_result(self, resource_id: Union[str, int]) -> ResultType:
    return cast(
        ResultType,
        self.result_factory.resource_not_found(
            resource_type=self.resource_type,
            resource_id=resource_id
        )
    )
```

示例：

```python
user = await self.get_resource_by_id(user_id)
if not user:
    return self.resource_not_found_result(user_id)
```

#### 权限不足

```python
def permission_denied_result(
    self,
    resource_id: Optional[Union[str, int]] = None
) -> ResultType:
    return cast(
        ResultType,
        self.result_factory.permission_denied(
            resource_type=self.resource_type,
            resource_id=resource_id
        )
    )
```

示例：

```python
if not user.can_access(current_user_id):
    return self.permission_denied_result(user.id)
```

## 处理嵌套服务调用

当一个服务需要调用另一个服务时，应当检查结果对象并适当处理：

```python
async def process_subscription(self, user_id: int, plan_id: int) -> Result[Dict[str, Any]]:
    # 获取用户
    user_result = await self.user_service.get_user(user_id)
    if not user_result.is_success:
        return user_result  # 直接返回错误结果

    user = user_result.data

    # 获取计划
    plan_result = await self.plan_service.get_plan(plan_id)
    if not plan_result.is_success:
        return plan_result  # 直接返回错误结果

    plan = plan_result.data

    # 执行订阅处理逻辑...
```

## 处理异常

服务方法应当捕获并处理可预见的异常，转换为适当的 Result 对象：

```python
async def create_payment(self, params: CreatePaymentParams) -> Result[PaymentResponse]:
    try:
        # 业务逻辑...
        return self.create_success_result(payment_response)
    except PaymentGatewayError as e:
        self.logger.error(f"支付网关错误: {str(e)}", exc_info=True)
        return self.create_error_result(
            error_code=ErrorCode.PAYMENT_FAILED,
            error_message=f"支付处理失败: {str(e)}"
        )
    except ValidationError as e:
        self.logger.warning(f"验证错误: {str(e)}")
        return self.create_error_result(
            error_code=ErrorCode.VALIDATION_ERROR,
            error_message="数据验证失败",
            data=format_validation_errors(e)
        )
    except Exception as e:
        self.logger.exception(f"创建支付记录时发生未知错误: {str(e)}")
        return self.create_error_result(
            error_code=ErrorCode.SYSTEM_ERROR,
            error_message="系统处理异常，请稍后重试"
        )
```

## 处理验证错误

处理 Pydantic 验证错误时，应当提供用户友好的错误信息：

```python
def format_validation_errors(error: ValidationError) -> Dict[str, List[str]]:
    """将Pydantic验证错误转换为用户友好的格式"""
    formatted_errors = {}
    for err in error.errors():
        # 获取字段名
        if err["loc"]:
            field = ".".join(str(loc) for loc in err["loc"])
        else:
            field = "非字段错误"

        # 获取错误信息
        message = err["msg"]

        # 添加到错误字典
        if field not in formatted_errors:
            formatted_errors[field] = []
        formatted_errors[field].append(message)

    return formatted_errors
```

使用例子：

```python
try:
    user_data = UserCreate.model_validate(data)
except ValidationError as e:
    return self.create_error_result(
        error_code=ErrorCode.VALIDATION_ERROR,
        error_message="输入数据无效",
        data=format_validation_errors(e)
    )
```

## 服务层方法设计模式

### 1. 基本操作模式

```python
async def operation(self, params) -> Result[ResponseType]:
    try:
        # 1. 验证资源存在
        resource = await self.get_resource_by_id(resource_id)
        if not resource:
            return self.resource_not_found_result(resource_id)

        # 2. 检查权限
        if not can_access(current_user, resource):
            return self.permission_denied_result(resource_id)

        # 3. 验证业务规则
        if not is_valid_operation(resource, params):
            return self.create_error_result(
                error_code=ErrorCode.INVALID_OPERATION,
                error_message="操作无效，因为..."
            )

        # 4. 执行业务逻辑
        result = await perform_operation(resource, params)

        # 5. 返回成功结果
        return self.create_success_result(result)

    except Exception as e:
        # 6. 处理异常
        self.logger.error(f"操作失败: {str(e)}", exc_info=True)
        return self.create_error_result(
            error_code=ErrorCode.OPERATION_FAILED,
            error_message="操作失败，请稍后重试"
        )
```

### 2. 分页列表模式

```python
async def get_resources(self, params: QueryParams) -> Result[Dict[str, Any]]:
    try:
        # 构建过滤条件
        filters = build_filters(params)

        # 执行查询
        items, total = await self.repository.get_many(
            self.db,
            skip=params.skip,
            limit=params.limit,
            filters=filters,
            order_by=params.order_by,
            order_desc=params.order_desc
        )

        # 转换为响应格式
        items_data = [ItemResponse.model_validate(item.to_dict()) for item in items]

        # 构建返回数据
        result_data = {
            "items": items_data,
            "total": total,
            "page": params.page,
            "page_size": params.page_size
        }

        return self.create_success_result(result_data)

    except Exception as e:
        self.logger.error(f"获取资源列表失败: {str(e)}", exc_info=True)
        return self.create_error_result(
            error_code=ErrorCode.OPERATION_FAILED,
            error_message="获取资源列表失败，请稍后重试"
        )
```

### 3. 缓存模式

```python
async def get_resource(self, resource_id: int) -> Result[ResourceResponse]:
    try:
        # 尝试从缓存获取
        cache_key = self._get_resource_cache_key(resource_id)
        cached_resource = await self.get_cached_resource(
            cache_key,
            lambda data: Resource.model_validate(data)
        )

        if cached_resource:
            return self.create_success_result(ResourceResponse.from_orm(cached_resource))

        # 缓存未命中，从数据库获取
        resource = await self.get_resource_by_id(resource_id)
        if not resource:
            return self.resource_not_found_result(resource_id)

        # 缓存资源
        await self.cache_resource(cache_key, resource)

        # 返回结果
        return self.create_success_result(ResourceResponse.from_orm(resource))

    except Exception as e:
        self.logger.error(f"获取资源失败: {str(e)}", exc_info=True)
        return self.create_error_result(
            error_code=ErrorCode.OPERATION_FAILED,
            error_message="获取资源失败，请稍后重试"
        )
```

## 服务层调用示例

### 在路由层使用服务

```python
@router.get("/{resource_id}", response_model=Result[ResourceResponse])
@handle_route_errors(RESOURCE_ERROR_MAPPING)
async def get_resource(
    resource_id: int,
    service: ResourceService = Depends(get_resource_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[ResourceResponse]:
    return await service.get_resource(resource_id)
```

### 在其他服务中使用服务

```python
@inject
class OrderService(BaseService[Order, Result[OrderResponse]]):
    def __init__(
        self,
        db: AsyncSession,
        user_service: UserService,
        product_service: ProductService,
        redis: Optional[Redis] = None
    ):
        super().__init__(db, redis)
        self.user_service = user_service
        self.product_service = product_service

    async def create_order(self, params: CreateOrderParams) -> Result[OrderResponse]:
        # 验证用户
        user_result = await self.user_service.get_user(params.user_id)
        if not user_result.is_success:
            return user_result

        # 验证产品
        product_result = await self.product_service.get_product(params.product_id)
        if not product_result.is_success:
            return product_result

        # 创建订单逻辑...
```

## 最佳实践总结

1. 所有服务类继承 `BaseService[ModelType, ResultType]`
2. 所有服务方法返回 `Result` 对象
3. 使用 `create_success_result` 和 `create_error_result` 创建结果
4. 捕获并处理可预见的异常
5. 在调用其他服务时，检查结果对象
6. 提供用户友好的错误消息
7. 在返回错误时记录适当的日志
8. 使用 `ErrorCode` 枚举类提供统一的错误码
9. 当服务方法返回 `Result[SomeType]` 时，成功结果的 `data` 字段应当是 `SomeType` 类型
