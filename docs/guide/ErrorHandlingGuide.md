# 错误处理标准化指南

## 简介

本文档定义了项目中错误处理的标准化方法，旨在确保系统中所有模块以一致的方式处理和返回错误，提
高代码可维护性和用户体验。

## 核心原则

1. **统一性**：所有服务层方法必须返回标准的`Result`对象
2. **明确性**：错误信息应当准确描述问题，并尽可能提供解决方案
3. **分层性**：在适当的层级处理错误，服务层负责业务逻辑错误，路由层负责输入验证和权限错误
4. **友好性**：向最终用户展示友好的错误信息，同时记录足够的技术细节用于调试
5. **可追踪性**：所有错误都应当被记录，并附带足够的上下文信息用于问题定位

## 错误处理架构

### 错误类型

系统中错误可以分为以下几种主要类型：

1. **输入验证错误**：由 Pydantic 验证器或业务逻辑验证产生的错误
2. **业务规则错误**：业务流程无法继续的情况，例如资源不存在、状态不允许操作等
3. **权限错误**：用户没有足够的权限执行操作
4. **系统错误**：内部系统组件失败，如数据库连接问题、缓存错误等
5. **第三方服务错误**：外部服务调用失败

### 错误码体系

项目使用`ErrorCode`枚举类来定义统一的错误码，按不同领域分组：

-   1000-1999：通用错误
-   2000-2999：认证授权错误
-   3000-3999：用户相关错误
-   4000-4999：营销模块错误
-   5000-5999：账单模块错误
-   6000-6999：系统模块错误
-   7000-7999：API 错误

每个错误码都应当有一个明确的含义，并且在`ErrorCode.get_message`方法中定义默认的错误消息。

## 标准实践

### 服务层错误处理

服务层是处理业务逻辑错误的主要场所。所有服务类都应当继承`BaseService`，并使用其提供的错误处
理方法：

```python
class UserService(BaseService[User, Result[UserResponse]]):
    resource_type = "用户"

    async def get_user(self, user_id: int) -> Result[UserResponse]:
        user = await self.get_resource_by_id(user_id)
        if not user:
            return self.resource_not_found_result(user_id)

        # 业务逻辑...
        return self.create_success_result(user_response)
```

主要方法包括：

-   `create_success_result(data)`: 创建成功结果
-   `create_error_result(error_code, error_message, data)`: 创建错误结果
-   `resource_not_found_result(resource_id)`: 资源不存在结果
-   `permission_denied_result(resource_id)`: 权限不足结果

### 路由层错误处理

路由层应当使用`handle_route_errors`装饰器处理异常，确保所有异常都被转换为适当的 HTTP 响应：

```python
@router.get("/{user_id}", response_model=Result[UserResponse])
@handle_route_errors(USER_ERROR_MAPPING)
async def get_user(
    user_id: int,
    user_service: UserService = Depends(get_user_service)
) -> Result[UserResponse]:
    return await user_service.get_user(user_id)
```

或者使用`ResultHandler`依赖项：

```python
@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    user_service: UserService = Depends(get_user_service),
    result_handler: Callable = Depends(ResultHandler(USER_ERROR_MAPPING))
) -> UserResponse:
    result = await user_service.get_user(user_id)
    return result_handler(result)  # 直接返回data或抛出异常
```

### 验证错误处理

使用 Pydantic 进行输入验证，并在处理验证错误时提供友好的错误信息：

```python
try:
    user_data = UserCreate.model_validate(data)
except ValidationError as e:
    return self.create_error_result(
        error_code=ErrorCode.VALIDATION_ERROR,
        error_message="输入数据无效",
        data=format_validation_errors(e)  # 转换为用户友好的格式
    )
```

## 错误响应格式

所有 HTTP 错误响应应当遵循以下格式：

```json
{
  "isSuccess": false,
  "timestamp": "2023-09-15T12:34:56.789Z",
  "resultCode": 1000,
  "resultMsg": "请求的资源不存在",
  "data": {
    "details": "附加的错误详情"
  }
}
```

成功响应格式：

```json
{
  "isSuccess": true,
  "timestamp": "2023-09-15T12:34:56.789Z",
  "resultCode": 200,
  "resultMsg": "ok",
  "data": {
    "id": 1,
    "name": "示例数据"
  }
}
```

## 日志记录

错误处理应当包括适当的日志记录：

1. **警告级别**：记录业务规则错误和验证错误
2. **错误级别**：记录系统错误和未处理的异常
3. **关键级别**：记录安全相关错误和严重的系统故障

```python
try:
    # 业务逻辑
except DatabaseError as e:
    self.logger.error(f"数据库操作失败: {str(e)}", exc_info=True)
    return self.create_error_result(
        error_code=ErrorCode.DATABASE_ERROR,
        error_message="数据库操作失败，请稍后重试"
    )
```

## 错误处理检查清单

在实现新功能或修改现有功能时，使用以下检查清单确保符合错误处理标准：

-   [ ] 服务方法返回`Result`对象
-   [ ] 使用`BaseService`提供的方法创建错误结果
-   [ ] 路由使用`handle_route_errors`装饰器或`ResultHandler`依赖项
-   [ ] 为每种错误情况提供明确、用户友好的错误消息
-   [ ] 正确记录错误日志，包含足够的上下文信息
-   [ ] 验证错误转换为用户友好的格式
-   [ ] 使用最合适的 HTTP 状态码映射
-   [ ] 确保所有异常都被处理，不会泄露到客户端

## 常见问题与解决方案

### 1. 如何处理嵌套服务调用中的错误？

在服务 A 调用服务 B 的情况下，服务 B 应返回`Result`对象，服务 A 应检查结果并决定是否继续处理
：

```python
result = await service_b.operation()
if not result.is_success:
    return result  # 直接返回错误结果

# 继续处理...
```

### 2. 如何处理批量操作中的错误？

批量操作应收集所有错误，并在完成后返回：

```python
errors = []
for item in items:
    try:
        # 处理单个项目
    except Exception as e:
        errors.append({"id": item.id, "error": str(e)})

if errors:
    return self.create_error_result(
        error_code=ErrorCode.BATCH_OPERATION_PARTIAL_FAILURE,
        error_message="部分操作失败",
        data={"failed_items": errors}
    )
```

### 3. 如何处理异步操作中的错误？

对于后台任务，应记录错误并更新任务状态：

```python
try:
    # 异步操作
except Exception as e:
    self.logger.error(f"后台任务失败: {str(e)}", exc_info=True)
    await task.update_status("failed", error=str(e))
```

## 最佳实践示例

### 服务层示例

```python
async def update_user(self, params: UpdateUserParams) -> Result[UserResponse]:
    """更新用户信息"""
    try:
        # 验证用户存在
        user = await self.get_resource_by_id(params.user_id)
        if not user:
            return self.resource_not_found_result(params.user_id)

        # 检查权限
        if not self.can_update_user(params.current_user_id, user):
            return self.permission_denied_result(params.user_id)

        # 检查业务规则
        if params.email and await User.exists_with_email(self.db, params.email, exclude_id=params.user_id):
            return self.create_error_result(
                error_code=ErrorCode.USER_EXISTS,
                error_message=f"邮箱 {params.email} 已被使用"
            )

        # 执行更新
        user.name = params.name if params.name is not None else user.name
        user.email = params.email if params.email is not None else user.email
        # ... 其他字段更新

        await user.save(self.db)

        # 返回成功结果
        return self.create_success_result(UserResponse.from_orm(user))

    except Exception as e:
        self.logger.error(f"更新用户失败: {str(e)}", exc_info=True)
        return self.create_error_result(
            error_code=ErrorCode.OPERATION_FAILED,
            error_message="更新用户失败，请稍后重试"
        )
```

### 路由层示例

```python
@router.put("/{user_id}", response_model=Result[UserResponse])
@handle_route_errors(USER_ERROR_MAPPING)
async def update_user(
    user_update: UserUpdate,
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service)
) -> Result[UserResponse]:
    params = UpdateUserParams(
        user_id=user_id,
        current_user_id=current_user.id,
        **user_update.model_dump()
    )
    return await user_service.update_user(params)
```
