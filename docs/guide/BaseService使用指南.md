# BaseService 使用指南

## 概述

`BaseService`是 FastAPI Nano 框架中的核心组件，提供服务层统一的基础设施和功能。通过继
承`BaseService`，可以减少重复代码，统一错误处理和结果返回方式，提高代码的可维护性。

## 基本用法

### 1. 继承 BaseService

```python
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis

from svc.core.services import BaseService, Result
from svc.apps.your_module.models.your_model import YourModel
from svc.apps.your_module.schemas.base import YourResult

class YourService(BaseService[YourModel, YourResult]):
    """您的服务类"""

    # 设置资源类型名称（用于错误信息）
    resource_type = "您的资源"

    def __init__(self, db: AsyncSession, redis: Optional[Redis] = None):
        """初始化服务"""
        super().__init__(db, redis)
```

### 2. 实现 get_resource_by_id 方法

```python
async def get_resource_by_id(self, resource_id: int) -> Optional[YourModel]:
    """获取资源对象

    此方法需要由子类实现，用于check_resource_exists装饰器

    Args:
        resource_id: 资源ID

    Returns:
        资源对象或None
    """
    return await YourModel.get_by_id(self.db, resource_id)
```

### 3. 使用错误处理装饰器

```python
from svc.core.services.base import handle_service_errors

@handle_service_errors(error_code="GET_FAILED")
async def get_item(self, item_id: int) -> YourResult:
    """获取项目

    Args:
        item_id: 项目ID

    Returns:
        结果对象
    """
    item = await self.get_resource_by_id(item_id)
    if not item:
        return self.resource_not_found_result(item_id)

    return self.create_success_result(item)
```

### 4. 使用资源检查装饰器

```python
from svc.core.services.base import check_resource_exists

@check_resource_exists(id_param="item_id", error_code="ITEM_NOT_FOUND")
async def update_item(self, item_id: int, data: dict, **kwargs) -> YourResult:
    """更新项目

    Args:
        item_id: 项目ID
        data: 更新数据

    Returns:
        结果对象
    """
    # 资源对象会被装饰器自动添加到kwargs中
    item = kwargs.get("resource")

    # 更新逻辑
    await item.update(self.db, **data)

    return self.create_success_result(item)
```

## 返回结果

### 创建成功结果

```python
# 返回没有数据的成功结果
return self.create_success_result()

# 返回带数据的成功结果
return self.create_success_result(item)
```

### 创建错误结果

```python
# 返回一般错误
return self.create_error_result(
    error_code="OPERATION_FAILED",
    error_message="操作失败的原因"
)

# 返回资源不存在错误
return self.resource_not_found_result(resource_id)

# 返回权限不足错误
return self.permission_denied_result(resource_id)
```

## 日志记录

BaseService 自动配置了 logger，可以直接使用：

```python
# 记录调试日志
self.logger.debug(f"处理请求: item_id={item_id}")

# 记录信息日志
self.logger.info(f"项目创建成功: id={item.id}")

# 记录警告日志
self.logger.warning(f"发现潜在问题: {warning_message}")

# 记录错误日志
self.logger.error(f"操作失败: {error_message}", exc_info=True)
```

## 缓存使用

如果在初始化时提供了 Redis 实例，可以使用它进行缓存操作：

```python
async def get_cached_item(self, item_id: int) -> Optional[Item]:
    """获取带缓存的项目"""
    # 检查缓存
    if self.redis:
        cache_key = f"item:{item_id}"
        cached = await self.redis.get(cache_key)
        if cached:
            return Item.parse_raw(cached)

    # 缓存未命中，从数据库获取
    item = await self.get_resource_by_id(item_id)

    # 设置缓存
    if item and self.redis:
        cache_key = f"item:{item_id}"
        await self.redis.set(
            cache_key,
            item.json(),
            ex=3600  # 过期时间（秒）
        )

    return item
```

## 事件发布

对于需要触发事件的场景，使用事件总线：

```python
from svc.core.events import event_bus, EventType

async def create_item(self, data: dict) -> YourResult:
    """创建项目"""
    # 创建项目
    item = await YourModel.create(self.db, **data)

    # 触发事件
    await event_bus.emit(
        EventType.ITEM_CREATED,
        item_id=item.id,
        creator_id=data.get("creator_id")
    )

    return self.create_success_result(item)
```

## 最佳实践

1. **总是继承 BaseService**：所有业务服务类都应该继承 BaseService
2. **实现 get_resource_by_id**：确保正确实现此方法以支持资源检查装饰器
3. **使用标准结果对象**：使用 create_success_result 和 create_error_result 方法返回结果
4. **统一错误处理**：使用 handle_service_errors 装饰器处理异常
5. **适当使用日志**：使用 self.logger 记录关键操作和错误
6. **规范化注释**：使用规范的文档字符串记录参数和返回值
