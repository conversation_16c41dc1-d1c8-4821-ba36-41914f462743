# AItools

<div align="center">
  <h3>轻量级高性能微服务框架</h3>
  <p>基于FastAPI构建的企业级开发框架，集成认证授权和事件驱动功能</p>
</div>

## 目录

-   [项目概述](#项目概述)
-   [核心优势](#核心优势)
-   [功能一览](#功能一览)
-   [设计模式与架构](#设计模式与架构)
-   [编码规范](#编码规范)
-   [快速上手](#快速上手)
-   [项目结构](#项目结构)
-   [开发指南](#开发指南)
-   [性能优化](#性能优化)
-   [国际化支持](#国际化支持)
-   [CI/CD 与部署](#cicd与部署)
-   [API 文档](#api文档)
-   [常见问题](#常见问题)
-   [许可证](#许可证)
-   [安全和依赖管理](#安全和依赖管理)

## 项目概述

AItools 是一个轻量级微服务框架，基于 FastAPI 构建，提供完整的认证授权和事件驱动功能。它专为
快速开发高性能、可扩展的微服务应用而设计，集成了现代 Web 应用所需的核心组件。

## 核心优势

-   ⚡️ **高性能**: 基于 FastAPI 和异步编程，提供卓越的性能表现
-   🔒 **安全可靠**: 内置 JWT 认证和基于角色的权限控制
-   🔌 **即插即用**: 模块化设计，支持自动路由注册和热配置加载
-   📊 **全面监控**: 集成 Prometheus 和 Grafana，实时监控系统性能
-   🚀 **DevOps 就绪**: 完整 CI/CD 流程，支持自动化测试、构建和部署
-   🌍 **国际化支持**: 内置多语言切换和本地化功能
-   🔍 **性能优化**: 多级缓存、查询优化和异步任务处理

## 功能一览

| 模块             | 状态 | 描述                                         |
| ---------------- | ---- | -------------------------------------------- |
| **核心基础设施** | ✅   | 配置管理、异步数据库会话、认证授权、事件系统 |
| **用户管理**     | ✅   | 用户 CRUD、角色分配、权限控制                |
| **账单管理**     | ✅   | 订阅管理、账单生成、支付集成                 |
| **营销模块**     | ✅   | 活动管理、邀请关系、奖励发放                 |
| **系统管理**     | ✅   | 审计日志、监控指标、健康检查                 |
| **测试和部署**   | ✅   | 单元测试、集成测试、CI/CD 配置               |
| **性能优化**     | ✅   | 查询优化、缓存策略、异步任务                 |
| **国际化支持**   | ✅   | 多语言支持、本地化、翻译管理                 |

## 设计模式与架构

AItools 框架采用了多种现代化设计模式，旨在提供高度可维护、可扩展和高性能的应用架构。

### 1. 依赖注入模式 (Dependency Injection)

通过 FastAPI 的 Depends 机制实现组件间松散耦合，提高可测试性和可维护性。

```python
# 依赖注入函数定义
async def get_user_service(
    db: AsyncSession = Depends(get_db),
    redis: Redis = Depends(get_redis)
) -> UserService:
    return UserService(db, redis)

# 在路由中使用
@router.get("/users/{user_id}")
async def get_user(
    user_id: int,
    service: UserService = Depends(get_user_service),
    current_user: User = Depends(get_current_user)
):
    return await service.get_user(user_id)
```

### 2. 服务层模式 (Service Layer)

使用泛型基类封装业务逻辑，统一错误处理和结果返回。

```python
class BaseService(Generic[ModelType, ResultType]):
    """服务基类，提供通用功能和错误处理"""

    resource_type: str = "资源"

    def __init__(self, db: AsyncSession, redis: Optional[Redis] = None):
        self.db = db
        self.redis = redis
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

    async def get_resource_by_id(self, resource_id: Union[str, int]) -> Optional[ModelType]:
        """获取指定ID的资源，子类应该覆盖此方法"""
        raise NotImplementedError("子类必须实现get_resource_by_id方法")
```

### 3. 事件驱动模式 (Event-Driven)

使用事件总线实现组件间的解耦通信，支持本地和远程事件处理。

```python
# 定义事件处理器
@event_bus.on_local("user.created")
async def handle_user_created(user_id: int, **kwargs):
    # 处理用户创建事件
    print(f"用户已创建: {user_id}")

# 发布事件
await event_bus.emit("user.created", user_id=123)
```

### 4. 中间件模式 (Middleware)

基于责任链模式实现可扩展、可配置的请求处理流程。

```python
class RequestLoggingMiddleware(BaseMiddleware):
    """请求日志记录中间件"""

    priority = 10  # 优先级设置

    async def __call__(self, request: Request, call_next) -> Response:
        start_time = time.time()

        # 请求前处理
        self.log_request(request)

        # 调用下一个中间件
        response = await call_next(request)

        # 请求后处理
        process_time = time.time() - start_time
        self.log_response(response, process_time)

        return response
```

### 5. 工厂模式 (Factory Pattern)

使用工厂类创建统一的结果对象和其他复杂组件。

```python
class ResultFactory:
    """结果对象工厂，用于创建各种结果实例"""

    @staticmethod
    def success(data: Optional[Any] = None) -> Result:
        """创建成功结果"""
        return Result(is_success=True, data=data)

    @staticmethod
    def error(result_code: int, result_msg: str, data: Optional[Any] = None) -> Result:
        """创建错误结果"""
        return Result(is_success=False, result_code=result_code, result_msg=result_msg, data=data)
```

## 编码规范

### 1. 代码组织结构

-   按功能模块划分目录，每个模块包含标准的子目录结构
-   核心基础设施集中在 core 目录
-   业务模块放置在 apps 目录

```
app_module/
├── models/          # 数据模型定义
|—— repositories     # 数据访问
├── services/        # 业务逻辑实现
├── schemas/         # 数据验证和转换
├── routers/         # API路由定义
├── utils/           # 工具函数
├── dependencies.py  # 依赖注入
└── __init__.py      # 模块导出
```

### 2. 命名约定

-   类名使用 PascalCase（如 BaseService）
-   函数和变量使用 snake_case（如 get_current_user）
-   常量使用 UPPER_SNAKE_CASE（如 CACHE_VERSION）
-   私有方法和属性前缀下划线（如\_emit_local）
-   目录和文件使用小写下划线命名（如 user_service.py）

### 3. 类型提示使用

-   所有函数参数和返回值添加类型提示
-   使用泛型表示类型关系（如 Generic[ModelType, ResultType]）
-   使用 Optional 表示可选值，Union 表示多种可能类型
-   支持 Pydantic 模型进行数据验证和类型转换

```python
async def get_user(user_id: int, tenant_id: Optional[str] = None) -> Optional[User]:
    """获取用户信息

    Args:
        user_id: 用户ID
        tenant_id: 租户ID（可选）

    Returns:
        User: 用户对象，不存在则返回None
    """
    # 实现逻辑
    pass
```

### 4. 异步编程风格

-   所有 I/O 操作使用异步函数
-   使用 async/await 关键字
-   避免在异步代码中使用阻塞操作
-   数据库操作使用异步 SQLAlchemy

```python
async def update_user(self, user_id: int, data: dict) -> User:
    """异步更新用户信息"""
    user = await self.get_resource_by_id(user_id)
    if not user:
        return None

    # 使用异步更新
    for key, value in data.items():
        setattr(user, key, value)

    self.db.add(user)
    await self.db.commit()
    await self.db.refresh(user)

    return user
```

### 5. 错误处理规范

-   使用统一的错误码和错误消息
-   服务层返回统一的 Result 对象
-   控制器层使用统一的异常处理装饰器
-   详细的错误日志记录

```python
# 服务层错误处理
try:
    # 业务逻辑
    return self.create_success_result(data)
except Exception as e:
    self.logger.error(f"处理失败: {str(e)}", exc_info=True)
    return self.create_error_result(
        result_code=ErrorCode.OPERATION_FAILED,
        result_msg=f"操作失败: {str(e)}"
    )

# 控制器层错误处理
@router.post("/resources", response_model=ResourceResponse)
@handle_route_errors(RESOURCE_ERROR_MAPPING)
async def create_resource(
    data: ResourceCreate,
    service: ResourceService = Depends(get_resource_service)
):
    return await service.create_resource(data)
```

## 快速上手

### 安装

```bash
# 克隆仓库
git clone <repository-url>
cd aitools

# 安装依赖（使用uv，更快的Python包管理工具）
uv sync
```

### 运行

```bash
# 本地开发
make run-local

# Docker部署
make run-container
```

### 访问

-   API 文档：http://localhost:8000/docs
-   监控面板：http://localhost:3000

## 项目结构

```
svc/
├── main.py              # 应用入口
├── core/                # 核心基础设施
│   ├── config/          # 配置管理
│   ├── database/        # 数据库模块
│   ├── security/        # 安全模块
│   ├── events/          # 事件系统
│   ├── middleware/      # 中间件
│   ├── i18n/            # 国际化支持
│   ├── cache/           # 缓存管理
│   ├── exceptions/      # 异常处理
│   └── utils/           # 核心工具函数
├── apps/                # 业务模块
│   ├── auth/            # 认证授权
│   │   ├── models/      # 数据模型
│   │   ├── schemas/     # Pydantic模型
│   │   ├── services/    # 服务层
│   │   ├── routers/     # API路由
│   │   ├── utils/       # 工具函数
│   │   ├── __init__.py  # 模块导出
│   │   └── dependencies.py # 依赖注入
│   ├── system/          # 系统管理
│   ├── billing/         # 账单管理
│   └── marketing/       # 营销模块
├── utils/               # 通用工具
│   └── router_factory.py # 路由自动注册工具
├── tests/               # 测试套件
│   ├── unit/            # 单元测试
│   └── integration/     # 集成测试
└── docs/                # 项目文档
```

## 开发指南

### 添加新模块

1. 在`apps`目录下创建新模块目录
2. 创建子目录：`routers`, `services`, `models`, `schemas`, `utils`
3. 创建`dependencies.py`和`__init__.py`文件
4. 按照统一的架构模式实现模块功能
5. 系统会自动注册路由

```python
# apps/your_module/routers/your_router.py
from fastapi import APIRouter

router = APIRouter()

@router.get("/")
async def get_items():
    return {"items": []}
```

### 路由注册

项目使用自动路由注册机制，系统启动时会自动扫描所有模块并注册路由：

```python
# 创建路由器实例
router = APIRouter(
    prefix="/your-prefix",
    tags=["your-tag"],
    responses={404: {"description": "Not found"}}
)

# 定义路由处理器
@router.get("/")
async def get_items():
    return {"items": []}
```

路由自动注册由`utils/router_factory.py`实现，它会搜索 apps 目录下各模块的 routers 目录，并自
动注册发现的路由。

### 认证和授权

```python
# 路由中使用认证
from fastapi import Depends
from svc.core.security.auth import get_current_user

@router.get("/protected")
async def protected_route(user = Depends(get_current_user)):
    return {"message": f"Hello, {user.username}"}
```

## 性能优化

AItools 提供了全面的性能优化工具，帮助您构建高性能的应用。

### 缓存策略

框架实现了多级缓存机制，包括内存缓存、Redis 缓存和 HTTP 缓存控制：

#### 函数级缓存

使用`cached`装饰器对函数结果进行缓存：

```python
from svc.core.cache import cached, invalidate_cache

class UserService:
    @staticmethod
    @cached(prefix="user", expire=300, tenant_param="tenant_id")  # 缓存5分钟，支持租户隔离
    async def get_user(db, user_id, tenant_id=None):
        # 数据库查询逻辑
        return user

    @staticmethod
    @invalidate_cache("user:*", tenant_param="tenant_id")  # 使缓存失效，支持租户隔离
    async def update_user(db, user_id, data, tenant_id=None):
        # 更新用户
        # ...
        return updated_user
```

#### 资源级缓存

对于资源对象，使用`ResourceCache`类或`resource_cached`装饰器提供更高级的缓存管理：

```python
from svc.core.cache import ResourceCache, resource_cached
from pydantic import BaseModel

# 资源模型
class UserModel(BaseModel):
    id: int
    name: str
    email: str
    tenant_id: str

class UserService:
    def __init__(self, db, redis):
        self.db = db
        self.redis = redis
        # 创建资源缓存实例
        self.cache = ResourceCache(
            resource_type="user",
            model_class=UserModel,
            expire=1800  # 30分钟过期
        )

    # 手动管理缓存
    async def get_user(self, user_id: int, tenant_id: str):
        # 尝试从缓存获取
        cached_user = await self.cache.get(user_id, tenant_id)
        if cached_user:
            return cached_user

        # 从数据库查询
        user = await self.db.fetch_one(...)
        if user:
            user_model = UserModel(**user)
            # 设置缓存
            await self.cache.set(user_id, user_model, tenant_id)
            return user_model
        return None

    # 使用装饰器自动缓存
    @resource_cached("user", UserModel)
    async def get_user_by_id(self, db, id: int, tenant_id: str = None):
        # 此函数的结果会自动缓存
        user = await db.fetch_one(...)
        return UserModel(**user) if user else None
```

### 异步任务处理

对于耗时操作，使用异步任务处理以避免阻塞主线程：

```python
from svc.core.events import event_bus

async def process_registration(user_id):
    # 处理用户注册后的任务
    # 发送事件
    await event_bus.emit("user.registered", {"user_id": user_id})
```

## 国际化支持

AItools 提供完善的国际化支持，让您的应用轻松实现多语言功能。

### 多语言支持

```python
from svc.core.i18n import I18nMiddleware, _

app = FastAPI()
app.add_middleware(I18nMiddleware, supported_languages=["en", "zh-CN", "ja"])

@app.get("/greeting")
async def greeting():
    return {"message": _("Hello World")}
```

### 切换语言

-   基于 Accept-Language 请求头自动识别客户端语言偏好
-   支持通过查询参数覆盖语言设置：`?lang=zh-CN`
-   用户可在个人设置中保存语言偏好

## CI/CD 与部署

### 环境配置

项目支持多环境配置：

-   本地开发环境：`.env.local`
-   开发环境：`.env.development`
-   测试环境：`.env.test`
-   生产环境：`.env`

### Docker 部署

项目提供了完整的 Docker 支持：

```bash
# 构建镜像
docker build -t fastapi-nano .

# 使用Docker Compose启动
docker-compose up -d
```

## API 文档

AItools 提供了详细的 API 文档，可通过以下方式访问：

1. **Swagger UI**: 运行应用后访问`/docs`
2. **ReDoc**: 运行应用后访问`/redoc`

## 常见问题

## 许可证

MIT

## 安全和依赖管理

AItools 提供了一系列工具来确保代码安全和依赖管理：

### 安全检查

```bash
# 执行所有安全检查
python scripts/run_security_checks.py --all

# 检测代码库中的安全问题
python scripts/security_checker.py
```

### 依赖管理

```bash
# 检测并更新依赖
python scripts/update_dependencies.py

# 安装预提交钩子
cp scripts/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit
```

更多详细信息请参阅[安全工具使用指南](docs/security_tools_guide.md)。
