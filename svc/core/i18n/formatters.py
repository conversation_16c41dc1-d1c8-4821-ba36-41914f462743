"""
格式化工具。

提供本地化相关的格式化功能，包括日期、时间、数字和货币等。
"""

from datetime import datetime
from typing import Optional, Union
import locale
import babel.numbers
import babel.dates
from babel.core import Locale

from .translation import get_current_language

def _get_locale(language: Optional[str] = None) -> Locale:
    """
    根据语言代码获取Babel的Locale对象。
    
    Args:
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        Babel的Locale对象
    """
    if language is None:
        language = get_current_language()
    
    # 转换语言代码为Babel支持的格式
    if language == "zh-CN":
        return Locale.parse("zh_CN")
    elif language == "en":
        return Locale.parse("en_US")
    elif language == "ja":
        return Locale.parse("ja_JP")
    else:
        return Locale.parse(language.replace("-", "_"))

def format_number(
    number: Union[int, float],
    decimal_places: int = 2,
    language: Optional[str] = None
) -> str:
    """
    格式化数字。
    
    Args:
        number: 要格式化的数字
        decimal_places: 小数位数
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的数字字符串
    """
    locale_obj = _get_locale(language)
    return babel.numbers.format_decimal(
        number,
        format=f"#,##0.{'0' * decimal_places}",
        locale=locale_obj
    )

def format_currency(
    amount: Union[int, float],
    currency: str = "USD",
    language: Optional[str] = None
) -> str:
    """
    格式化货币。
    
    Args:
        amount: 金额
        currency: 货币代码，如USD、CNY、JPY等
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的货币字符串
    """
    locale_obj = _get_locale(language)
    return babel.numbers.format_currency(amount, currency, locale=locale_obj)

def format_percent(
    number: Union[int, float],
    decimal_places: int = 2,
    language: Optional[str] = None
) -> str:
    """
    格式化百分比。
    
    Args:
        number: 要格式化的数字（0.1表示10%）
        decimal_places: 小数位数
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的百分比字符串
    """
    locale_obj = _get_locale(language)
    return babel.numbers.format_percent(
        number,
        format=f"#,##0.{'0' * decimal_places} %",
        locale=locale_obj
    )

def format_date(
    date: Union[datetime, str],
    format: str = "medium",
    language: Optional[str] = None
) -> str:
    """
    格式化日期。
    
    Args:
        date: 日期对象或ISO格式的日期字符串
        format: 格式化样式，可选"short"、"medium"、"long"或"full"
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的日期字符串
    """
    locale_obj = _get_locale(language)
    
    # 如果是字符串，转换为datetime对象
    if isinstance(date, str):
        date = datetime.fromisoformat(date.replace("Z", "+00:00"))
    
    return babel.dates.format_date(date, format=format, locale=locale_obj)

def format_time(
    time: Union[datetime, str],
    format: str = "medium",
    language: Optional[str] = None
) -> str:
    """
    格式化时间。
    
    Args:
        time: 时间对象或ISO格式的时间字符串
        format: 格式化样式，可选"short"、"medium"、"long"或"full"
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的时间字符串
    """
    locale_obj = _get_locale(language)
    
    # 如果是字符串，转换为datetime对象
    if isinstance(time, str):
        time = datetime.fromisoformat(time.replace("Z", "+00:00"))
    
    return babel.dates.format_time(time, format=format, locale=locale_obj)

def format_datetime(
    datetime_obj: Union[datetime, str],
    format: str = "medium",
    language: Optional[str] = None
) -> str:
    """
    格式化日期时间。
    
    Args:
        datetime_obj: 日期时间对象或ISO格式的日期时间字符串
        format: 格式化样式，可选"short"、"medium"、"long"或"full"
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的日期时间字符串
    """
    locale_obj = _get_locale(language)
    
    # 如果是字符串，转换为datetime对象
    if isinstance(datetime_obj, str):
        datetime_obj = datetime.fromisoformat(datetime_obj.replace("Z", "+00:00"))
    
    return babel.dates.format_datetime(datetime_obj, format=format, locale=locale_obj)

def format_relative_time(
    datetime_obj: Union[datetime, str],
    reference: Optional[datetime] = None,
    language: Optional[str] = None
) -> str:
    """
    格式化相对时间（如"2小时前"）。
    
    Args:
        datetime_obj: 日期时间对象或ISO格式的日期时间字符串
        reference: 参考时间点，如果为None则使用当前时间
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        格式化后的相对时间字符串
    """
    locale_obj = _get_locale(language)
    
    # 如果是字符串，转换为datetime对象
    if isinstance(datetime_obj, str):
        datetime_obj = datetime.fromisoformat(datetime_obj.replace("Z", "+00:00"))
    
    # 默认参考时间为当前时间
    if reference is None:
        reference = datetime.now()
    
    delta = reference - datetime_obj
    
    # 使用Babel的format_timedelta
    return babel.dates.format_timedelta(delta, locale=locale_obj) 