"""
国际化中间件。

提供HTTP请求的语言检测和设置功能。
"""

from typing import Dict, List, Optional, Union
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from .translation import get_translator, set_language

class I18nMiddleware(BaseHTTPMiddleware):
    """
    国际化中间件。
    
    检测请求中的语言信息并设置响应的语言。
    """
    
    def __init__(
        self,
        app: ASGIApp,
        supported_languages: List[str] = ["en", "zh-CN"],
        default_language: str = "en",
        language_param: str = "lang",
        language_header: str = "Accept-Language",
        language_cookie: str = "preferred_language"
    ):
        """
        初始化国际化中间件。
        
        Args:
            app: ASGI应用
            supported_languages: 支持的语言代码列表
            default_language: 默认语言代码
            language_param: 用于设置语言的查询参数名
            language_header: 用于检测语言的HTTP头
            language_cookie: 用于存储语言偏好的Cookie名
        """
        super().__init__(app)
        self.supported_languages = supported_languages
        self.default_language = default_language
        self.language_param = language_param
        self.language_header = language_header
        self.language_cookie = language_cookie
    
    async def dispatch(self, request: Request, call_next) -> Response:
        """
        处理请求并设置适当的语言。
        
        Args:
            request: HTTP请求
            call_next: 下一个处理函数
            
        Returns:
            HTTP响应
        """
        # 从查询参数获取语言
        lang_query = request.query_params.get(self.language_param)
        if lang_query and lang_query in self.supported_languages:
            language = lang_query
        else:
            # 从请求头获取语言
            accept_language = request.headers.get(self.language_header, "")
            language = self._parse_accept_language(accept_language)
            
            # 如果没有合适的语言，从cookie获取
            if not language:
                language = request.cookies.get(self.language_cookie, self.default_language)
        
        # 设置当前请求的语言
        request.state.language = language
        set_language(language)
        
        # 处理请求
        response = await call_next(request)
        
        # 如果是从查询参数获取的语言，设置cookie
        if lang_query and lang_query in self.supported_languages:
            response.set_cookie(
                self.language_cookie,
                language,
                max_age=31536000,  # 1年
                httponly=True,
                samesite="lax"
            )
        
        return response
    
    def _parse_accept_language(self, accept_language: str) -> Optional[str]:
        """
        解析Accept-Language头并找到最匹配的语言。
        
        Args:
            accept_language: Accept-Language HTTP头的值
            
        Returns:
            匹配的语言代码，如果没有匹配则返回None
        """
        if not accept_language:
            return None
        
        # 解析Accept-Language头
        # 格式: "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
        languages = {}
        for item in accept_language.split(","):
            parts = item.strip().split(";q=")
            lang = parts[0].strip()
            q = float(parts[1]) if len(parts) > 1 else 1.0
            languages[lang] = q
        
        # 按优先级排序
        sorted_languages = sorted(languages.items(), key=lambda x: x[1], reverse=True)
        
        # 找到第一个支持的语言
        for lang, _ in sorted_languages:
            if lang in self.supported_languages:
                return lang
            
            # 检查语言基础部分（如"zh-CN"的"zh"）
            base_lang = lang.split("-")[0]
            for supported in self.supported_languages:
                if supported.startswith(base_lang + "-"):
                    return supported
        
        return None 