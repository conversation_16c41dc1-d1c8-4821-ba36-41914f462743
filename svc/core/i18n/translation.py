"""
翻译功能模块。

提供文本翻译和本地化功能。
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional
import threading
from pathlib import Path

# 创建线程本地存储
_thread_local = threading.local()
_thread_local.language = "en"

# 翻译缓存
_translations: Dict[str, Dict[str, str]] = {}

# 日志记录器
_logger = logging.getLogger(__name__)

def get_translations_dir() -> Path:
    """
    获取翻译文件目录路径。
    
    Returns:
        翻译文件目录的Path对象
    """
    base_dir = Path(__file__).parent
    translations_dir = base_dir / "translations"
    translations_dir.mkdir(exist_ok=True)
    return translations_dir

def load_translations(language: str) -> Dict[str, str]:
    """
    加载指定语言的翻译文件。
    
    Args:
        language: 语言代码
        
    Returns:
        翻译字典
    """
    # 如果已加载，直接返回
    if language in _translations:
        return _translations[language]
    
    translations_dir = get_translations_dir()
    translation_file = translations_dir / f"{language}.json"
    
    # 如果文件不存在，创建空文件
    if not translation_file.exists():
        _logger.warning(f"Translation file for {language} not found, creating empty file")
        with open(translation_file, "w", encoding="utf-8") as f:
            json.dump({}, f, ensure_ascii=False, indent=2)
        return {}
    
    # 读取翻译文件
    try:
        with open(translation_file, "r", encoding="utf-8") as f:
            translations = json.load(f)
        _translations[language] = translations
        return translations
    except Exception as e:
        _logger.error(f"Failed to load translations for {language}: {e}")
        return {}

def get_translator(language: Optional[str] = None) -> Dict[str, str]:
    """
    获取指定语言的翻译器。
    
    Args:
        language: 语言代码，如果为None则使用当前线程的语言
        
    Returns:
        翻译字典
    """
    if language is None:
        language = getattr(_thread_local, "language", "en")
    
    return load_translations(language)

def set_language(language: str) -> None:
    """
    设置当前线程的语言。
    
    Args:
        language: 语言代码
    """
    _thread_local.language = language

def get_current_language() -> str:
    """
    获取当前线程的语言。
    
    Returns:
        当前语言代码
    """
    return getattr(_thread_local, "language", "en")

def get_available_languages() -> List[str]:
    """
    获取所有可用的语言。
    
    Returns:
        可用语言代码列表
    """
    translations_dir = get_translations_dir()
    return [f.stem for f in translations_dir.glob("*.json")]

def _(text: str, **kwargs) -> str:
    """
    翻译文本。
    
    Args:
        text: 原始文本
        **kwargs: 用于格式化的参数
        
    Returns:
        翻译后的文本
    """
    language = getattr(_thread_local, "language", "en")
    translations = get_translator(language)
    
    # 获取翻译，如果不存在则使用原文
    translated = translations.get(text, text)
    
    # 应用格式化参数
    if kwargs:
        try:
            return translated.format(**kwargs)
        except KeyError as e:
            _logger.error(f"Missing format key in translation: {e}")
            return translated
    
    return translated

def add_translation(language: str, key: str, value: str) -> None:
    """
    添加新的翻译。
    
    Args:
        language: 语言代码
        key: 翻译键
        value: 翻译值
    """
    translations = get_translator(language)
    translations[key] = value
    
    # 保存到文件
    translations_dir = get_translations_dir()
    translation_file = translations_dir / f"{language}.json"
    
    try:
        with open(translation_file, "w", encoding="utf-8") as f:
            json.dump(translations, f, ensure_ascii=False, indent=2)
    except Exception as e:
        _logger.error(f"Failed to save translation for {language}: {e}")

def extract_translation_strings(directory: str) -> List[str]:
    """
    从指定目录的代码文件中提取翻译字符串。
    
    Args:
        directory: 目录路径
        
    Returns:
        提取的字符串列表
    """
    import re
    import glob
    
    translation_patterns = [
        r'_\("([^"]+)"\)',  # _("text")
        r"_\('([^']+)'\)",  # _('text')
        r'_\("""([^"]+)"""\)',  # _("""text""")
        r"_\('''([^']+)'''\)",  # _('''text''')
    ]
    
    strings = set()
    
    # 遍历目录下的所有Python文件
    for file_path in glob.glob(f"{directory}/**/*.py", recursive=True):
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
                
                # 查找所有翻译模式
                for pattern in translation_patterns:
                    for match in re.finditer(pattern, content):
                        strings.add(match.group(1))
        except Exception as e:
            _logger.error(f"Failed to extract strings from {file_path}: {e}")
    
    return list(strings)

def check_missing_translations(directory: str) -> Dict[str, List[str]]:
    """
    检查所有语言中缺失的翻译。
    
    Args:
        directory: 代码目录路径
        
    Returns:
        每种语言缺失的翻译列表
    """
    strings = extract_translation_strings(directory)
    languages = get_available_languages()
    missing = {}
    
    for language in languages:
        translations = get_translator(language)
        missing[language] = [s for s in strings if s not in translations]
    
    return missing 