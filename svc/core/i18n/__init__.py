"""
国际化支持模块。

提供多语言支持、本地化和翻译管理功能。
"""

# 中间件导入
from svc.core.i18n.middleware import I18nMiddleware

# 翻译相关功能导入
from svc.core.i18n.translation import (
    _, 
    get_translator, 
    set_language, 
    get_available_languages
)

# 格式化工具导入
from svc.core.i18n.formatters import (
    format_date,
    format_number,
    format_currency
)

__all__ = [
    # 中间件
    "I18nMiddleware",
    
    # 翻译函数
    "_",
    "get_translator",
    "set_language",
    "get_available_languages",
    
    # 格式化工具
    "format_date",
    "format_number",
    "format_currency",
] 