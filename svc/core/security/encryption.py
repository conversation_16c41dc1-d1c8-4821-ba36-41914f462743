"""
加密工具模块

提供对敏感数据进行加密和解密的功能，支持字符串和字典的加密处理。
基于Fernet对称加密算法实现，密钥从应用密钥派生，确保安全性和一致性。

主要功能：
- 字符串加密/解密
- 字典加密/解密
- 加密字段描述符
- 密钥生成与管理
"""

import base64
import os
import secrets
from typing import Any, Dict, List, Optional, Union, TypedDict, Callable

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from svc.core.config.settings import get_settings

# 获取应用配置
settings = get_settings()

def generate_key(length: int = 32) -> str:
    """生成指定长度的随机密钥。
    
    使用密码学安全的随机数生成器生成密钥。
    
    Args:
        length: 密钥长度，默认32字节
        
    Returns:
        str: base64编码的密钥字符串
    """
    key = secrets.token_bytes(length)
    return base64.urlsafe_b64encode(key).decode()

# 使用应用密钥派生Fernet密钥
def _get_encryption_key() -> bytes:
    """
    从应用SECRET_KEY派生加密密钥
    
    使用PBKDF2密钥派生函数和SHA256哈希算法，从应用密钥派生固定长度的加密密钥。
    
    Returns:
        bytes: 派生的Fernet加密密钥，长度为32字节，base64编码
    """
    # 使用固定盐值
    salt = b"svc_encryption_salt"
    
    # 使用PBKDF2派生密钥
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    
    # 从应用密钥派生
    key = base64.urlsafe_b64encode(
        kdf.derive(settings.SECRET_KEY.encode())
    )
    
    return key

# 获取Fernet加密实例
def _get_cipher() -> Fernet:
    """
    获取Fernet加密实例
    
    基于派生的密钥创建Fernet加密实例，用于后续的加密和解密操作。
    
    Returns:
        Fernet: 加密实例，用于对称加密和解密
    """
    key = _get_encryption_key()
    return Fernet(key)

def encrypt_value(value: str) -> str:
    """
    加密字符串值
    
    使用Fernet算法对字符串进行加密，并返回base64编码的加密结果。
    
    Args:
        value: 需要加密的字符串
        
    Returns:
        str: 加密后的字符串（Base64编码）
        
    Examples:
        >>> encrypted = encrypt_value("sensitive_data")
        >>> print(encrypted)  # 返回加密后的字符串
    """
    cipher = _get_cipher()
    encrypted_bytes = cipher.encrypt(value.encode())
    return encrypted_bytes.decode()

def decrypt_value(encrypted_value: str) -> str:
    """
    解密加密的字符串值
    
    将使用Fernet算法加密的字符串解密回原始值。
    
    Args:
        encrypted_value: 加密的字符串（Base64编码）
        
    Returns:
        str: 解密后的原始字符串
        
    Raises:
        ValueError: 当提供的字符串不是有效的加密值时
        
    Examples:
        >>> original = decrypt_value(encrypted_value)
        >>> print(original)  # 返回 "sensitive_data"
    """
    cipher = _get_cipher()
    decrypted_bytes = cipher.decrypt(encrypted_value.encode())
    return decrypted_bytes.decode()

def encrypt_dict(data: Dict[str, Any], keys_to_encrypt: List[str]) -> Dict[str, Any]:
    """
    加密字典中指定键的值
    
    复制输入字典，并对指定的键进行加密处理。
    
    Args:
        data: 需要处理的字典
        keys_to_encrypt: 需要加密的键列表
        
    Returns:
        Dict[str, Any]: 处理后的字典，指定键的值已加密
        
    Examples:
        >>> data = {"name": "John", "ssn": "***********", "age": 30}
        >>> encrypted_data = encrypt_dict(data, ["ssn"])
        >>> print(encrypted_data)  # 其中ssn字段被加密
    """
    result = data.copy()
    
    for key in keys_to_encrypt:
        if key in result and isinstance(result[key], str):
            result[key] = encrypt_value(result[key])
    
    return result

def decrypt_dict(data: Dict[str, Any], keys_to_decrypt: List[str]) -> Dict[str, Any]:
    """
    解密字典中指定键的值
    
    复制输入字典，并对指定的键进行解密处理。
    如果解密失败，将保留原始加密值。
    
    Args:
        data: 需要处理的字典
        keys_to_decrypt: 需要解密的键列表
        
    Returns:
        Dict[str, Any]: 处理后的字典，指定键的值已解密
        
    Examples:
        >>> decrypted_data = decrypt_dict(encrypted_data, ["ssn"])
        >>> print(decrypted_data)  # 还原为原始数据
    """
    result = data.copy()
    
    for key in keys_to_decrypt:
        if key in result and isinstance(result[key], str):
            try:
                result[key] = decrypt_value(result[key])
            except Exception:
                # 如果解密失败，保留原值
                pass
    
    return result

def encrypt_data(data: Union[str, Dict[str, Any]], keys_to_encrypt: Optional[List[str]] = None) -> Union[str, Dict[str, Any]]:
    """通用数据加密函数
    
    支持加密字符串或字典类型的数据。对于字典类型，可以指定需要加密的字段列表。
    
    Args:
        data: 要加密的数据，可以是字符串或字典
        keys_to_encrypt: 当data为字典时，指定需要加密的字段列表
        
    Returns:
        Union[str, Dict[str, Any]]: 加密后的数据
        
    Raises:
        TypeError: 当data类型不是字符串或字典时抛出
    """
    if isinstance(data, str):
        return encrypt_value(data)
    elif isinstance(data, dict):
        if not keys_to_encrypt:
            return data
        return encrypt_dict(data, keys_to_encrypt)
    else:
        raise TypeError("data must be string or dict")

def decrypt_data(data: Union[str, Dict[str, Any]], keys_to_decrypt: Optional[List[str]] = None) -> Union[str, Dict[str, Any]]:
    """通用数据解密函数
    
    支持解密字符串或字典类型的数据。对于字典类型，可以指定需要解密的字段列表。
    
    Args:
        data: 要解密的数据，可以是字符串或字典
        keys_to_decrypt: 当data为字典时，指定需要解密的字段列表
        
    Returns:
        Union[str, Dict[str, Any]]: 解密后的数据
        
    Raises:
        TypeError: 当data类型不是字符串或字典时抛出
    """
    if isinstance(data, str):
        return decrypt_value(data)
    elif isinstance(data, dict):
        if not keys_to_decrypt:
            return data
        return decrypt_dict(data, keys_to_decrypt)
    else:
        raise TypeError("data must be string or dict")

class EncryptedField:
    """
    加密字段处理器，用于自动处理模型字段的加密和解密
    
    可以用作描述符，在获取和设置属性时自动进行加密和解密处理。
    """
    
    def __init__(self, name: str):
        """
        初始化加密字段处理器
        
        Args:
            name: 字段名称
        """
        self.name = name
        self.private_name = f"_{name}"
    
    def __get__(self, instance, owner):
        """
        获取字段值时自动解密
        
        Args:
            instance: 类实例
            owner: 类对象
            
        Returns:
            str: 解密后的字段值
        """
        if instance is None:
            return self
            
        encrypted_value = getattr(instance, self.private_name, None)
        if encrypted_value is None:
            return None
            
        try:
            return decrypt_value(encrypted_value)
        except:
            # 解密失败返回原值
            return encrypted_value
    
    def __set__(self, instance, value):
        """
        设置字段值时自动加密
        
        Args:
            instance: 类实例
            value: 待设置的值
        """
        if value is None:
            setattr(instance, self.private_name, None)
        else:
            encrypted_value = encrypt_value(value)
            setattr(instance, self.private_name, encrypted_value) 