"""
OAuth2安全定义模块，提供与OpenAPI兼容的安全模式。
"""
from fastapi.security import OAuth2PasswordBearer
from fastapi import Depends, HTTPException, status
from fastapi.security.utils import get_authorization_scheme_param

from svc.core.config.settings import get_settings

settings = get_settings()

# 创建OAuth2PasswordBearer实例
# 使用/auth/login作为获取令牌的URL
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.api_prefix}/auth/login",
    auto_error=False  # 设置为False，防止自动抛出错误
)

# 从请求中提取令牌，不验证
def get_token_from_header(authorization: str = None,token_type:str=None) -> str:
    """
    从请求头中提取JWT令牌。
    
    Args:
        authorization: 授权头
        
    Returns:
        str: JWT令牌，如果不存在则返回None
    """
    if not authorization:
        return None
        
    scheme, token = get_authorization_scheme_param(authorization)
    if scheme.lower() != "bearer":
        return None
        
    return token

# 从依赖项中获取令牌
def get_token_from_request(token: str = Depends(oauth2_scheme)) -> str:
    """
    从请求中获取JWT令牌。
    
    Args:
        token: 通过OAuth2PasswordBearer依赖项获取的令牌
        
    Returns:
        str: JWT令牌
        
    Raises:
        HTTPException: 如果未提供有效令牌，则抛出401错误
    """
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败：未提供有效令牌",
            headers={"WWW-Authenticate": "Bearer"}
        )
    return token 