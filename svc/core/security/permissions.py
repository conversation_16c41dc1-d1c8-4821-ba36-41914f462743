"""
权限检查模块。
提供对资源访问权限的检查功能。
"""

from typing import Any, Dict, List, Optional, Protocol, Type, TypeVar, Annotated, Set, Union, Generic, cast
from datetime import datetime
from functools import wraps
import logging
import inspect
from abc import ABC, abstractmethod

from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

logger = logging.getLogger(__name__)

# 类型定义
class ResourceProtocol(Protocol):
    """资源协议，定义资源对象必须实现的属性和方法"""
    id: int
    created_by_id: int
    tenant_id: str
    created_at: datetime
    
    @property
    def owner_id(self) -> int:
        """资源所有者ID"""
        ...
        
    @property
    def is_public(self) -> bool:
        """资源是否公开"""
        ...
        
    async def can_access(self, user_id: int, db: AsyncSession) -> bool:
        """检查用户是否可以访问此资源"""
        ...

# 资源类型别名，用于类型注解
ResourceType = TypeVar("ResourceType", bound=ResourceProtocol)


class PermissionException(HTTPException):
    """权限异常基类"""
    def __init__(self, detail: str):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )


class ResourceNotFoundException(HTTPException):
    """资源不存在异常"""
    def __init__(self, resource_type: str):
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{resource_type} 未找到"
        )


class ResourceFinder(Generic[ResourceType]):
    """资源查找器，用于统一查找资源的逻辑"""
    
    def __init__(self, cache_service=None):
        """初始化资源查找器
        
        Args:
            cache_service: 缓存服务，用于缓存资源
        """
        self.cache_service = cache_service
    
    async def find_resource(
        self, 
        resource_model: Type[ResourceType], 
        resource_id: int, 
        db: AsyncSession
    ) -> Optional[ResourceType]:
        """查找资源
        
        使用多种策略查找资源，优先使用 get_by_id 方法，其次尝试使用 Repository，
        最后直接查询数据库。
        
        Args:
            resource_model: 资源模型类
            resource_id: 资源ID
            db: 数据库会话
            
        Returns:
            找到的资源对象，如果未找到则返回 None
        """
        # 检查缓存
        if self.cache_service:
            cache_key = f"{resource_model.__name__}:{resource_id}"
            cached = await self.cache_service.get(cache_key)
            if cached:
                return cached
        
        resource = None
        
        # 方法1: 使用模型类的get_by_id类方法
        if hasattr(resource_model, "get_by_id") and inspect.ismethod(getattr(resource_model, "get_by_id")):
            try:
                resource = await resource_model.get_by_id(db, resource_id)
            except Exception:
                pass
        
        # 方法2: 尝试使用Repository
        if resource is None:
            try:
                model_name = resource_model.__name__
                repository_name = f"{model_name}Repository"
                
                # 尝试从同一模块导入Repository类
                module_parts = resource_model.__module__.split('.')
                if len(module_parts) >= 3:
                    # 尝试从models改为repositories路径
                    repo_module_name = f"{'.'.join(module_parts[:-2])}.repositories.{model_name.lower()}"
                    try:
                        repo_module = __import__(repo_module_name, fromlist=[repository_name])
                        if hasattr(repo_module, repository_name):
                            repository_class = getattr(repo_module, repository_name)
                            repository = repository_class()
                            resource = await repository.get_by_id(db, resource_id)
                    except (ImportError, AttributeError):
                        pass
            except Exception:
                pass
        
        # 方法3: 直接查询数据库
        if resource is None:
            try:
                result = await db.execute(
                    select(resource_model).where(getattr(resource_model, "id") == resource_id)
                )
                resource = result.scalars().first()
            except Exception:
                pass
        
        # 缓存资源
        if resource and self.cache_service:
            cache_key = f"{resource_model.__name__}:{resource_id}"
            await self.cache_service.set(cache_key, resource, ttl=300)
        
        return resource


class PermissionService:
    """权限管理服务，提供权限检查功能"""
    
    def __init__(self, cache_service=None):
        """初始化权限服务
        
        Args:
            cache_service: 缓存服务，用于缓存权限和资源
        """
        self.cache_service = cache_service
        self._resource_registry = {}  # 资源类型注册表
        self.resource_finder = ResourceFinder(cache_service)
    
    def register_resource(self, resource_type: str, model_class: Type[ResourceType]):
        """注册资源类型与模型类的映射
        
        Args:
            resource_type: 资源类型字符串，如 'user', 'project'
            model_class: 对应的模型类
        """
        self._resource_registry[resource_type] = model_class
        
    async def get_user_permissions(self, user_id: int, db: AsyncSession) -> Set[str]:
        """获取用户所有权限
        
        Args:
            user_id: 用户ID
            db: 数据库会话
            
        Returns:
            用户拥有的权限集合
        """
        # Import necessary models and functions here to avoid potential circular imports
        # and ensure they are available in this async context.
        from svc.apps.auth.models.user import User
        from svc.apps.auth.models.role import Role
        from sqlalchemy.orm import selectinload
        
        # 尝试从缓存获取
        if self.cache_service:
            cache_key = f"permissions:{user_id}"
            cached = await self.cache_service.get(cache_key)
            if cached is not None: # Ensure cache miss is handled correctly
                return set(cached)
                
        # 直接查询与用户关联的角色，并急切加载其权限
        # Assuming 'roles' is the relationship name on User model
        # Assuming 'permissions' is the relationship/attribute on Role model containing permission strings
        stmt = (
            select(Role)
            .join(User.roles)
            .filter(User.id == user_id)
            # Eagerly load permissions associated with each role.
            # Adjust 'permissions' if the attribute name on Role model is different.
            # If Role.permissions is itself a relationship (e.g., to a Permission model),
            # you might need .options(selectinload(Role.permissions)) instead.
            # For simplicity, assuming Role.permissions is a list/set of strings directly.
        )
        
        # If permissions are stored in a separate table linked to Role, use selectinload:
        # stmt = stmt.options(selectinload(Role.permissions)) 

        result = await db.execute(stmt)
        # Use unique() to handle potential duplicates if the join produces them
        roles = result.scalars().unique().all()
        
        permissions = set()
        if not roles:
            # Explicitly check if user exists, otherwise return empty set
            user_exists = await db.scalar(select(User.id).where(User.id == user_id))
            if not user_exists:
                 logger.debug(f"User {user_id} not found when fetching permissions.")
                 return set()
            # If user exists but has no roles, permissions set remains empty, which is correct.
            logger.debug(f"User {user_id} found but has no roles.")

        else:
            for role in roles:
                # Adapt this based on how permissions are stored in your Role model:
                # 1. If role.permissions is a simple list/set of strings:
                if hasattr(role, 'permissions') and isinstance(role.permissions, (list, set)):
                     permissions.update(role.permissions)
                # 2. If role.permissions is a relationship to Permission objects with a 'name' attribute:
                # elif hasattr(role, 'permissions') and role.permissions: 
                #    permissions.update(p.name for p in role.permissions if hasattr(p, 'name'))
                else:
                     logger.warning(f"Role ID {role.id} has unexpected permissions format: {type(getattr(role, 'permissions', None))}")

        # 缓存权限
        if self.cache_service:
            await self.cache_service.set(f"permissions:{user_id}", list(permissions), ttl=300)
            logger.debug(f"Cached permissions for user {user_id}")
            
        return permissions
    
    async def has_permission(self, user_id: int, permission: str, db: AsyncSession) -> bool:
        """检查用户是否拥有指定权限
        
        Args:
            user_id: 用户ID
            permission: 权限字符串，格式为 'resource:action'
            db: 数据库会话
            
        Returns:
            用户是否拥有权限
        """
        from svc.apps.auth.repositories.user import UserRepository
        
        user = await UserRepository().get_by_id(db, user_id)
        if not user:
            return False
            
        if user.is_superuser:
            return True
            
        permissions = await self.get_user_permissions(user_id, db)
        
        # 检查通配符权限
        if "*:*" in permissions:
            return True
            
        # 检查类别通配符 (例如: "users:*")
        resource_category = permission.split(':')[0]
        if f"{resource_category}:*" in permissions:
            return True
            
        return permission in permissions
    
    async def check_resource_permission(
        self, user_id: int, resource_type: str, 
        resource_id: int, action: str, db: AsyncSession
    ) -> bool:
        """检查用户是否有权限对特定资源执行操作
        
        Args:
            user_id: 用户ID
            resource_type: 资源类型
            resource_id: 资源ID
            action: 操作类型
            db: 数据库会话
            
        Returns:
            用户是否有权限
        """
        from svc.apps.auth.repositories import UserRepository
        
        # 检查权限字符串
        permission = f"{resource_type}:{action}"
        has_global_perm = await self.has_permission(user_id, permission, db)
        
        # 如果有全局权限，则无需检查资源权限
        if has_global_perm:
            return True
            
        # 获取资源和用户
        resource_model = self._get_model_for_type(resource_type)
        if not resource_model:
            return False
            
        user = await UserRepository().get_by_id(db, user_id)
        if not user:
            return False
        
        # 使用资源查找器获取资源
        resource = await self.resource_finder.find_resource(resource_model, resource_id, db)
        if not resource:
            return False
        
        # 检查资源所有权
        if hasattr(resource, "owner_id") and resource.owner_id == user.id:
            return True
            
        # 检查租户所有权
        if (hasattr(resource, "tenant_id") and hasattr(user, "tenant_id") and 
            resource.tenant_id == user.tenant_id):
            return True
            
        # 检查资源自定义权限逻辑
        if hasattr(resource, "can_access"):
            return await resource.can_access(user.id, db)
            
        return False
    
    async def check_resource_access(
        self, user: Any, resource_type: str, 
        resource_id: int, action: str, db: AsyncSession,
        resource: Any = None
    ) -> bool:
        """统一的资源访问权限检查
        
        按优先级检查：超级用户 > 用户自定义方法 > 全局权限 > 所有者权限 > 创建者权限 > 租户权限 > 自定义规则
        
        Args:
            user: 用户对象
            resource_type: 资源类型
            resource_id: 资源ID
            action: 操作类型
            db: 数据库会话
            resource: 可选的资源对象，如果已经获取过则可以传入避免重复查询
            
        Returns:
            用户是否有权限
        """
        # 1. 超级用户检查（最高优先级）
        if getattr(user, "is_superuser", False):
            return True
            
        # 2. 用户自定义权限检查方法
        if hasattr(user, "has_resource_permission"):
            try:
                has_perm = user.has_resource_permission(resource_type, resource_id, action)
                if has_perm:
                    return True
            except Exception as e:
                logger.warning(f"用户自定义权限检查失败: {str(e)}")
        
        # 3. 全局权限检查
        permission = f"{resource_type}:{action}"
        try:
            has_global_perm = await self.has_permission(user.id, permission, db)
            if has_global_perm:
                return True
        except Exception as e:
            logger.warning(f"全局权限检查失败: {str(e)}")
        
        # 4. 资源特定权限检查
        if resource is None:
            resource_model = self._get_model_for_type(resource_type)
            if not resource_model:
                return False
            
            # 获取资源实例（使用缓存）
            resource = await self.resource_finder.find_resource(resource_model, resource_id, db)
            if not resource:
                return False
        
        # 5. 所有者权限
        if hasattr(resource, "owner_id") and resource.owner_id == user.id:
            return True
        
        # 6. 创建者权限
        if hasattr(resource, "created_by_id") and resource.created_by_id == user.id:
            return True
            
        # 7. 租户权限
        if (hasattr(resource, "tenant_id") and hasattr(user, "tenant_id") and 
            resource.tenant_id == user.tenant_id):
            return True
            
        # 8. 资源自定义权限逻辑（最低优先级）
        if hasattr(resource, "can_access"):
            try:
                return await resource.can_access(user.id, db)
            except Exception as e:
                logger.warning(f"资源自定义权限检查失败: {str(e)}")
            
        return False
    
    async def has_role(self, user_id: int, role: str, db: AsyncSession) -> bool:
        """检查用户是否拥有指定角色
        
        Args:
            user_id: 用户ID
            role: 角色名称
            db: 数据库会话
            
        Returns:
            用户是否拥有角色
        """
        from svc.apps.auth.models.user import User
        
        # 尝试从缓存获取
        if self.cache_service:
            cache_key = f"user_roles:{user_id}"
            cached_roles = await self.cache_service.get(cache_key)
            if cached_roles is not None:
                return role in cached_roles
        
        # 从数据库检查
        user = await User.get_by_id(db, user_id)
        if not user:
            return False
            
        if user.is_superuser:
            return True
            
        has_role = False
        if hasattr(user, "roles"):
            for user_role in user.roles:
                if user_role.name == role:
                    has_role = True
                    break
        
        # 缓存角色
        if self.cache_service and hasattr(user, "roles"):
            role_names = [r.name for r in user.roles]
            await self.cache_service.set(f"user_roles:{user_id}", role_names, ttl=300)
            
        return has_role
    
    async def invalidate_permission_cache(self, user_id: int):
        """使指定用户的权限缓存失效
        
        Args:
            user_id: 用户ID
        """
        if self.cache_service:
            await self.cache_service.delete(f"permissions:{user_id}")
            await self.cache_service.delete(f"user_roles:{user_id}")
            logger.debug(f"已清除用户[{user_id}]的权限缓存")
    
    async def invalidate_resource_cache(self, resource_type: str, resource_id: int):
        """使指定资源的缓存失效
        
        Args:
            resource_type: 资源类型
            resource_id: 资源ID
        """
        if self.cache_service:
            resource_model = self._get_model_for_type(resource_type)
            if resource_model:
                cache_key = f"{resource_model.__name__}:{resource_id}"
                await self.cache_service.delete(cache_key)
                logger.debug(f"已清除资源[{resource_type}:{resource_id}]的缓存")
        
    def _get_model_for_type(self, resource_type: str) -> Optional[Type[ResourceType]]:
        """根据资源类型获取模型类
        
        Args:
            resource_type: 资源类型字符串
            
        Returns:
            对应的模型类，如果未注册则返回 None
        """
        # print(self._resource_registry)  # 注释掉调试语句
        return self._resource_registry.get(resource_type)

# 创建全局权限服务实例
permission_service = PermissionService()

def register_resource_model(resource_type: str):
    """资源模型注册装饰器
    
    用法:
    @register_resource_model("user")
    class User(Base):
        ...
        
    Args:
        resource_type: 资源类型标识符
        
    Returns:
        装饰器函数
    """
    def decorator(cls):
        global permission_service
        permission_service.register_resource(resource_type, cls)
        logger.info(f"已通过装饰器注册资源类型: {resource_type}")
        return cls
    return decorator


class ResourcePermissionChecker(Generic[ResourceType]):
    """资源权限检查器，用于检查用户是否有权访问特定资源"""
    
    def __init__(
        self,
        resource_type: Type[ResourceType],
        allow_owner: bool = True,
        allow_tenant: bool = True,
        allow_public: bool = True,
        require_active: bool = True,
        superuser_bypass: bool = True,
        cache_service = None
    ):
        """初始化权限检查器
        
        Args:
            resource_type: 资源类型
            allow_owner: 是否允许资源所有者访问
            allow_tenant: 是否允许同租户用户访问
            allow_public: 是否允许访问公开资源
            require_active: 是否要求用户处于活跃状态
            superuser_bypass: 超级用户是否绕过权限检查
            cache_service: 缓存服务
        """
        self.resource_type = resource_type
        self.allow_owner = allow_owner
        self.allow_tenant = allow_tenant
        self.allow_public = allow_public
        self.require_active = require_active
        self.superuser_bypass = superuser_bypass
        self.resource_finder = ResourceFinder(cache_service)
    
    async def __call__(
        self,
        resource_id: int,
        current_user: Any,
        db: AsyncSession
    ) -> ResourceType:
        """检查权限并返回资源
        
        Args:
            resource_id: 资源ID
            current_user: 当前用户
            db: 数据库会话
            
        Returns:
            资源对象
            
        Raises:
            HTTPException: 当用户没有访问权限时
        """
        # 检查用户是否活跃
        if self.require_active and not getattr(current_user, "is_active", False):
            raise PermissionException("非活跃用户无法访问资源")
        
        # 获取资源
        resource = await self.resource_finder.find_resource(self.resource_type, resource_id, db)
        if not resource:
            raise ResourceNotFoundException(self.resource_type.__name__)
        
        # 超级用户绕过权限检查
        if self.superuser_bypass and getattr(current_user, "is_superuser", False):
            return resource
        
        # 检查权限
        has_permission = await self._check_permission(resource, current_user, db)
        if not has_permission:
            raise PermissionException("没有权限访问此资源")
        
        return resource
    
    async def _check_permission(
        self, 
        resource: ResourceType, 
        user: Any,
        db: AsyncSession
    ) -> bool:
        """检查用户是否有权限访问资源
        
        Args:
            resource: 资源对象
            user: 用户对象
            db: 数据库会话
            
        Returns:
            bool: 是否有权限
        """
        # 所有者权限
        if self.allow_owner and resource.owner_id == user.id:
            return True
        
        # 租户权限
        if self.allow_tenant and resource.tenant_id == user.tenant_id:
            return True
        
        # 公共资源
        if self.allow_public and hasattr(resource, 'is_public') and resource.is_public:
            return True
        
        # 自定义访问检查
        if hasattr(resource, 'can_access'):
            return await resource.can_access(user.id, db)
        
        return False