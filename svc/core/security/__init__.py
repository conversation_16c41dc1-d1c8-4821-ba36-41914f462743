"""
安全模块。
提供认证、授权和安全相关的功能。
"""

# 基础安全工具（无内部依赖）
from svc.core.security.password import verify_password, get_password_hash

# 加密服务（依赖基础工具）
from svc.core.security.encryption import (
    encrypt_data,
    decrypt_data,
    EncryptedField,
    generate_key
)

# 令牌服务
from svc.core.security.token import TokenService


# 权限管理
from svc.core.security.permissions import (
    ResourceProtocol,
    ResourceType,
    PermissionService,
    ResourcePermissionChecker
)

__all__ = [
    # 密码工具
    "verify_password",
    "get_password_hash",
    
    # 加密服务
    "encrypt_data",
    "decrypt_data",
    "EncryptedField",
    "generate_key",
    
    # 令牌服务
    "TokenService",
    
    
    # 权限管理
    "ResourceProtocol",
    "ResourceType",
    "PermissionService",
    "ResourcePermissionChecker"
]
