"""
令牌管理服务
提供JWT令牌的生成、验证和错误处理功能
"""
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any, Tuple, List, Set

from jose import jwt, JWTError
import logging

# 创建日志记录器
logger = logging.getLogger(__name__)

# 记录已废弃的刷新令牌 - 生产环境应该使用数据库或Redis替代内存存储
_revoked_tokens: Set[str] = set()
# 记录刷新令牌的哈希与用户ID的映射 - 生产环境应该使用数据库或Redis替代内存存储
_refresh_tokens: Dict[str, int] = {}

class TokenService:
    """令牌管理服务，提供JWT令牌生成和验证功能"""
    
    def __init__(
        self,
        secret_key: str,
        algorithm: str = "HS256",
        access_token_ttl: int = 1800,
        refresh_token_ttl: int = 604800
    ):
        """初始化令牌服务
        
        Args:
            secret_key: 密钥
            algorithm: 加密算法
            access_token_ttl: 访问令牌有效期（秒）
            refresh_token_ttl: 刷新令牌有效期（秒）
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.access_token_ttl = access_token_ttl  # 30分钟
        self.refresh_token_ttl = refresh_token_ttl  # 7天
        
    def create_access_token(
        self,
        user_id: int,
        extra_data: dict = None
    ) -> str:
        """创建访问令牌
        
        Args:
            user_id: 用户ID
            extra_data: 额外数据
            
        Returns:
            str: JWT令牌
        """
        return self._create_token(
            data={"sub": str(user_id), "type": "access", **(extra_data or {})},
            expires_delta=timedelta(seconds=self.access_token_ttl)
        )
    
    def create_refresh_token(self, user_id: int, device_info: dict = None) -> str:
        """创建刷新令牌
        
        Args:
            user_id: 用户ID
            device_info: 设备信息（如IP地址、User-Agent等）
            
        Returns:
            str: JWT刷新令牌
        """
        # 生成唯一的刷新令牌ID
        import uuid
        token_id = str(uuid.uuid4())
        
        # 构建令牌数据，包括token_id和设备信息
        token_data = {
            "sub": str(user_id), 
            "type": "refresh", 
            "jti": token_id,  # JWT ID
            **(device_info or {})
        }
        
        # 创建令牌
        token = self._create_token(
            data=token_data,
            expires_delta=timedelta(seconds=self.refresh_token_ttl)
        )
        
        # 存储令牌ID和用户ID的映射
        self._store_refresh_token(token_id, user_id)
        
        return token
    
    def _store_refresh_token(self, token_id: str, user_id: int) -> None:
        """存储刷新令牌ID与用户的映射
        
        生产环境应替换为数据库或Redis存储
        
        Args:
            token_id: 令牌ID
            user_id: 用户ID
        """
        _refresh_tokens[token_id] = user_id
        logger.debug(f"存储刷新令牌: {token_id} -> 用户ID: {user_id}")
    
    def create_password_reset_token(self, user_id: int) -> str:
        """创建密码重置令牌
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: JWT令牌
        """
        return self._create_token(
            data={"sub": str(user_id), "type": "reset"},
            expires_delta=timedelta(minutes=30)  # 密码重置令牌有效期30分钟
        )
        
    def _create_token(
        self,
        data: dict,
        expires_delta: timedelta
    ) -> str:
        """创建JWT令牌(内部方法)
        
        Args:
            data: 令牌数据
            expires_delta: 有效期
            
        Returns:
            str: JWT令牌
        """
        to_encode = data.copy()
        expire = datetime.now(timezone.utc) + expires_delta
        to_encode.update({
            "exp": expire.timestamp(),
            "iat": datetime.now(timezone.utc).timestamp()
        })
        return jwt.encode(
            to_encode,
            self.secret_key,
            algorithm=self.algorithm
        )
    
    def refresh_access_token(self, refresh_token: str, device_info: dict = None) -> Dict[str, str]:
        """使用刷新令牌获取新的访问令牌和刷新令牌
        
        实现刷新令牌旋转机制，每次使用刷新令牌后，生成新的刷新令牌并使旧令牌失效
        
        Args:
            refresh_token: 刷新令牌
            device_info: 设备信息（如IP地址、User-Agent等）
            
        Returns:
            Dict[str, str]: 包含新访问令牌和刷新令牌的字典
            
        Raises:
            JWTError: 如果刷新令牌无效或已被吊销
        """
        try:
            # 验证刷新令牌
            payload = self.verify_refresh_token(refresh_token)
            
            # 提取用户ID和令牌ID
            user_id = int(payload.get("sub"))
            token_id = payload.get("jti")
            
            # 检查令牌是否已被吊销
            if token_id and self.is_token_revoked(token_id):
                logger.warning(f"尝试使用已吊销的刷新令牌: {token_id}")
                raise JWTError("刷新令牌已被吊销")
            
            # 吊销当前刷新令牌(只能使用一次)
            if token_id:
                self.revoke_token(token_id)
            
            # 创建新的访问令牌和刷新令牌
            access_token = self.create_access_token(user_id)
            new_refresh_token = self.create_refresh_token(user_id, device_info)
            
            return {
                "access_token": access_token,
                "refresh_token": new_refresh_token
            }
        except JWTError as e:
            logger.warning(f"刷新令牌验证失败: {str(e)}")
            raise
        
    def verify_token(
        self,
        token: str,
        token_type: str = None
    ) -> Tuple[dict, bool]:
        """验证令牌并返回载荷
        
        Args:
            token: JWT令牌
            token_type: 令牌类型（可选）
            
        Returns:
            Tuple[dict, bool]: (载荷, 是否过期)
            
        Raises:
            TokenError: 令牌无效
        """
        try:
            # 解码令牌
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm]
            )
            
            # 检查令牌类型
            if token_type and payload.get("type") != token_type:
                raise JWTError("无效的令牌类型")
            
            # 如果是刷新令牌，检查是否已被吊销
            if payload.get("type") == "refresh" and payload.get("jti"):
                if self.is_token_revoked(payload.get("jti")):
                    raise JWTError("令牌已被吊销")
                
            # 检查是否过期
            is_expired = self.is_token_expired(payload)
            
            return payload, is_expired
            
        except jwt.ExpiredSignatureError:
            raise JWTError("令牌已过期")
            
        except jwt.JWTError:
            raise JWTError("无效的令牌")
            
    def verify_access_token(self, token: str) -> dict:
        """验证访问令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            dict: 令牌载荷
            
        Raises:
            TokenError: 令牌无效
        """
        payload, is_expired = self.verify_token(token, "access")
        if is_expired:
            raise JWTError("访问令牌已过期")
        return payload
    
    def verify_refresh_token(self, token: str) -> dict:
        """验证刷新令牌
        
        Args:
            token: JWT令牌
            
        Returns:
            dict: 令牌载荷
            
        Raises:
            TokenError: 令牌无效
        """
        payload, is_expired = self.verify_token(token, "refresh")
        if is_expired:
            raise JWTError("刷新令牌已过期")
        return payload
    
    def is_token_expired(self, payload: Dict[str, Any]) -> bool:
        """检查令牌是否过期
        
        Args:
            payload: 令牌载荷
            
        Returns:
            bool: 是否过期
        """
        exp = payload.get('exp')
        if not exp:
            return True
            
        now = datetime.now(timezone.utc).timestamp()
        return exp < now
    
    def revoke_token(self, token_id: str) -> None:
        """吊销令牌
        
        Args:
            token_id: 令牌ID
        """
        _revoked_tokens.add(token_id)
        logger.debug(f"令牌已吊销: {token_id}")
    
    def revoke_all_user_tokens(self, user_id: int) -> None:
        """吊销用户的所有刷新令牌
        
        生产环境应替换为数据库或Redis实现
        
        Args:
            user_id: 用户ID
        """
        # 找出属于该用户的所有令牌ID
        token_ids = [
            token_id for token_id, uid in _refresh_tokens.items() 
            if uid == user_id
        ]
        
        # 吊销所有找到的令牌
        for token_id in token_ids:
            self.revoke_token(token_id)
        
        logger.info(f"已吊销用户 {user_id} 的 {len(token_ids)} 个令牌")
    
    def is_token_revoked(self, token_id: str) -> bool:
        """检查令牌是否已被吊销
        
        Args:
            token_id: 令牌ID
            
        Returns:
            bool: 是否已被吊销
        """
        return token_id in _revoked_tokens
        
    @staticmethod
    def extract_token_from_header(
        auth_header: Optional[str]
    ) -> Optional[str]:
        """从认证头中提取令牌
        
        Args:
            auth_header: 认证头
            
        Returns:
            Optional[str]: 令牌
        """
        if not auth_header:
            return None
            
        try:
            scheme, token = auth_header.split()
            if scheme.lower() != "bearer":
                return None
            return token
        except ValueError:
            return None