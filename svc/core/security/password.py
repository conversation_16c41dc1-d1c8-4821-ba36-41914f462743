"""
密码处理工具模块，提供密码哈希和验证功能

独立的密码处理模块，避免循环导入问题。
"""
# 以下是直接使用bcrypt库的替代实现
# 若要使用此实现，请注释掉上面的passlib实现，并取消下面的注释
# 此实现可以使用最新版本的bcrypt，没有兼容性问题

import bcrypt

def verify_password(plain_password: str, hashed_password: str) -> bool:
    '''
    验证密码

    使用bcrypt算法验证明文密码与哈希密码是否匹配。

    Args:
        plain_password: 明文密码
        hashed_password: 哈希后的密码

    Returns:
        bool: 密码是否匹配
    '''
    # 确保输入是bytes类型
    if isinstance(plain_password, str):
        plain_password = plain_password.encode('utf-8')
    if isinstance(hashed_password, str):
        hashed_password = hashed_password.encode('utf-8')
        
    return bcrypt.checkpw(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    '''
    对密码进行哈希处理

    使用bcrypt算法对密码进行安全哈希，生成安全存储的密码哈希值。

    Args:
        password: 明文密码

    Returns:
        str: 哈希后的密码
    '''
    # 确保输入是bytes类型
    if isinstance(password, str):
        password = password.encode('utf-8')
        
    # 使用默认盐轮数(12)生成哈希
    hashed = bcrypt.hashpw(password, bcrypt.gensalt())
    
    # 返回字符串格式
    return hashed.decode('utf-8')
