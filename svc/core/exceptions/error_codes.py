"""
错误码定义模块，统一管理系统中的所有错误码。
"""

from enum import Enum


class ErrorCode(str, Enum):
    """统一的错误码定义，按领域分组"""
    
    # 通用错误码 (1000-1999)
    NOT_FOUND = 1000  # 资源未找到
    PERMISSION_DENIED = 1001  # 权限不足
    INVALID_INPUT = 1002  # 无效输入
    SYSTEM_ERROR = 1003  # 系统错误
    VALIDATION_ERROR = 1004  # 验证错误
    RESOURCE_EXISTS = 1005  # 资源已存在
    RESOURCE_EXPIRED = 1006  # 资源已过期
    RESOURCE_LOCKED = 1007  # 资源被锁定
    INTERNAL_ERROR = 1008  # 内部错误
    OPERATION_FAILED = 1009  # 操作失败
    CONFLICT = 1010  # 冲突
    CREATE_FAILED = 1011  # 创建失败
    UPDATE_FAILED = 1012  # 更新失败
    DELETE_FAILED = 1013  # 删除失败
    
    # 认证授权错误码 (2000-2999)
    UNAUTHORIZED = 2000  # 未认证
    TOKEN_EXPIRED = 2001  # 令牌过期
    TOKEN_INVALID = 2002  # 令牌无效
    LOGIN_FAILED = 2003  # 登录失败
    ACCOUNT_LOCKED = 2004  # 账号被锁定
    ACCOUNT_DISABLED = 2005  # 账号被禁用
    PASSWORD_EXPIRED = 2006  # 密码过期
    
    # 用户相关错误码 (3000-3999)
    USER_NOT_FOUND = 3000  # 用户不存在
    USER_EXISTS = 3001  # 用户已存在
    USER_INACTIVE = 3002  # 用户未激活
    USER_BLOCKED = 3003  # 用户被封禁
    INVALID_USERNAME = 3004  # 无效用户名
    INVALID_PASSWORD = 3005  # 无效密码
    INVALID_EMAIL = 3006  # 无效邮箱

    ROLE_NOT_FOUND = 3007  # 角色不存在
    ROLE_EXISTS = 3008  # 角色已存在
    ROLE_IN_USE = 3009  # 角色正在使用中
    
    # 营销模块错误码 (4000-4999)
    CAMPAIGN_NOT_FOUND = 4000  # 活动不存在
    CAMPAIGN_ENDED = 4001  # 活动已结束
    CAMPAIGN_NOT_STARTED = 4002  # 活动未开始
    CAMPAIGN_FULL = 4003  # 活动名额已满
    CAMPAIGN_NOT_ELIGIBLE = 4004  # 不符合活动资格
    
    INVITATION_NOT_FOUND = 4100  # 邀请不存在
    INVITATION_EXPIRED = 4101  # 邀请已过期
    INVITATION_USED = 4102  # 邀请已使用
    INVITATION_INVALID = 4103  # 邀请无效
    INVITATION_LIMIT_EXCEEDED = 4104  # 超出邀请限制
    INVITATION_GENERATE_ERROR = 4105  # 生成邀请码失败
    INVITATION_SAVE_ERROR = 4106  # 保存邀请记录失败
    ALREADY_PARTICIPATED = 4107  # 已参与活动
    
    # 账单模块错误码 (5000-5999)
    PAYMENT_FAILED = 5000  # 支付失败
    PAYMENT_EXPIRED = 5001  # 支付已过期
    PAYMENT_CANCELLED = 5002  # 支付已取消
    PAYMENT_NOT_FOUND = 5003  # 支付记录不存在
    INSUFFICIENT_BALANCE = 5004  # 余额不足
    INVOICE_NOT_FOUND = 5005  # 发票不存在
    INVOICE_PAID = 5006  # 发票已支付
    INVOICE_CANCELLED = 5007  # 发票已取消
    SUBSCRIPTION_NOT_FOUND = 5008  # 订阅不存在
    SUBSCRIPTION_EXISTS = 5009  # 订阅已存在
    SUBSCRIPTION_IN_USE = 5010  # 订阅正在使用中
    SUBSCRIPTION_EXPIRED = 5011  # 订阅已过期
    SUBSCRIPTION_CANCELLED = "5012"  # 订阅已取消
    SUBSCRIPTION_PAUSED = 5013  # 订阅已暂停
    SUBSCRIPTION_RENEWAL_FAILED = 5014  # 订阅续费失败
    SUBSCRIPTION_PAYMENT_REQUIRED = 5015  # 订阅需要支付
    SUBSCRIPTION_PLAN_NOT_FOUND = 5016  # 订阅计划不存在
    SUBSCRIPTION_PLAN_EXISTS = 5017  # 订阅计划已存在
    SUBSCRIPTION_PLAN_IN_USE = 5018  # 订阅计划正在使用中
    
    # 计划相关错误码 (5100-5199)
    PLAN_NOT_FOUND = 5100  # 计划不存在
    PLAN_EXISTS = 5101  # 计划已存在
    PLAN_IN_USE = 5102  # 计划正在使用中
    
    # 系统模块错误码 (6000-6999)
    CONFIG_ERROR = 6000  # 配置错误
    DATABASE_ERROR = 6001  # 数据库错误
    CACHE_ERROR = 6002  # 缓存错误
    QUEUE_ERROR = 6003  # 队列错误
    NETWORK_ERROR = 6004  # 网络错误
    THIRD_PARTY_ERROR = 6005  # 第三方服务错误
    RATE_LIMIT_EXCEEDED = 6006  # 超出访问频率限制
    
    # API错误码 (7000-7999)
    API_ERROR = 7000  # API错误
    API_NOT_FOUND = 7001  # API不存在
    API_VERSION_ERROR = 7002  # API版本错误
    API_DEPRECATED = 7003  # API已弃用
    API_LIMIT_EXCEEDED = 7004  # API调用次数超限
    
    @classmethod
    def get_message(cls, code: str) -> str:
        """获取错误码对应的默认错误消息
        
        Args:
            code: 错误码
            
        Returns:
            str: 错误消息
        """
        messages = {
            # 通用错误消息
            cls.NOT_FOUND: "请求的资源不存在",
            cls.PERMISSION_DENIED: "没有权限执行此操作",
            cls.INVALID_INPUT: "输入参数无效",
            cls.SYSTEM_ERROR: "系统错误，请稍后重试",
            cls.VALIDATION_ERROR: "数据验证失败",
            cls.RESOURCE_EXISTS: "资源已存在",
            cls.RESOURCE_EXPIRED: "资源已过期",
            cls.RESOURCE_LOCKED: "资源已被锁定",
            
            # 认证授权错误消息
            cls.UNAUTHORIZED: "请先登录",
            cls.TOKEN_EXPIRED: "登录已过期，请重新登录",
            cls.TOKEN_INVALID: "无效的认证信息",
            cls.LOGIN_FAILED: "登录失败，请检查用户名和密码",
            cls.ACCOUNT_LOCKED: "账号已被锁定",
            cls.ACCOUNT_DISABLED: "账号已被禁用",
            cls.PASSWORD_EXPIRED: "密码已过期，请修改密码",
            
            # 用户相关错误消息
            cls.USER_NOT_FOUND: "用户不存在",
            cls.USER_EXISTS: "用户已存在",
            cls.USER_INACTIVE: "用户未激活",
            cls.USER_BLOCKED: "用户已被封禁",
            cls.INVALID_USERNAME: "无效的用户名",
            cls.INVALID_PASSWORD: "无效的密码",
            cls.INVALID_EMAIL: "无效的邮箱地址",

            cls.ROLE_NOT_FOUND: "角色不存在",
            cls.ROLE_EXISTS: "角色已存在",
            cls.ROLE_IN_USE: "角色正在使用中",
            
            # 营销模块错误消息
            cls.CAMPAIGN_NOT_FOUND: "活动不存在",
            cls.CAMPAIGN_ENDED: "活动已结束",
            cls.CAMPAIGN_NOT_STARTED: "活动未开始",
            cls.CAMPAIGN_FULL: "活动名额已满",
            cls.CAMPAIGN_NOT_ELIGIBLE: "不符合活动参与资格",
            
            cls.INVITATION_NOT_FOUND: "邀请不存在",
            cls.INVITATION_EXPIRED: "邀请已过期",
            cls.INVITATION_USED: "邀请已被使用",
            cls.INVITATION_INVALID: "无效的邀请",
            cls.INVITATION_LIMIT_EXCEEDED: "超出邀请次数限制",
            cls.INVITATION_GENERATE_ERROR: "生成邀请码失败",
            cls.INVITATION_SAVE_ERROR: "保存邀请记录失败",
            cls.ALREADY_PARTICIPATED: "您已参与过该活动",
            
            # 账单模块错误消息
            cls.PAYMENT_FAILED: "支付失败",
            cls.PAYMENT_EXPIRED: "支付已过期",
            cls.PAYMENT_CANCELLED: "支付已取消",
            cls.PAYMENT_NOT_FOUND: "支付记录不存在",
            cls.INSUFFICIENT_BALANCE: "余额不足",
            cls.INVOICE_NOT_FOUND: "发票不存在",
            cls.INVOICE_PAID: "发票已支付",
            cls.INVOICE_CANCELLED: "发票已取消",
            cls.SUBSCRIPTION_NOT_FOUND: "订阅不存在",
            cls.SUBSCRIPTION_EXISTS: "订阅已存在",
            cls.SUBSCRIPTION_IN_USE: "订阅正在使用中",
            cls.SUBSCRIPTION_EXPIRED: "订阅已过期",
            cls.SUBSCRIPTION_CANCELLED: "订阅已取消",
            cls.SUBSCRIPTION_PAUSED: "订阅已暂停",
            cls.SUBSCRIPTION_RENEWAL_FAILED: "订阅续费失败",
            cls.SUBSCRIPTION_PAYMENT_REQUIRED: "订阅需要支付",
            cls.SUBSCRIPTION_PLAN_NOT_FOUND: "订阅计划不存在",
            cls.SUBSCRIPTION_PLAN_EXISTS: "订阅计划已存在",
            cls.SUBSCRIPTION_PLAN_IN_USE: "订阅计划正在使用中",
            
            # 计划相关错误消息
            cls.PLAN_NOT_FOUND: "计划不存在",
            cls.PLAN_EXISTS: "计划已存在",
            cls.PLAN_IN_USE: "计划正在使用中",
            
            # 系统模块错误消息
            cls.CONFIG_ERROR: "系统配置错误",
            cls.DATABASE_ERROR: "数据库操作失败",
            cls.CACHE_ERROR: "缓存操作失败",
            cls.QUEUE_ERROR: "队列操作失败",
            cls.NETWORK_ERROR: "网络连接错误",
            cls.THIRD_PARTY_ERROR: "第三方服务调用失败",
            cls.RATE_LIMIT_EXCEEDED: "请求过于频繁，请稍后重试",
            
            # API错误消息
            cls.API_ERROR: "API调用失败",
            cls.API_NOT_FOUND: "API接口不存在",
            cls.API_VERSION_ERROR: "API版本不兼容",
            cls.API_DEPRECATED: "API已弃用",
            cls.API_LIMIT_EXCEEDED: "API调用次数超出限制"
        }
        return messages.get(code, "未知错误") 