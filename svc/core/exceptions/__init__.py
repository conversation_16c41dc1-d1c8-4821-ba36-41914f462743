"""
统一异常处理模块
提供应用异常类和错误处理函数，用于统一管理应用中的异常

错误处理规范：
1. 服务层：
   - 应移除handle_service_errors装饰器
   - 在方法内使用try-except块处理异常
   - 使用create_error_result返回错误结果
   - 示例:
     ```python
     async def some_service_method(self, params):
         try:
             # 业务逻辑
             return self.create_success_result(data)
         except Exception as e:
             self.logger.error(f"处理失败: {str(e)}", exc_info=True)
             return self.create_error_result(
                 error_code=ErrorCode.OPERATION_FAILED,
                 error_message=f"操作失败: {str(e)}"
             )
     ```

2. 路由层：
   - 应使用handle_route_errors装饰器处理服务返回的错误
   - 示例:
     ```python
     @router.post("/resource", response_model=ResourceResponse)
     @handle_route_errors(RESOURCE_ERROR_MAPPING)
     async def create_resource(
         resource_data: ResourceCreate,
         service: ResourceService = Depends(get_resource_service)
     ):
         return await service.create_resource(resource_data)
     ```
"""

import functools
from typing import (Any, Awaitable, Callable, Dict, Optional, TypeVar, Union,
                    cast)

from fastapi import HTTPException, status

from svc.core.exceptions.error_codes import ErrorCode
from svc.core.exceptions.route_error_handler import (
    AUTH_ERROR_MAPPING, CAMPAIGN_ERROR_MAPPING, CATEGORY_ERROR_MAPPING,
    INVENTORY_ERROR_MAPPING, INVITATION_ERROR_MAPPING, INVOICE_ERROR_MAPPING,
    MARKETING_ERROR_MAPPING, PAYMENT_ERROR_MAPPING, PLAN_ERROR_MAPPING,
    PRODUCT_ERROR_MAPPING, PRODUCT_VARIANT_ERROR_MAPPING,
    PRODUCTS_ERROR_MAPPING, REWARD_ERROR_MAPPING, ROLE_ERROR_MAPPING,
    SUBSCRIPTION_ERROR_MAPPING, USER_ERROR_MAPPING, ResultHandler,
    handle_route_errors)
from svc.core.services.result import Result

# 定义泛型类型变量
T = TypeVar('T')
R = TypeVar('R', bound=Result)

class AppException(HTTPException):
    """应用异常基类，继承自FastAPI的HTTPException"""
    
    def __init__(
        self,
        code: Union[ErrorCode, str],
        message: Optional[str] = None,
        status_code: int = status.HTTP_400_BAD_REQUEST,
        headers: Optional[Dict[str, str]] = None
    ):
        """初始化应用异常
        
        Args:
            code: 错误码
            message: 错误消息，不提供时使用错误码的默认消息
            status_code: HTTP状态码
            headers: 响应头
        """
        self.code = code if isinstance(code, str) else code.value
        self.error_message = message or ErrorCode.get_message(self.code) if isinstance(code, ErrorCode) else str(code)
        
        detail = {
            "code": self.code,
            "message": self.error_message
        }
        
        super().__init__(status_code=status_code, detail=detail, headers=headers)


def handle_service_errors(error_code: Union[ErrorCode, str] = "OPERATION_FAILED"):
    """错误处理装饰器，用于服务层方法的异常处理
    
    注意: 此装饰器已被弃用，请使用try-except和create_error_result方法替代。
    
    Args:
        error_code: 默认错误代码
        
    Returns:
        装饰后的函数
    
    Example:
        @handle_service_errors(error_code=ErrorCode.CREATE_FAILED)
        async def create_user(self, params: CreateUserParams) -> UserResult:
            # 实现...
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> T:
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # 获取self参数(服务实例)
                self = args[0]
                if hasattr(self, 'db') and self.db:
                    try:
                        await self.db.rollback()
                    except Exception:
                        pass
                    
                # 记录错误
                if hasattr(self, 'logger'):
                    self.logger.exception(f"服务方法执行出错: {str(e)}")
                
                # 根据返回类型处理错误
                if hasattr(func, '__annotations__') and 'return' in func.__annotations__:
                    result_cls = func.__annotations__.get('return')
                    # 确保结果类型是Result子类
                    if hasattr(self, 'create_error_result'):
                        code = error_code.value if hasattr(error_code, 'value') else error_code
                        return cast(T, self.create_error_result(
                            error_code=code,
                            error_message=str(e)
                        ))
                # 对于其他返回类型，重新抛出异常
                raise
                
        return wrapper
    return decorator


def handle_result_error(
    result: Result,
    error_status_mapping: Optional[Dict[str, int]] = None,
    default_status_code: int = status.HTTP_400_BAD_REQUEST,
    error_headers: Optional[Dict[str, str]] = None
) -> None:
    """
    处理服务结果中的错误，如果有错误则抛出HTTPException
    
    注意: 此函数已被弃用，请使用handle_route_errors装饰器替代。
    
    Args:
        result: 服务返回的结果对象
        error_status_mapping: 错误码到HTTP状态码的映射字典
        default_status_code: 默认HTTP状态码
        error_headers: 错误时需要返回的额外响应头
        
    Raises:
        HTTPException: 当结果包含错误时抛出
    """
    if not result.is_success:
        status_code = default_status_code
        
        # 使用错误码映射确定HTTP状态码
        if error_status_mapping and result.error_code in error_status_mapping:
            status_code = error_status_mapping[result.error_code]
            
        raise HTTPException(
            status_code=status_code,
            detail={
                "code": result.error_code,
                "message": result.error_message
            },
            headers=error_headers
        )


def create_http_exception(error_code: Union[ErrorCode, str], error_message: Optional[str] = None) -> HTTPException:
    """基于错误代码创建HTTP异常
    
    Args:
        error_code: 错误代码
        error_message: 错误消息
        
    Returns:
        HTTP异常对象
    """
    code = error_code.value if hasattr(error_code, 'value') else str(error_code)
    message = error_message or (ErrorCode.get_message(code) if hasattr(ErrorCode, 'get_message') else str(error_code))
    
    error_status = {
        str(ErrorCode.NOT_FOUND): status.HTTP_404_NOT_FOUND,
        str(ErrorCode.PERMISSION_DENIED): status.HTTP_403_FORBIDDEN,
        str(ErrorCode.UNAUTHORIZED): status.HTTP_401_UNAUTHORIZED,
        **AUTH_ERROR_MAPPING,
        **USER_ERROR_MAPPING,
    }
    
    status_code = error_status.get(code, status.HTTP_400_BAD_REQUEST)
    headers = {"WWW-Authenticate": "Bearer"} if status_code == status.HTTP_401_UNAUTHORIZED else None
    
    return HTTPException(
        status_code=status_code,
        detail={
            "code": code,
            "message": message
        },
        headers=headers
    )

# 导出所需的组件
__all__ = [
    "ErrorCode",
    "AppException",
    "handle_service_errors",
    "handle_result_error",
    "create_http_exception",
    "handle_route_errors",
    "ResultHandler",
    "AUTH_ERROR_MAPPING",
    "USER_ERROR_MAPPING",
    "ROLE_ERROR_MAPPING",
    "SUBSCRIPTION_ERROR_MAPPING",
    "INVOICE_ERROR_MAPPING",
    "PAYMENT_ERROR_MAPPING",
    "PLAN_ERROR_MAPPING",
    "CAMPAIGN_ERROR_MAPPING",
    "INVITATION_ERROR_MAPPING",
    "REWARD_ERROR_MAPPING",
    "MARKETING_ERROR_MAPPING",
    "PRODUCT_ERROR_MAPPING",
    "CATEGORY_ERROR_MAPPING",
    "PRODUCT_VARIANT_ERROR_MAPPING",
    "INVENTORY_ERROR_MAPPING",
    "PRODUCTS_ERROR_MAPPING"
]
