#!/usr/bin/env python
"""
错误处理标准化审查工具

此脚本用于检查服务类是否符合Result对象使用的最佳实践：
1. 是否正确继承了BaseService并指定了泛型参数
2. 是否设置了resource_type属性
3. 方法是否使用了Result[T]形式的返回类型
4. 是否使用了ErrorCode枚举而非直接使用数字错误码
"""
import os
import sys
import ast
import re
from typing import List, Dict, Any, Set, Tuple, Optional
import argparse
import logging
from pathlib import Path

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# 需要检查的模式
PATTERNS = {
    "base_service_generic": re.compile(r"BaseService\[\w+,\s*Result\[\w+(\[\w+\])?\]\]"),
    "result_return_type": re.compile(r"def\s+\w+\([^)]*\)\s*->\s*Result\[\w+(\[\w+\])?\]"),
    "error_code_enum": re.compile(r"error_code\s*=\s*ErrorCode\.\w+"),
    "numeric_error_code": re.compile(r"error_code\s*=\s*\d+"),
}

class ServiceClassVisitor(ast.NodeVisitor):
    """AST访问器，用于检查服务类是否符合标准"""
    
    def __init__(self):
        self.service_classes = []
        self.current_class = None
        self.errors = []
        
    def visit_ClassDef(self, node):
        # 检查是否是服务类（继承自BaseService）
        is_service_class = False
        for base in node.bases:
            if hasattr(base, 'id') and base.id == 'BaseService':
                is_service_class = True
            elif hasattr(base, 'value') and hasattr(base.value, 'id') and base.value.id == 'BaseService':
                is_service_class = True
                
        if is_service_class:
            self.current_class = {
                'name': node.name,
                'lineno': node.lineno,
                'has_resource_type': False,
                'has_valid_generic': False,
                'methods': [],
                'direct_numeric_error_codes': []
            }
            
            # 检查是否有泛型参数
            for base in node.bases:
                if hasattr(base, 'slice') and isinstance(base.slice, ast.Subscript):
                    # 有泛型参数，假设是正确的
                    self.current_class['has_valid_generic'] = True
            
            # 检查类体
            for item in node.body:
                # 检查resource_type属性
                if isinstance(item, ast.Assign):
                    for target in item.targets:
                        if hasattr(target, 'id') and target.id == 'resource_type':
                            self.current_class['has_resource_type'] = True
                
                # 递归访问方法
                self.visit(item)
            
            self.service_classes.append(self.current_class)
            self.current_class = None
        else:
            # 递归访问类体
            for item in node.body:
                self.visit(item)
    
    def visit_FunctionDef(self, node):
        if self.current_class is None:
            return
        
        # 检查方法的返回类型是否是Result[T]
        has_valid_return_type = False
        if node.returns:
            return_type = ast.unparse(node.returns)
            if 'Result[' in return_type:
                has_valid_return_type = True
        
        # 检查方法体中是否使用了数字错误码
        direct_numeric_error_codes = []
        for item in ast.walk(node):
            if isinstance(item, ast.keyword) and item.arg == 'error_code':
                if isinstance(item.value, ast.Num):
                    direct_numeric_error_codes.append((node.name, item.value.lineno))
        
        method_info = {
            'name': node.name,
            'lineno': node.lineno,
            'has_valid_return_type': has_valid_return_type,
            'direct_numeric_error_codes': direct_numeric_error_codes
        }
        
        self.current_class['methods'].append(method_info)
        
        # 添加发现的数字错误码
        if direct_numeric_error_codes:
            self.current_class['direct_numeric_error_codes'].extend(direct_numeric_error_codes)

def check_file(file_path: str) -> List[Dict[str, Any]]:
    """
    检查单个文件中的服务类是否符合标准
    
    Args:
        file_path: 文件路径
        
    Returns:
        List[Dict[str, Any]]: 检查结果
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        code = f.read()
    
    try:
        tree = ast.parse(code)
        visitor = ServiceClassVisitor()
        visitor.visit(tree)
        return visitor.service_classes
    except SyntaxError as e:
        logger.error(f"语法错误 {file_path}: {e}")
        return []

def scan_directory(directory: str, file_pattern: str = "*.py") -> List[Dict[str, Any]]:
    """
    扫描目录中的所有.py文件，检查服务类是否符合标准
    
    Args:
        directory: 目录路径
        file_pattern: 文件匹配模式
        
    Returns:
        List[Dict[str, Any]]: 检查结果
    """
    results = []
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)
                try:
                    file_results = check_file(file_path)
                    if file_results:
                        for result in file_results:
                            result['file'] = file_path
                        results.extend(file_results)
                except Exception as e:
                    logger.error(f"检查文件 {file_path} 时出错: {e}")
    
    return results

def generate_report(results: List[Dict[str, Any]]) -> Tuple[str, int]:
    """
    生成检查报告
    
    Args:
        results: 检查结果
        
    Returns:
        Tuple[str, int]: 报告文本和问题数量
    """
    if not results:
        return "未发现服务类", 0
    
    report_lines = ["错误处理标准化审查报告", "=" * 30, ""]
    issue_count = 0
    
    for result in results:
        class_name = result['name']
        file_path = result.get('file', '未知文件')
        
        class_issues = []
        
        # 检查泛型参数
        if not result['has_valid_generic']:
            class_issues.append(f"未指定泛型参数，应使用 BaseService[ModelType, Result[ResponseType]] 形式")
            issue_count += 1
        
        # 检查resource_type属性
        if not result['has_resource_type']:
            class_issues.append(f"未设置 resource_type 属性，应添加 resource_type = \"资源类型\" 属性")
            issue_count += 1
        
        # 检查方法返回类型
        for method in result['methods']:
            if not method['has_valid_return_type']:
                class_issues.append(f"方法 {method['name']} (行 {method['lineno']}) 未使用 Result[T] 形式的返回类型")
                issue_count += 1
        
        # 检查数字错误码
        for method_name, lineno in result['direct_numeric_error_codes']:
            class_issues.append(f"方法 {method_name} (行 {lineno}) 使用了数字错误码，应使用 ErrorCode 枚举")
            issue_count += 1
        
        if class_issues:
            report_lines.append(f"文件: {file_path}")
            report_lines.append(f"类: {class_name} (行 {result['lineno']})")
            for issue in class_issues:
                report_lines.append(f"  - {issue}")
            report_lines.append("")
    
    # 添加总结
    report_lines.append(f"总计: 发现 {issue_count} 个问题")
    
    return "\n".join(report_lines), issue_count

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='检查服务类是否符合Result对象使用的最佳实践')
    parser.add_argument('directory', help='要扫描的目录路径')
    parser.add_argument('--report', help='生成报告文件的路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='输出详细信息')
    
    args = parser.parse_args()
    
    if args.verbose:
        logger.setLevel(logging.DEBUG)
    
    logger.info(f"开始扫描目录: {args.directory}")
    results = scan_directory(args.directory)
    
    report, issue_count = generate_report(results)
    
    if args.report:
        with open(args.report, 'w', encoding='utf-8') as f:
            f.write(report)
        logger.info(f"报告已保存至: {args.report}")
    else:
        print(report)
    
    logger.info(f"扫描完成，共发现 {issue_count} 个问题")
    
    # 如果有问题，返回非零退出码
    return 1 if issue_count > 0 else 0

if __name__ == "__main__":
    sys.exit(main()) 