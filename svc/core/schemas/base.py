from typing import Generic, TypeVar, List, Optional
from pydantic import BaseModel, Field

from svc.core.models.base import CamelCaseModel

DataType = TypeVar('DataType')

class PageParams(CamelCaseModel):
    """
    通用分页参数模型
    """
    page_num: int = Field(1, ge=1, description="页码（从1开始）")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")

class BaseResponse(CamelCaseModel):
    """基础响应模型，可以包含通用字段"""
    is_success: bool = Field(True, description="操作是否成功")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_message: Optional[str] = Field(None, description="错误信息")

class DataResponse(CamelCaseModel, Generic[DataType]):
    """包含数据的响应模型"""
    data: Optional[DataType] = Field(None, description="响应数据")


class PaginatedResponse(CamelCaseModel, Generic[DataType]):
    """
    通用分页响应模型
    """
    items: List[DataType] = Field(..., description="当前页的数据列表")
    total: int = Field(..., description="总记录数")
    page_num: int = Field(..., description="当前页码 (从1开始)")
    page_size: int = Field(..., description="每页记录数")
    page_count: int = Field(..., description="总页数") 