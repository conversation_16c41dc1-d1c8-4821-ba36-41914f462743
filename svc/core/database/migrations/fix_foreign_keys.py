"""
修复数据库外键约束脚本

该脚本用于修复campaigns表与其依赖表之间的外键约束，
添加级联删除选项，解决迁移时无法删除表的问题。
"""
from sqlalchemy import MetaData, Table, Column, ForeignKey, create_engine
from sqlalchemy.engine import Engine
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_foreign_keys(engine_url: str):
    """修复外键约束关系
    
    Args:
        engine_url: 数据库连接URL
    """
    logger.info("开始修复外键约束关系...")
    
    # 创建数据库引擎
    engine = create_engine(engine_url)
    
    try:
        # 先删除现有的外键约束
        drop_fk_constraints(engine)
        
        # 重新创建带有CASCADE选项的外键约束
        create_cascade_fk_constraints(engine)
        
        logger.info("外键约束关系修复完成")
    except Exception as e:
        logger.error(f"修复外键约束关系时出错: {str(e)}")
        raise

def drop_fk_constraints(engine: Engine):
    """删除现有的外键约束
    
    Args:
        engine: 数据库引擎
    """
    logger.info("删除现有的外键约束...")
    
    with engine.connect() as conn:
        # 删除invitations表的campaign_id外键约束
        conn.execute("""
            ALTER TABLE invitations 
            DROP CONSTRAINT IF EXISTS invitations_campaign_id_fkey;
        """)
        
        # 删除rewards表的campaign_id外键约束
        conn.execute("""
            ALTER TABLE rewards 
            DROP CONSTRAINT IF EXISTS rewards_campaign_id_fkey;
        """)
        
        conn.commit()
    
    logger.info("现有外键约束已删除")

def create_cascade_fk_constraints(engine: Engine):
    """创建带有CASCADE选项的外键约束
    
    Args:
        engine: 数据库引擎
    """
    logger.info("创建带有CASCADE选项的外键约束...")
    
    with engine.connect() as conn:
        # 为invitations表添加带CASCADE选项的campaign_id外键约束
        conn.execute("""
            ALTER TABLE invitations 
            ADD CONSTRAINT invitations_campaign_id_fkey 
            FOREIGN KEY (campaign_id) 
            REFERENCES campaigns(id) 
            ON DELETE CASCADE;
        """)
        
        # 为rewards表添加带CASCADE选项的campaign_id外键约束
        conn.execute("""
            ALTER TABLE rewards 
            ADD CONSTRAINT rewards_campaign_id_fkey 
            FOREIGN KEY (campaign_id) 
            REFERENCES campaigns(id) 
            ON DELETE CASCADE;
        """)
        
        conn.commit()
    
    logger.info("带CASCADE选项的外键约束已创建")

if __name__ == "__main__":
    import sys
    import os
    from dotenv import load_dotenv
    
    # 加载环境变量
    load_dotenv()
    
    # 获取数据库连接URL
    db_url = os.getenv("DATABASE_URL")
    if not db_url:
        logger.error("未找到DATABASE_URL环境变量")
        sys.exit(1)
    
    # 执行修复
    fix_foreign_keys(db_url) 