"""
数据库迁移脚本：将UUID主键转换为自增整数。

迁移步骤：
1. 清理之前的迁移状态
2. 删除所有外键约束
3. 为每个表创建新的自增ID列
4. 创建UUID到新ID的映射
5. 重命名ID列
6. 更新所有外键引用
7. 重新添加外键约束
8. 清理旧的UUID列
"""

from typing import Dict, List, Tuple, Set
import asyncio
import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 需要迁移的表及其外键关系
TABLES_TO_MIGRATE = [
    ("users", []),  # users表没有外键依赖
    ("roles", []),  # roles表没有外键依赖
    ("campaigns", []),  # campaigns表没有外键依赖
    ("invitations", ["campaign_id", "inviter_id", "invitee_id", "parent_id"]),  # 依赖campaigns、users和self
    ("rewards", ["invitation_id", "user_id"]),  # 依赖invitations和users表
]

# 特殊处理的关联表
JUNCTION_TABLES = [
    "user_role",  # 用户角色关联表
]

# 表之间的外键关系
FK_CONSTRAINTS = {
    "invitations": [
        {"name": "fk_invitations_campaign_id", "column": "campaign_id", "ref_table": "campaigns", "ref_column": "id"},
        {"name": "fk_invitations_inviter_id", "column": "inviter_id", "ref_table": "users", "ref_column": "id"},
        {"name": "fk_invitations_invitee_id", "column": "invitee_id", "ref_table": "users", "ref_column": "id"},
        {"name": "fk_invitations_parent_id", "column": "parent_id", "ref_table": "invitations", "ref_column": "id"}
    ],
    "rewards": [
        {"name": "fk_rewards_invitation_id", "column": "invitation_id", "ref_table": "invitations", "ref_column": "id"},
        {"name": "fk_rewards_user_id", "column": "user_id", "ref_table": "users", "ref_column": "id"}
    ],
    "user_role": [
        {"name": "fk_user_role_user_id", "column": "user_id", "ref_table": "users", "ref_column": "id"},
        {"name": "fk_user_role_role_id", "column": "role_id", "ref_table": "roles", "ref_column": "id"}
    ]
}

async def cleanup_previous_migration(db: AsyncSession) -> None:
    """清理之前的迁移状态"""
    try:
        logger.info("清理之前的迁移状态...")
        # 删除映射表（如果存在）
        await db.execute(text("DROP TABLE IF EXISTS id_mappings"))
        await db.commit()
        
        # 删除新ID列（如果存在）
        for table, _ in TABLES_TO_MIGRATE:
            logger.info(f"删除表 {table} 的新ID列（如果存在）")
            await db.execute(text(f"""
                ALTER TABLE IF EXISTS {table}
                DROP COLUMN IF EXISTS new_id
            """))
            await db.commit()
        
        # 恢复user_role表（如果需要）
        logger.info("恢复user_role表的临时列（如果存在）")
        await db.execute(text("""
            ALTER TABLE IF EXISTS user_role
            DROP COLUMN IF EXISTS user_id_new,
            DROP COLUMN IF EXISTS role_id_new
        """))
        await db.commit()
        
        logger.info("清理完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"清理失败: {str(e)}")
        raise Exception(f"清理失败: {str(e)}")

async def drop_foreign_key_constraints(db: AsyncSession) -> None:
    """删除所有外键约束"""
    try:
        logger.info("开始删除所有外键约束...")
        
        # 遍历每个表及其外键约束
        for table, constraints in FK_CONSTRAINTS.items():
            for constraint in constraints:
                logger.info(f"删除表 {table} 上的外键约束 {constraint['name']}")
                await db.execute(text(f"""
                    ALTER TABLE IF EXISTS {table}
                    DROP CONSTRAINT IF EXISTS {constraint['name']}
                """))
                await db.commit()
        
        logger.info("所有外键约束已删除")
    except Exception as e:
        await db.rollback()
        logger.error(f"删除外键约束失败: {str(e)}")
        raise Exception(f"删除外键约束失败: {str(e)}")

async def create_id_mapping_table(db: AsyncSession) -> None:
    """创建ID映射表"""
    try:
        logger.info("创建ID映射表...")
        await db.execute(text("""
            CREATE TABLE IF NOT EXISTS id_mappings (
                table_name VARCHAR(255) NOT NULL,
                old_id UUID NOT NULL,
                new_id BIGINT NOT NULL,
                PRIMARY KEY (table_name, old_id)
            )
        """))
        await db.commit()
        logger.info("ID映射表创建完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"创建ID映射表失败: {str(e)}")
        raise Exception(f"创建ID映射表失败: {str(e)}")

async def add_new_id_column(db: AsyncSession, table: str) -> None:
    """为表添加新的自增ID列"""
    try:
        logger.info(f"为表 {table} 添加新的自增ID列...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            ADD COLUMN IF NOT EXISTS new_id BIGINT GENERATED ALWAYS AS IDENTITY
        """))
        await db.commit()
        logger.info(f"表 {table} 新ID列添加完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"为表 {table} 添加新ID列失败: {str(e)}")
        raise Exception(f"为表 {table} 添加新ID列失败: {str(e)}")

async def create_id_mapping(db: AsyncSession, table: str) -> None:
    """创建UUID到新ID的映射"""
    try:
        logger.info(f"为表 {table} 创建ID映射...")
        await db.execute(text(f"""
            INSERT INTO id_mappings (table_name, old_id, new_id)
            SELECT '{table}', id, new_id
            FROM {table}
        """))
        await db.commit()
        logger.info(f"表 {table} ID映射创建完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"为表 {table} 创建ID映射失败: {str(e)}")
        raise Exception(f"为表 {table} 创建ID映射失败: {str(e)}")

async def rename_id_columns(db: AsyncSession, table: str) -> None:
    """重命名ID列 - 分步执行以提高可靠性"""
    try:
        logger.info(f"开始重命名表 {table} 的ID列...")
        
        # 步骤1：删除主键约束
        logger.info(f"删除表 {table} 的主键约束...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            DROP CONSTRAINT {table}_pkey
        """))
        await db.commit()
        
        # 步骤2：修改旧ID列类型（确保兼容性）
        logger.info(f"修改表 {table} 的旧ID列类型...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            ALTER COLUMN id TYPE UUID USING id::uuid
        """))
        await db.commit()
        
        # 步骤3：设置新ID列属性
        logger.info(f"设置表 {table} 的新ID列属性...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            ALTER COLUMN new_id SET NOT NULL,
            ALTER COLUMN new_id SET DEFAULT nextval('{table}_new_id_seq')
        """))
        await db.commit()
        
        # 步骤4：添加新主键约束
        logger.info(f"为表 {table} 添加新主键约束...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            ADD PRIMARY KEY (new_id)
        """))
        await db.commit()
        
        # 步骤5：重命名列
        logger.info(f"重命名表 {table} 的ID列...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            RENAME COLUMN id TO old_id,
            RENAME COLUMN new_id TO id
        """))
        await db.commit()
        
        logger.info(f"表 {table} ID列重命名完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"重命名表 {table} 的ID列失败: {str(e)}")
        raise Exception(f"重命名表 {table} 的ID列失败: {str(e)}")

async def update_foreign_keys(db: AsyncSession, table: str, fk_columns: List[str]) -> None:
    """更新外键引用"""
    try:
        logger.info(f"开始更新表 {table} 的外键引用...")
        for fk_column in fk_columns:
            # 获取引用表名（从列名移除_id后缀）
            ref_table = fk_column.replace("_id", "s")
            
            logger.info(f"更新表 {table} 的外键列 {fk_column}...")
            # 更新外键列
            await db.execute(text(f"""
                UPDATE {table} t
                SET {fk_column} = m.new_id
                FROM id_mappings m
                WHERE m.table_name = '{ref_table}'
                AND t.{fk_column}::text = m.old_id::text
            """))
            await db.commit()
            
        logger.info(f"表 {table} 的外键引用更新完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"更新表 {table} 的外键引用失败: {str(e)}")
        raise Exception(f"更新表 {table} 的外键引用失败: {str(e)}")

async def add_foreign_key_constraints(db: AsyncSession) -> None:
    """重新添加外键约束"""
    try:
        logger.info("开始重新添加外键约束...")
        
        # 遍历每个表及其外键约束
        for table, constraints in FK_CONSTRAINTS.items():
            for constraint in constraints:
                logger.info(f"为表 {table} 添加外键约束 {constraint['name']}...")
                await db.execute(text(f"""
                    ALTER TABLE {table}
                    ADD CONSTRAINT {constraint['name']} 
                    FOREIGN KEY ({constraint['column']}) 
                    REFERENCES {constraint['ref_table']}({constraint['ref_column']})
                """))
                await db.commit()
        
        logger.info("所有外键约束添加完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"添加外键约束失败: {str(e)}")
        raise Exception(f"添加外键约束失败: {str(e)}")

async def cleanup_old_columns(db: AsyncSession, table: str) -> None:
    """清理旧的UUID列"""
    try:
        logger.info(f"清理表 {table} 的旧UUID列...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            DROP COLUMN old_id
        """))
        await db.commit()
        logger.info(f"表 {table} 旧UUID列清理完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"清理表 {table} 的旧UUID列失败: {str(e)}")
        raise Exception(f"清理表 {table} 的旧UUID列失败: {str(e)}")

async def migrate_junction_table(db: AsyncSession, table: str) -> None:
    """迁移关联表 - 分步执行"""
    try:
        logger.info(f"开始迁移关联表 {table}...")
        
        # 步骤1：创建临时列
        logger.info(f"为关联表 {table} 创建临时列...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            ADD COLUMN user_id_new BIGINT,
            ADD COLUMN role_id_new BIGINT
        """))
        await db.commit()
        
        # 步骤2：更新临时列引用
        logger.info(f"更新关联表 {table} 的user_id引用...")
        await db.execute(text(f"""
            UPDATE {table} t
            SET user_id_new = m.new_id
            FROM id_mappings m
            WHERE m.table_name = 'users'
            AND t.user_id::text = m.old_id::text
        """))
        await db.commit()
        
        logger.info(f"更新关联表 {table} 的role_id引用...")
        await db.execute(text(f"""
            UPDATE {table} t
            SET role_id_new = m.new_id
            FROM id_mappings m
            WHERE m.table_name = 'roles'
            AND t.role_id::text = m.old_id::text
        """))
        await db.commit()
        
        # 步骤3：删除旧约束和列
        logger.info(f"删除关联表 {table} 的旧主键约束...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            DROP CONSTRAINT user_role_pkey
        """))
        await db.commit()
        
        logger.info(f"删除关联表 {table} 的旧列...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            DROP COLUMN user_id,
            DROP COLUMN role_id
        """))
        await db.commit()
        
        # 步骤4：重命名新列
        logger.info(f"重命名关联表 {table} 的新列...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            RENAME COLUMN user_id_new TO user_id
        """))
        await db.commit()
        
        await db.execute(text(f"""
            ALTER TABLE {table}
            RENAME COLUMN role_id_new TO role_id
        """))
        await db.commit()
        
        # 步骤5：添加新约束
        logger.info(f"为关联表 {table} 添加新主键约束...")
        await db.execute(text(f"""
            ALTER TABLE {table}
            ADD PRIMARY KEY (user_id, role_id)
        """))
        await db.commit()
        
        logger.info(f"关联表 {table} 迁移完成")
    except Exception as e:
        await db.rollback()
        logger.error(f"迁移关联表 {table} 失败: {str(e)}")
        raise Exception(f"迁移关联表 {table} 失败: {str(e)}")

async def migrate_database(db: AsyncSession) -> None:
    """执行数据库迁移"""
    try:
        logger.info("开始执行数据库迁移...")
        
        # 阶段1：清理之前的迁移状态
        await cleanup_previous_migration(db)
        
        # 阶段2：删除所有外键约束
        await drop_foreign_key_constraints(db)
        
        # 阶段3：创建ID映射表
        await create_id_mapping_table(db)
        
        # 阶段4：添加新ID列并创建映射
        for table, _ in TABLES_TO_MIGRATE:
            await add_new_id_column(db, table)
            await create_id_mapping(db, table)
        
        # 阶段5：重命名所有表的ID列
        for table, _ in TABLES_TO_MIGRATE:
            await rename_id_columns(db, table)
        
        # 阶段6：更新所有外键引用
        for table, fk_columns in TABLES_TO_MIGRATE:
            if fk_columns:
                await update_foreign_keys(db, table, fk_columns)
        
        # 阶段7：重新添加所有外键约束
        await add_foreign_key_constraints(db)
        
        # 阶段8：迁移关联表
        for table in JUNCTION_TABLES:
            await migrate_junction_table(db, table)
        
        # 阶段9：清理
        for table, _ in TABLES_TO_MIGRATE:
            await cleanup_old_columns(db, table)
        
        # 删除映射表
        logger.info("删除ID映射表...")
        await db.execute(text("DROP TABLE id_mappings"))
        await db.commit()
        
        logger.info("数据库迁移成功完成！")
    except Exception as e:
        await db.rollback()
        logger.error(f"迁移失败: {str(e)}")
        raise Exception(f"迁移失败: {str(e)}")

async def rollback_migration(db: AsyncSession) -> None:
    """回滚迁移（如果需要）"""
    logger.info("执行迁移回滚...")
    # 实现回滚逻辑
    pass 