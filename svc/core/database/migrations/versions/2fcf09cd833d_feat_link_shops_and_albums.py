"""feat: link shops and albums

版本信息：
- Revision ID: 2fcf09cd833d
- 上一版本: 2ed26f852690
- 创建时间: 2025-06-20 13:45:35.408119

变更说明：
[在此处添加本次迁移的主要变更内容]

影响的表：
[列出本次迁移涉及的所有表]

注意事项：
[列出迁移时需要注意的特殊情况]
"""

# 标准库导入
from datetime import datetime
from typing import Dict, List, Optional, Union

# 第三方库导入
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects import postgresql

# 版本标识
revision = '2fcf09cd833d'
down_revision = '2ed26f852690'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """执行迁移升级。
    
    本次升级包含以下变更：
    1. [变更1描述]
    2. [变更2描述]
    ...
    
    注意：
    - [注意事项1]
    - [注意事项2]
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_shop_images_shop_id', table_name='shop_images')
    op.drop_table('shop_images')
    op.add_column('shops', sa.Column('album_id', sa.BigInteger(), nullable=True, comment='关联图册ID'))
    op.create_unique_constraint(None, 'shops', ['album_id'])
    op.create_foreign_key(None, 'shops', 'albums', ['album_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """执行迁移回滚。
    
    本次回滚将：
    1. [回滚步骤1描述]
    2. [回滚步骤2描述]
    ...
    
    注意：
    - 回滚顺序与升级顺序相反
    - 请确保数据已备份
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'shops', type_='foreignkey')
    op.drop_constraint(None, 'shops', type_='unique')
    op.drop_column('shops', 'album_id')
    op.create_table('shop_images',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('shop_id', sa.BIGINT(), autoincrement=False, nullable=False, comment='门店ID'),
    sa.Column('image_url', sa.TEXT(), autoincrement=False, nullable=False, comment='图片URL'),
    sa.Column('caption', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='图片标题'),
    sa.Column('is_cover', sa.BOOLEAN(), autoincrement=False, nullable=False, comment='是否为封面图片'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['shop_id'], ['shops.id'], name='shop_images_shop_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='shop_images_pkey')
    )
    op.create_index('ix_shop_images_shop_id', 'shop_images', ['shop_id'], unique=False)
    # ### end Alembic commands ### 