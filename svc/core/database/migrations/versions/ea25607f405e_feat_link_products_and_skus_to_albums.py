"""feat: link products and skus to albums

版本信息：
- Revision ID: ea25607f405e
- 上一版本: 2fcf09cd833d
- 创建时间: 2025-06-20 14:02:07.157967

变更说明：
[在此处添加本次迁移的主要变更内容]

影响的表：
[列出本次迁移涉及的所有表]

注意事项：
[列出迁移时需要注意的特殊情况]
"""

# 标准库导入
from datetime import datetime
from typing import Dict, List, Optional, Union

# 第三方库导入
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.dialects import postgresql

# 版本标识
revision = 'ea25607f405e'
down_revision = '2fcf09cd833d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """执行迁移升级。
    
    本次升级包含以下变更：
    1. [变更1描述]
    2. [变更2描述]
    ...
    
    注意：
    - [注意事项1]
    - [注意事项2]
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('product_spec_combinations', sa.Column('album_id', sa.BigInteger(), nullable=True, comment='关联图册ID'))
    op.create_unique_constraint(None, 'product_spec_combinations', ['album_id'])
    op.create_foreign_key(None, 'product_spec_combinations', 'albums', ['album_id'], ['id'])
    op.add_column('products', sa.Column('album_id', sa.BigInteger(), nullable=True, comment='关联图册ID'))
    op.create_unique_constraint(None, 'products', ['album_id'])
    op.create_foreign_key(None, 'products', 'albums', ['album_id'], ['id'])
    op.drop_column('products', 'spec_image_urls')
    op.drop_column('products', 'cover_image_url')
    op.drop_column('products', 'carousel_image_urls')
    # ### end Alembic commands ###


def downgrade() -> None:
    """执行迁移回滚。
    
    本次回滚将：
    1. [回滚步骤1描述]
    2. [回滚步骤2描述]
    ...
    
    注意：
    - 回滚顺序与升级顺序相反
    - 请确保数据已备份
    """
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('products', sa.Column('carousel_image_urls', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='产品详情轮播图URL列表'))
    op.add_column('products', sa.Column('cover_image_url', sa.VARCHAR(length=500), autoincrement=False, nullable=True, comment='产品封面图片URL'))
    op.add_column('products', sa.Column('spec_image_urls', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='产品规格图片URL列表'))
    op.drop_constraint(None, 'products', type_='foreignkey')
    op.drop_constraint(None, 'products', type_='unique')
    op.drop_column('products', 'album_id')
    op.drop_constraint(None, 'product_spec_combinations', type_='foreignkey')
    op.drop_constraint(None, 'product_spec_combinations', type_='unique')
    op.drop_column('product_spec_combinations', 'album_id')
    # ### end Alembic commands ### 