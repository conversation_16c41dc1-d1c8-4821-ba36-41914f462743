"""${message}

版本信息：
- Revision ID: ${up_revision}
- 上一版本: ${down_revision | comma,n}
- 创建时间: ${create_date}

变更说明：
[在此处添加本次迁移的主要变更内容]

影响的表：
[列出本次迁移涉及的所有表]

注意事项：
[列出迁移时需要注意的特殊情况]
"""

# 标准库导入
from datetime import datetime
from typing import Dict, List, Optional, Union

# 第三方库导入
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
${imports if imports else ""}

# 版本标识
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    """执行迁移升级。
    
    本次升级包含以下变更：
    1. [变更1描述]
    2. [变更2描述]
    ...
    
    注意：
    - [注意事项1]
    - [注意事项2]
    """
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    """执行迁移回滚。
    
    本次回滚将：
    1. [回滚步骤1描述]
    2. [回滚步骤2描述]
    ...
    
    注意：
    - 回滚顺序与升级顺序相反
    - 请确保数据已备份
    """
    ${downgrades if downgrades else "pass"} 