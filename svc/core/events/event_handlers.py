"""
事件处理器模块，使用新的事件总线系统
用于注册和处理各种系统事件的处理函数
"""
import logging
from fastapi_events.handlers.base import BaseEventHandler
from fastapi_events.typing import Event
logger = logging.getLogger(__name__)


class DictPayloadHandler(BaseEventHandler):
    def __init__(self):
        super().__init__()
    async def handle(self, event: Event) -> None:
        event_name, payload = event
        if not isinstance(payload, dict):
            payload = {"payload": payload}
        await self._handle_event((event_name, payload))

    async def _handle_event(self, event: Event):
        for listener in self._listeners:
            await listener(event)

dict_handler = DictPayloadHandler()