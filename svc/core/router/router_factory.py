"""
路由工厂模块。
提供自动路由注册功能。
"""
from fastapi import FastAPI
from pathlib import Path
import importlib
import logging
from typing import Set, Dict, Any, List
from fastapi import APIRouter

from svc.core.config.settings import get_settings,Settings

logger = logging.getLogger(__name__)

def register_routers(app: FastAPI) -> None:
    """
    自动注册所有模块的路由。
    
    Args:
        app: FastAPI应用实例
    """
    # 获取配置
    settings = get_settings()
    
    # 获取apps目录的路径
    apps_dir = Path(__file__).parent.parent.parent / "apps"
    
    # 已注册的模块集合，确保每个模块只注册一次
    registered_modules = set()
    
    # 已注册的路由路径集合，用于检测重复注册
    registered_routes: Dict[str, List[str]] = {}
    
    # 优先级模块列表，这些模块将首先注册
    priority_modules = ["auth"]
    
    # 首先注册优先级模块
    for module_name in priority_modules:
        module_dir = apps_dir / module_name
        if not module_dir.is_dir():
            logger.warning(f"优先级模块 {module_name} 不存在")
            continue
        
        # 尝试通过模块级注册
        # if _register_module_router(app, module_name, module_dir, registered_routes, settings):
        #     registered_modules.add(module_name)
        #     logger.info(f"已注册优先级模块 {module_name} 的路由")
        # else:
        # 尝试通过文件级注册
        if _register_file_routers(app, module_name, module_dir, registered_routes, settings):
            registered_modules.add(module_name)
            logger.debug(f"已通过文件级注册优先级模块 {module_name} 的路由")
        else:
            logger.warning(f"无法注册优先级模块 {module_name} 的路由")
    
    # 遍历apps目录下的所有模块
    for module_dir in apps_dir.iterdir():
        if not module_dir.is_dir() or module_dir.name.startswith("__"):
            continue
        
        # 跳过已注册的模块
        if module_dir.name in registered_modules:
            logger.debug(f"模块 {module_dir.name} 已注册，跳过")
            continue
        
        # 尝试通过模块级注册
        # if _register_module_router(app, module_dir.name, module_dir, registered_routes, settings):
        #     registered_modules.add(module_dir.name)
        #     continue
        
        # 如果模块级注册失败，尝试文件级注册
        if _register_file_routers(app, module_dir.name, module_dir, registered_routes, settings):
            registered_modules.add(module_dir.name)
            continue
        
        logger.debug(f"模块 {module_dir.name} 没有可注册的路由")
    
    # 打印注册的路由统计信息
    route_count = sum(len(methods) for methods in registered_routes.values())
    logger.info(f"共注册了 {len(registered_routes)} 个路由路径，{route_count} 个端点")


def _register_module_router(
    app: FastAPI, 
    module_name: str, 
    module_dir: Path, 
    registered_routes: Dict[str, List[str]],
    settings: Settings
) -> bool:
    """
    注册模块级路由（通过__init__.py）。
    
    Args:
        app: FastAPI应用实例
        module_name: 模块名称
        module_dir: 模块目录路径
        registered_routes: 已注册的路由集合
        settings: 应用配置
        
    Returns:
        bool: 是否成功注册了路由
    """
    # 检查是否存在routers/__init__.py文件
    router_init_path = module_dir / "routers" / "__init__.py"
    if not router_init_path.exists():
        logger.debug(f"模块 {module_name} 没有路由初始化文件")
        return False
    
    try:
        # 动态导入模块
        module_path = f"svc.apps.{module_name}.routers"
        module = importlib.import_module(module_path)
        
        # 检查是否存在router变量
        if hasattr(module, "router"):
            logger.info(f"注册模块 {module_name} 的路由")
            
            # 获取路由前注册的路由数量和路由列表
            before_count = len(app.routes)
            before_routes = set(route.path for route in app.routes)
            
            # 检查路由是否已经设置了标签
            router = module.router
            has_tags = hasattr(router, "tags") and router.tags
            
            # 创建API路由器
            router = APIRouter(
                prefix=settings.api_prefix,
                tags=[module_name.title()]
            )
            
            # 注册路由，如果路由已经有标签，则不添加新的标签
            if has_tags:
                logger.debug(f"模块 {module_name} 的路由已设置标签: {router.tags}，不添加新标签")
                app.include_router(
                    router
                )
            else:
                app.include_router(
                    router,
                    tags=[module_name]
                )
            
            # 获取路由后注册的路由数量
            after_count = len(app.routes)
            new_routes = after_count - before_count
            
            # 更新已注册的路由路径集合，只检查新添加的路由
            _update_registered_routes(app, registered_routes, before_routes)
            
            logger.info(f"模块 {module_name} 注册了 {new_routes} 个新路由")
            return True
        else:
            logger.debug(f"模块 {module_name} 没有定义router变量")
            return False
    except Exception as e:
        logger.error(f"注册模块 {module_name} 的路由时出错: {str(e)}")
        return False


def _register_file_routers(
    app: FastAPI, 
    module_name: str, 
    module_dir: Path, 
    registered_routes: Dict[str, List[str]],
    settings: Settings
) -> bool:
    """
    注册文件级路由（直接导入路由文件）。
    
    Args:
        app: FastAPI应用实例
        module_name: 模块名称
        module_dir: 模块目录路径
        registered_routes: 已注册的路由集合
        settings: 应用配置
        
    Returns:
        bool: 是否成功注册了任何路由
    """
    routers_dir = module_dir / "routers"
    if not routers_dir.is_dir():
        logger.debug(f"模块 {module_name} 没有routers目录")
        return False
    
    # 标记是否成功注册了任何路由
    registered_any = False
    
    # 遍历routers目录下的所有Python文件
    for router_file in routers_dir.glob("*.py"):
        # 跳过__init__.py和以_开头的文件
        if router_file.name == "__init__.py" or router_file.name.startswith("_"):
            continue
        
        # 获取文件名（不含扩展名）
        router_name = router_file.stem
        
        try:
            # 构建模块路径
            module_path = f"svc.apps.{module_name}.routers.{router_name}"
            
            # 动态导入模块
            module = importlib.import_module(module_path)

            # 检查是否存在router变量
            if hasattr(module, "router"):
                logger.debug(f"注册路由文件 {module_name}.{router_name}")
                
                # 获取路由前注册的路由数量和路由列表
                before_count = len(app.routes)
                before_routes = set(route.path for route in app.routes)
                
                # 检查路由是否已经设置了标签
                router = module.router
                has_tags = hasattr(router, "tags") and router.tags
                
              
                
                # 注册路由，如果路由已经有标签，则不添加新的标签
                if has_tags:
                    logger.debug(f"路由文件 {module_name}.{router_name} 的路由已设置标签: {router.tags}，不添加新标签")
                    app.include_router(
                        router,
                        prefix=f"{settings.api_prefix}/{router_name}",
                    )
                else:
                    app.include_router(
                        router,
                        tags=[f"{module_name}_{router_name}"],
                        prefix=f"{settings.api_prefix}/{router_name}",
                    )
                
                # 获取路由后注册的路由数量
                after_count = len(app.routes)
                new_routes = after_count - before_count
                
                # 更新已注册的路由路径集合，只检查新添加的路由
                _update_registered_routes(app, registered_routes, before_routes)
                
                logger.debug(f"路由文件 {module_name}.{router_name} 注册了 {new_routes} 个新路由")
                registered_any = True
            else:
                logger.debug(f"路由文件 {module_name}.{router_name} 没有定义router变量")
        except Exception as e:
            logger.error(f"注册路由文件 {module_name}.{router_name} 时出错: {str(e)}")
    
    return registered_any


def _update_registered_routes(app: FastAPI, registered_routes: Dict[str, List[str]], before_routes: Set[str] = None) -> None:
    """
    更新已注册的路由路径集合，只检查新添加的路由。
    
    Args:
        app: FastAPI应用实例
        registered_routes: 已注册的路由集合
        before_routes: 注册前的路由集合，用于只检查新添加的路由
    """
    from fastapi.routing import APIRoute
    
    for route in app.routes:
        if isinstance(route, APIRoute):
            path = route.path
            
            # 如果提供了before_routes，只检查新添加的路由
            if before_routes is not None and path in before_routes:
                continue
                
            methods = list(route.methods)
            
            if path not in registered_routes:
                registered_routes[path] = methods
            else:
                # 检查是否有重复的HTTP方法
                for method in methods:
                    if method in registered_routes[path]:
                        logger.warning(f"路由 {path} [{method}] 已经注册，可能存在重复注册")
                    else:
                        registered_routes[path].append(method)
