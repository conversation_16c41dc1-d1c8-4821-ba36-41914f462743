"""
自定义数据库类型模块。
提供数据库特定类型的装饰器和工具类。
"""
from pydantic import BaseModel, ConfigDict
from sqlalchemy import JSON, types
from sqlalchemy.dialects.postgresql import JSONB


class DatabaseCompatibleJSON(types.TypeDecorator):
    """
    根据数据库类型自动选择合适的JSON类型的自定义类型装饰器。
    对PostgreSQL使用JSONB，对其他数据库使用JSON。
    """
    impl = JSON
    
    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(JSONB())
        else:
            return dialect.type_descriptor(JSON()) 
        

class APIModel(BaseModel):
    """
    自定义API模型类型装饰器。
    将API模型转换为JSON字符串存储在数据库中。
    """
    model_config = ConfigDict(
        alias_generator = lambda s: s.replace('_', ''),  # 自定义转换逻辑
        populate_by_name = True
    )
    