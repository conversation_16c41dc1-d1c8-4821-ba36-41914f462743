"""
通用基础模型定义
包含应用中所有模型共享的基类和工具
"""
from typing import Any, Dict, Generic, List, Optional, TypeVar, Union
from pydantic import BaseModel, ConfigDict, Field
from sqlalchemy.orm import declarative_base

from svc.core.utils.string_utils import to_camel_case




class CamelCaseModel(BaseModel):
    """支持camelCase字段输出的基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        alias_generator=to_camel_case,
        populate_by_name=True,
    )


Base = declarative_base()


