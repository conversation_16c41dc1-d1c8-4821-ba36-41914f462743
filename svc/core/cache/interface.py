"""
缓存接口定义。
所有具体缓存实现必须遵循此接口。
"""
from abc import ABC, abstractmethod
from typing import TypeVar, Generic, Optional, Any, List

K = TypeVar('K')  # 键类型
V = TypeVar('V')  # 值类型

class CacheInterface(Generic[K, V], ABC):
    """缓存接口，定义所有缓存实现必须提供的方法"""
    
    @abstractmethod
    async def get(self, key: K) -> Optional[V]:
        """获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，如果不存在或已过期则返回None
        """
        pass
        
    @abstractmethod
    async def set(self, key: K, value: V, ttl: Optional[int] = None) -> None:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒），None表示使用默认值或永不过期
        """
        pass
        
    @abstractmethod
    async def delete(self, key: K) -> None:
        """删除缓存项
        
        Args:
            key: 缓存键
        """
        pass
        
    @abstractmethod
    async def exists(self, key: K) -> bool:
        """检查键是否存在且未过期
        
        Args:
            key: 缓存键
            
        Returns:
            如果键存在且未过期则返回True
        """
        pass
        
    @abstractmethod
    async def clear(self) -> None:
        """清空缓存"""
        pass 