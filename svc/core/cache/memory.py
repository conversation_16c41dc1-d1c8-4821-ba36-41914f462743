"""
内存缓存实现。
提供基于内存的缓存实现，支持过期时间和LRU淘汰策略。
"""
import time
import logging
import asyncio
import os
import psutil
from typing import Dict, Any, Optional, TypeVar, Generic, List, Tuple, Union

from .interface import CacheInterface, K, V
from .config import CacheConfig

logger = logging.getLogger(__name__)

class MemoryCache(CacheInterface[K, V]):
    """内存缓存实现，支持LRU淘汰策略和内存监控"""
    
    def __init__(self, config: CacheConfig):
        """初始化内存缓存
        
        Args:
            config: 缓存配置
        """
        self.config = config
        self._cache: Dict[K, Tuple[V, Optional[float]]] = {}
        self._access_count: Dict[K, int] = {}  # 记录访问次数（用于LRU）
        self._cleanup_task = None
        self._process = psutil.Process(os.getpid())
        
        logger.info(
            f"初始化内存缓存: 命名空间={config.namespace}, "
            f"清理间隔={config.cleanup_interval}秒, "
            f"最大条目数={config.max_items}, "
            f"最大内存占用={config.max_memory_percent}%"
        )
    
    async def get(self, key: K) -> Optional[V]:
        """获取缓存值"""
        if key not in self._cache:
            return None
        
        value, expiry = self._cache[key]
        
        # 检查是否过期
        if expiry is not None and time.time() > expiry:
            await self.delete(key)
            return None
        
        # 更新访问计数（LRU跟踪）
        self._access_count[key] = self._access_count.get(key, 0) + 1
        
        return value
    
    async def set(self, key: K, value: V, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        # 使用配置的默认TTL（如果未提供）
        ttl = ttl if ttl is not None else self.config.ttl
        
        # 检查是否需要进行LRU清理
        if len(self._cache) >= self.config.max_items:
            await self._lru_cleanup(int(self.config.max_items * 0.2))  # 清理20%的条目
        
        # 检查内存使用率
        if await self._check_memory_usage():
            logger.warning(f"内存使用率超过阈值 {self.config.max_memory_percent}%，进行紧急清理")
            await self._emergency_cleanup()
        
        # 设置过期时间（如果有）
        expiry = time.time() + ttl if ttl is not None else None
        self._cache[key] = (value, expiry)
        self._access_count[key] = 0
    
    async def delete(self, key: K) -> None:
        """删除缓存项"""
        if key in self._cache:
            del self._cache[key]
        
        if key in self._access_count:
            del self._access_count[key]
    
    async def exists(self, key: K) -> bool:
        """检查键是否存在且未过期"""
        if key not in self._cache:
            return False
        
        _, expiry = self._cache[key]
        
        # 检查是否过期
        if expiry is not None and time.time() > expiry:
            await self.delete(key)
            return False
        
        return True
    
    async def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._access_count.clear()
        logger.info(f"缓存已清空: {self.config.namespace}")
    
    async def _lru_cleanup(self, count: int) -> None:
        """基于LRU策略清理缓存
        
        Args:
            count: 要清理的项数
        """
        if count <= 0 or not self._cache:
            return
        
        # 按访问次数排序
        sorted_keys = sorted(
            self._access_count.keys(),
            key=lambda k: self._access_count.get(k, 0)
        )
        
        # 删除访问次数最少的项
        keys_to_delete = sorted_keys[:min(count, len(sorted_keys))]
        for key in keys_to_delete:
            await self.delete(key)
        
        logger.info(f"LRU清理完成: 删除了 {len(keys_to_delete)} 个最少使用项，当前缓存大小 {len(self._cache)}")
    
    async def _check_memory_usage(self) -> bool:
        """检查内存使用率是否超过阈值
        
        Returns:
            如果内存使用率超过阈值则返回True
        """
        memory_percent = self._process.memory_percent()
        return memory_percent > self.config.max_memory_percent
    
    async def _emergency_cleanup(self) -> None:
        """紧急清理缓存，当内存使用率过高时调用"""
        # 首先清理所有过期项
        await self._cleanup()
        
        # 如果内存使用率仍然过高，则进行LRU清理
        if await self._check_memory_usage():
            # 清理50%的缓存项
            await self._lru_cleanup(int(len(self._cache) * 0.5))
    
    async def _cleanup(self) -> None:
        """清理过期的缓存项"""
        now = time.time()
        expired_keys = []
        
        # 找出所有过期的键
        for key, (_, expiry) in self._cache.items():
            if expiry is not None and now > expiry:
                expired_keys.append(key)
        
        # 删除过期的键
        for key in expired_keys:
            await self.delete(key)
        
        logger.debug(f"缓存清理完成: 删除了 {len(expired_keys)} 个过期项，当前缓存大小 {len(self._cache)}")
        
        # 记录内存使用情况
        memory_info = self._process.memory_info()
        memory_percent = self._process.memory_percent()
        logger.debug(f"内存使用: {memory_info.rss / 1024 / 1024:.2f} MB ({memory_percent:.2f}%)")
    
    async def start_cleanup_task(self) -> None:
        """启动定期清理任务"""
        if self._cleanup_task is not None:
            return
        
        async def _cleanup_loop():
            while True:
                try:
                    await self._cleanup()
                except Exception as e:
                    logger.error(f"缓存清理出错: {e}")
                
                await asyncio.sleep(self.config.cleanup_interval)
        
        self._cleanup_task = asyncio.create_task(_cleanup_loop())
        logger.info(f"启动缓存定期清理任务，间隔 {self.config.cleanup_interval} 秒")
    
    async def stop_cleanup_task(self) -> None:
        """停止定期清理任务"""
        if self._cleanup_task is not None:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            
            self._cleanup_task = None
            logger.info("已停止缓存定期清理任务")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            包含缓存大小、内存使用等信息的字典
        """
        # 计算过期项数量
        now = time.time()
        expired_count = sum(1 for _, expiry in self._cache.values() 
                           if expiry is not None and now > expiry)
        
        # 获取内存使用情况
        memory_info = self._process.memory_info()
        memory_percent = self._process.memory_percent()
        
        return {
            "namespace": self.config.namespace,
            "total_items": len(self._cache),
            "expired_items": expired_count,
            "memory_usage_mb": memory_info.rss / 1024 / 1024,
            "memory_percent": memory_percent,
            "max_items": self.config.max_items,
            "max_memory_percent": self.config.max_memory_percent
        } 