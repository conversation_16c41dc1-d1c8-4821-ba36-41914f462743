"""
缓存装饰器模块。
提供函数级别的缓存支持，包括自动缓存、缓存失效等功能。
"""
import inspect
import json
import logging
from datetime import timedelta
from functools import wraps
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Union, cast

from fastapi.encoders import jsonable_encoder

from .base import CacheBase
from .memory import MemoryCache
from .redis import RedisCache, get_redis_cache

logger = logging.getLogger(__name__)

# 定义类型变量
T = TypeVar("T")

def cached(
    prefix: str = "",
    expire: Optional[Union[int, timedelta]] = None,
    cache_type: str = "redis",
    key_builder: Optional[Callable[..., str]] = None,
    namespace: str = "",
    skip_cache_if: Optional[Callable[..., bool]] = None,
    tenant_param: Optional[str] = None
) -> Callable:
    """
    缓存装饰器，用于自动缓存函数返回值。
    
    Args:
        prefix: 缓存键前缀
        expire: 过期时间（秒或timedelta对象）
        cache_type: 缓存类型，支持"redis"和"memory"
        key_builder: 自定义缓存键生成函数
        namespace: 缓存命名空间
        skip_cache_if: 跳过缓存的条件函数
        tenant_param: 租户ID参数名，用于租户隔离
        
    Returns:
        装饰后的函数
    
    Examples:
        @cached(prefix="user", expire=300)
        async def get_user(user_id: int) -> Dict:
            # 数据库查询等耗时操作
            return user_data
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        # 获取函数签名
        sig = inspect.signature(func)
        
        async def default_key_builder(*args: Any, **kwargs: Any) -> str:
            """默认的缓存键生成函数"""
            # 将位置参数转换为关键字参数
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            
            # 构建参数字典
            params = {}
            for name, value in bound_args.arguments.items():
                # 跳过self和cls参数
                if name in ("self", "cls"):
                    continue
                # 处理特殊类型
                if inspect.isclass(value):
                    params[name] = value.__name__
                elif hasattr(value, "__dict__"):
                    params[name] = str(value)
                else:
                    params[name] = value
            
            # 处理租户隔离
            if tenant_param and tenant_param in params:
                tenant_id = params.pop(tenant_param)
                key_parts = [f"tenant:{tenant_id}"]
                if prefix:
                    key_parts.append(prefix)
                else:
                    key_parts.append(func.__name__)
            else:
                key_parts = [prefix or func.__name__]
                
            # 添加参数
            for name, value in sorted(params.items()):
                key_parts.append(f"{name}={value}")
            
            return ":".join(key_parts)
        
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # 获取缓存实例
            cache: CacheBase
            if cache_type == "redis":
                cache = await get_redis_cache(namespace)
            else:
                cache = MemoryCache(namespace)
            
            # 检查是否需要跳过缓存
            if skip_cache_if and skip_cache_if(*args, **kwargs):
                return await func(*args, **kwargs)
            
            # 生成缓存键
            key = await (key_builder or default_key_builder)(*args, **kwargs)
            
            # 尝试从缓存获取
            cached_value = await cache.get(key)
            if cached_value is not None:
                return cast(T, cached_value)
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 缓存结果
            try:
                await cache.set(key, result, expire)
            except Exception as e:
                logger.error(f"Error caching result for key {key}: {e}")
            
            return result
        
        return cast(Callable[..., T], wrapper)
    return decorator


def invalidate_cache(
    key_pattern: str,
    cache_type: str = "redis",
    namespace: str = "",
    tenant_param: Optional[str] = None
) -> Callable:
    """
    缓存失效装饰器，用于在函数执行后使指定模式的缓存键失效。
    
    Args:
        key_pattern: 缓存键模式，支持*通配符
        cache_type: 缓存类型，支持"redis"和"memory"
        namespace: 缓存命名空间
        tenant_param: 租户ID参数名，用于租户隔离
        
    Returns:
        装饰后的函数
        
    Examples:
        @invalidate_cache("user:*")
        async def update_user(user_id: int, data: Dict):
            # 更新用户数据
            pass
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        # 获取函数签名
        sig = inspect.signature(func)
        
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # 提取租户ID
            tenant_id = None
            if tenant_param:
                tenant_id = kwargs.get(tenant_param)
                if not tenant_id and tenant_param in sig.parameters:
                    # 尝试从位置参数中获取
                    bound_args = sig.bind_partial(*args, **kwargs)
                    bound_args.apply_defaults()
                    if tenant_param in bound_args.arguments:
                        tenant_id = bound_args.arguments[tenant_param]
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 获取缓存实例
            cache: CacheBase
            if cache_type == "redis":
                cache = await get_redis_cache(namespace)
            else:
                cache = MemoryCache(namespace)
            
            try:
                # 构建模式
                pattern = key_pattern
                if tenant_id and tenant_param:
                    pattern = f"tenant:{tenant_id}:{key_pattern}"
                
                if namespace:
                    pattern = f"{namespace}:{pattern}"
                    
                # 对于Redis缓存，使用SCAN命令查找匹配的键
                if isinstance(cache, RedisCache):
                    cursor = 0
                    while True:
                        cursor, keys = await cache.redis.scan(
                            cursor=cursor,
                            match=pattern,
                            count=100
                        )
                        if keys:
                            await cache.redis.delete(*keys)
                            # 从统计中移除键
                            for key in keys:
                                if key in cache._stats["keys"]:
                                    cache._stats["keys"].remove(key)
                        if cursor == 0:
                            break
                # 对于内存缓存，遍历所有键
                else:
                    keys_to_delete = []
                    for key in cache._cache.keys():
                        if key.startswith(f"{namespace}:") if namespace else True:
                            if tenant_id and tenant_param:
                                # 检查租户前缀
                                tenant_prefix = f"tenant:{tenant_id}:"
                                if key.startswith(tenant_prefix):
                                    # 检查键模式
                                    pattern_to_check = key[len(tenant_prefix):]
                                    if pattern_to_check.startswith(key_pattern.replace("*", "")):
                                        keys_to_delete.append(key)
                            elif key.startswith(key_pattern.replace("*", "")):
                                keys_to_delete.append(key)
                    
                    for key in keys_to_delete:
                        await cache.delete(key)
            except Exception as e:
                logger.error(f"Error invalidating cache with pattern {pattern}: {e}")
            
            return result
        
        return cast(Callable[..., T], wrapper)
    return decorator


def cache_stampede_prevention(
    prefix: str = "",
    expire: Optional[Union[int, timedelta]] = None,
    cache_type: str = "redis",
    namespace: str = "",
    threshold: int = 10,
    tenant_param: Optional[str] = None
) -> Callable:
    """
    缓存雪崩预防装饰器，使用概率提前刷新来防止缓存雪崩。
    
    Args:
        prefix: 缓存键前缀
        expire: 过期时间（秒或timedelta对象）
        cache_type: 缓存类型，支持"redis"和"memory"
        namespace: 缓存命名空间
        threshold: 提前刷新阈值（秒）
        tenant_param: 租户ID参数名，用于租户隔离
        
    Returns:
        装饰后的函数
        
    Examples:
        @cache_stampede_prevention(prefix="stats", expire=300)
        async def get_system_stats() -> Dict:
            # 计算系统统计信息
            return stats
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        # 获取函数签名
        sig = inspect.signature(func)
        
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # 提取租户ID
            tenant_id = None
            if tenant_param:
                tenant_id = kwargs.get(tenant_param)
                if not tenant_id and tenant_param in sig.parameters:
                    # 尝试从位置参数中获取
                    bound_args = sig.bind_partial(*args, **kwargs)
                    bound_args.apply_defaults()
                    if tenant_param in bound_args.arguments:
                        tenant_id = bound_args.arguments[tenant_param]
                        
            # 获取缓存实例
            cache: CacheBase
            if cache_type == "redis":
                cache = await get_redis_cache(namespace)
            else:
                cache = MemoryCache(namespace)
            
            # 生成缓存键
            key = f"{prefix}:{func.__name__}"
            if args or kwargs:
                params = json.dumps(
                    jsonable_encoder({"args": args, "kwargs": kwargs}),
                    sort_keys=True
                )
                key = f"{key}:{params}"
                
            # 处理租户隔离
            if tenant_id and tenant_param:
                key = f"tenant:{tenant_id}:{key}"
                
            # 获取缓存值和TTL
            cached_value = await cache.get(key)
            ttl = await cache.get_ttl(key) if isinstance(cache, RedisCache) else None
            
            # 如果缓存存在且未过期
            if cached_value is not None and (ttl is None or ttl > 0):
                # 如果TTL小于阈值，有一定概率提前刷新
                if ttl is not None and ttl < threshold:
                    import random
                    if random.random() < 0.1:  # 10%概率提前刷新
                        try:
                            result = await func(*args, **kwargs)
                            await cache.set(key, result, expire)
                            return result
                        except Exception:
                            return cached_value
                return cached_value
            
            # 缓存不存在或已过期，执行原函数
            result = await func(*args, **kwargs)
            try:
                await cache.set(key, result, expire)
            except Exception as e:
                logger.error(f"Error caching result for key {key}: {e}")
            
            return result
        
        return cast(Callable[..., T], wrapper)
    return decorator 