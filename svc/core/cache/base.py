"""
缓存抽象基类，定义缓存接口和基本功能。
所有具体的缓存实现都应该继承这个基类。
"""
from abc import ABC, abstractmethod
from typing import Any, Optional, Union, List, Dict, Set
from datetime import timedelta
import logging


logger = logging.getLogger(__name__)

class CacheBase(ABC):
    """缓存抽象基类"""
    
    def __init__(self, namespace: str = ""):
        """
        初始化缓存实例。
        
        Args:
            namespace: 缓存命名空间，用于隔离不同模块的缓存
        """
        self.namespace = namespace
        self._stats = {
            "hits": 0,
            "misses": 0,
            "keys": set()
        }
    
    def _make_key(self, key: str) -> str:
        """
        生成带命名空间的缓存键。
        
        Args:
            key: 原始缓存键
            
        Returns:
            str: 带命名空间的缓存键
        """
        return f"{self.namespace}:{key}" if self.namespace else key
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存值。
        
        Args:
            key: 缓存键
            
        Returns:
            Optional[Any]: 缓存值，不存在时返回None
        """
        pass
    
    @abstractmethod
    async def set(
        self,
        key: str,
        value: Any,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """
        设置缓存值。
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒或timedelta对象）
            
        Returns:
            bool: 是否设置成功
        """
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """
        删除缓存值。
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否删除成功
        """
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """
        检查缓存键是否存在。
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否存在
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """
        清空当前命名空间下的所有缓存。
        
        Returns:
            bool: 是否清空成功
        """
        pass
    
    async def multi_get(self, keys: List[str]) -> Dict[str, Any]:
        """
        批量获取缓存值。
        
        Args:
            keys: 缓存键列表
            
        Returns:
            Dict[str, Any]: 键值对字典
        """
        result = {}
        for key in keys:
            value = await self.get(key)
            if value is not None:
                result[key] = value
        return result
    
    async def multi_set(
        self,
        mapping: Dict[str, Any],
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """
        批量设置缓存值。
        
        Args:
            mapping: 键值对字典
            expire: 过期时间（秒或timedelta对象）
            
        Returns:
            bool: 是否全部设置成功
        """
        success = True
        for key, value in mapping.items():
            if not await self.set(key, value, expire):
                success = False
        return success
    
    async def increment(self, key: str, delta: int = 1) -> Optional[int]:
        """
        增加计数器值。
        
        Args:
            key: 缓存键
            delta: 增加值
            
        Returns:
            Optional[int]: 增加后的值，失败返回None
        """
        value = await self.get(key)
        if value is None:
            value = 0
        try:
            new_value = int(value) + delta
            if await self.set(key, new_value):
                return new_value
        except (ValueError, TypeError):
            logger.error(f"Failed to increment key {key}: value is not a number")
        return None
    
    def get_stats(self) -> Dict[str, Union[int, Set[str]]]:
        """
        获取缓存统计信息。
        
        Returns:
            Dict: 包含命中次数、未命中次数和当前缓存键集合
        """
        return self._stats.copy()
    
    def _track_operation(self, hit: bool, key: str):
        """
        记录缓存操作统计。
        
        Args:
            hit: 是否命中
            key: 操作的键
        """
        if hit:
            self._stats["hits"] += 1
        else:
            self._stats["misses"] += 1
        self._stats["keys"].add(key) 