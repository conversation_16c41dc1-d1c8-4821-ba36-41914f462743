"""
缓存工厂模块。
提供创建不同类型缓存实例的工厂。
"""
from typing import TypeVar, Generic, Dict, Any, Optional, Type

from .interface import CacheInterface, K, V
from .config import CacheConfig
from .memory import MemoryCache

class CacheFactory:
    """缓存工厂，用于创建不同类型的缓存实例"""
    
    @staticmethod
    def create_cache(config: CacheConfig) -> CacheInterface:
        """创建指定类型的缓存实例
        
        Args:
            config: 缓存配置
            
        Returns:
            缓存实例
            
        Raises:
            ValueError: 不支持的缓存类型
            NotImplementedError: 缓存类型尚未实现
        """
        if config.cache_type.lower() == "memory":
            return MemoryCache(config)
        elif config.cache_type.lower() == "redis":
            # TODO: 实现Redis缓存
            raise NotImplementedError("Redis缓存尚未实现")
        else:
            raise ValueError(f"不支持的缓存类型: {config.cache_type}") 