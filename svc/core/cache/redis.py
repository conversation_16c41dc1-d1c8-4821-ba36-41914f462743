"""
Redis缓存工具模块。
提供Redis连接池和客户端管理。
"""

import logging
from typing import Optional, Any, Dict, List, Union
from contextlib import asynccontextmanager

from redis.asyncio import Redis


from svc.core.config.settings import get_settings
from svc.core.cache.base import CacheBase

logger = logging.getLogger(__name__)

# 获取应用配置
settings = get_settings()

# Redis连接池
_redis_pool: Optional[Redis] = None
_redis_pubsub_pool: Optional[Redis] = None

async def get_redis() -> Redis:
    """获取Redis客户端实例。
    
    Returns:
        Redis: Redis客户端实例
    """
    global _redis_pool
    
    if _redis_pool is None:
        logger.info("初始化Redis连接池")
        try:
            _redis_pool = Redis(
                host=settings.redis_host,
                port=settings.redis_port,
                db=settings.redis_db,
                password=settings.redis_password,
                encoding='utf-8',
            )
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            raise
    
    return _redis_pool

async def get_pubsub_redis() -> Redis:
    """获取用于发布/订阅的Redis客户端实例。
    
    Returns:
        Redis: Redis客户端实例
    """
    global _redis_pubsub_pool
    
    if _redis_pubsub_pool is None:
        logger.info("初始化Redis发布/订阅连接池")
        try:
            _redis_pubsub_pool = Redis(
                host=settings.redis_host,
                port=settings.redis_port,
                db=settings.redis_db,
                password=settings.redis_password,
                encoding='utf-8'
            )
        except Exception as e:
            logger.error(f"Redis发布/订阅连接失败: {str(e)}")
            raise
    
    return _redis_pubsub_pool

@asynccontextmanager
async def redis_connection():
    """Redis连接上下文管理器。
    
    用法:
    ```python
    async with redis_connection() as redis:
        await redis.set("key", "value")
    ```
    """
    redis = await get_redis()
    try:
        yield redis
    finally:
        pass  # 连接由连接池管理，不需要手动关闭

async def close_redis():
    """关闭所有Redis连接。
    
    在应用关闭时调用此函数清理资源。
    """
    global _redis_pool, _redis_pubsub_pool
    
    if _redis_pool is not None:
        logger.info("关闭Redis连接池")
        await _redis_pool.close()
        _redis_pool = None
    
    if _redis_pubsub_pool is not None:
        logger.info("关闭Redis发布/订阅连接池")
        await _redis_pubsub_pool.close()
        _redis_pubsub_pool = None

# Redis缓存实现类
class RedisCache(CacheBase):
    """Redis缓存实现类
    
    提供基于Redis的缓存功能实现，包括设置、获取、删除、清理等操作。
    """
    
    def __init__(self, namespace: str = ""):
        """初始化Redis缓存
        
        Args:
            namespace: 缓存命名空间
        """
        super().__init__(namespace)
    
    async def get(self, key: str) -> Any:
        """获取缓存值
        
        Args:
            key: 缓存键
            
        Returns:
            缓存值，不存在时返回None
        """
        full_key = self._get_namespaced_key(key)
        redis = await get_redis()
        value = await redis.get(full_key)
        if value:
            return await self._deserialize(value)
        return None
    
    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            expire: 过期时间（秒）
            
        Returns:
            操作是否成功
        """
        full_key = self._get_namespaced_key(key)
        redis = await get_redis()
        serialized_value = await self._serialize(value)
        if expire:
            return await redis.setex(full_key, expire, serialized_value)
        return await redis.set(full_key, serialized_value)
    
    async def delete(self, key: str) -> bool:
        """删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            操作是否成功
        """
        full_key = self._get_namespaced_key(key)
        redis = await get_redis()
        return await redis.delete(full_key) > 0
    
    async def clear(self, pattern: str = "*") -> int:
        """清理缓存
        
        Args:
            pattern: 缓存键模式
            
        Returns:
            清理的键数量
        """
        redis = await get_redis()
        pattern = self._get_namespaced_key(pattern)
        keys = await redis.keys(pattern)
        if keys:
            return await redis.delete(*keys)
        return 0
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            是否存在
        """
        full_key = self._get_namespaced_key(key)
        redis = await get_redis()
        return await redis.exists(full_key)
    
    async def get_ttl(self, key: str) -> Optional[int]:
        """获取缓存过期时间
        
        Args:
            key: 缓存键
            
        Returns:
            剩余过期时间（秒），永不过期返回-1，不存在返回None
        """
        full_key = self._get_namespaced_key(key)
        redis = await get_redis()
        ttl = await redis.ttl(full_key)
        return ttl if ttl >= -1 else None

# 获取RedisCache实例
async def get_redis_cache(namespace: str = "") -> RedisCache:
    """获取Redis缓存实例
    
    Args:
        namespace: 缓存命名空间
        
    Returns:
        RedisCache实例
    """
    return RedisCache(namespace)