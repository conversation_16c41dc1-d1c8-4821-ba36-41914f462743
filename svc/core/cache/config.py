"""
缓存配置模块。
提供缓存配置对象，用于配置缓存参数。
"""
from dataclasses import dataclass
from typing import Optional, Union, Type, TypeVar, Dict, Any
from datetime import timedelta

T = TypeVar('T')

@dataclass
class CacheConfig:
    """缓存配置对象"""
    
    namespace: str = ""  # 缓存命名空间
    ttl: Optional[int] = None  # 默认过期时间（秒）
    cache_type: str = "memory"  # 缓存类型（memory或redis）
    redis_url: Optional[str] = None  # Redis连接URL
    max_items: int = 10000  # 最大缓存条目数（内存缓存）
    max_memory_percent: float = 75.0  # 最大内存使用百分比（内存缓存）
    cleanup_interval: int = 60  # 清理间隔（秒，内存缓存）
    
    @classmethod
    def from_settings(cls, settings: Any, prefix: str = "") -> "CacheConfig":
        """从设置对象创建配置
        
        Args:
            settings: 设置对象
            prefix: 设置前缀
            
        Returns:
            配置对象
        """
        config = cls()
        
        # 尝试从设置中加载配置
        if hasattr(settings, f"{prefix}cache_namespace"):
            config.namespace = getattr(settings, f"{prefix}cache_namespace")
            
        if hasattr(settings, f"{prefix}cache_ttl"):
            config.ttl = getattr(settings, f"{prefix}cache_ttl")
            
        if hasattr(settings, f"{prefix}cache_type"):
            config.cache_type = getattr(settings, f"{prefix}cache_type")
            
        if hasattr(settings, f"{prefix}redis_url"):
            config.redis_url = getattr(settings, f"{prefix}redis_url")
            
        if hasattr(settings, f"{prefix}cache_max_items"):
            config.max_items = getattr(settings, f"{prefix}cache_max_items")
            
        if hasattr(settings, f"{prefix}cache_max_memory_percent"):
            config.max_memory_percent = getattr(settings, f"{prefix}cache_max_memory_percent")
            
        if hasattr(settings, f"{prefix}cache_cleanup_interval"):
            config.cleanup_interval = getattr(settings, f"{prefix}cache_cleanup_interval")
            
        return config 