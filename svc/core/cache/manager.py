"""
缓存管理器模块。
提供全局缓存访问点和缓存实例生命周期管理。
"""
from typing import Dict, Any, TypeVar, Type, Optional
import logging

from .interface import CacheInterface
from .config import CacheConfig
from .factory import CacheFactory

logger = logging.getLogger(__name__)

class CacheManager:
    """缓存管理器，管理所有缓存实例"""
    
    _instance = None
    _caches: Dict[str, CacheInterface] = {}
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(CacheManager, cls).__new__(cls)
        return cls._instance
    
    @classmethod
    def get_cache(cls, name: str, config: Optional[CacheConfig] = None) -> CacheInterface:
        """获取指定名称的缓存实例，如果不存在则创建
        
        Args:
            name: 缓存名称
            config: 缓存配置，如果为None则使用默认配置
            
        Returns:
            缓存实例
        """
        if name not in cls._caches:
            if config is None:
                config = CacheConfig()
            
            logger.info(f"创建缓存实例: {name}, 类型: {config.cache_type}")
            cache = CacheFactory.create_cache(config)
            cls._caches[name] = cache
        
        return cls._caches[name]
    
    @classmethod
    async def clear_all(cls) -> None:
        """清空所有缓存"""
        logger.info("清空所有缓存")
        for name, cache in cls._caches.items():
            await cache.clear()
            logger.debug(f"已清空缓存: {name}")
    
    @classmethod
    async def start_cleanup_tasks(cls) -> None:
        """启动所有内存缓存的清理任务"""
        logger.info("启动所有缓存清理任务")
        for name, cache in cls._caches.items():
            if hasattr(cache, "start_cleanup_task"):
                await cache.start_cleanup_task()
                logger.debug(f"已启动缓存清理任务: {name}")
    
    @classmethod
    async def stop_cleanup_tasks(cls) -> None:
        """停止所有内存缓存的清理任务"""
        logger.info("停止所有缓存清理任务")
        for name, cache in cls._caches.items():
            if hasattr(cache, "stop_cleanup_task"):
                await cache.stop_cleanup_task()
                logger.debug(f"已停止缓存清理任务: {name}")
    
    @classmethod
    def get_stats(cls) -> Dict[str, Any]:
        """获取所有缓存的统计信息
        
        Returns:
            包含所有缓存统计信息的字典
        """
        stats = {}
        for name, cache in cls._caches.items():
            if hasattr(cache, "get_stats"):
                stats[name] = cache.get_stats()
        
        return stats 