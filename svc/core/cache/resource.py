"""
资源缓存抽象层模块。
提供统一的资源级缓存支持，包括资源对象缓存、租户隔离和事件驱动的缓存失效。
"""
import inspect
import json
import logging
from datetime import timedelta
from functools import wraps
from typing import Any, Callable, Dict, Generic, List, Optional, Type, TypeVar, Union, cast
import asyncio

from fastapi.encoders import jsonable_encoder
from pydantic import BaseModel

from .base import CacheBase
from .memory import MemoryCache
from .redis import RedisCache, get_redis_cache

logger = logging.getLogger(__name__)

# 定义类型变量
T = TypeVar("T")
ModelType = TypeVar("ModelType", bound=BaseModel)


class ResourceCache(Generic[ModelType]):
    """资源缓存类，提供对资源对象的缓存操作"""
    
    def __init__(
        self,
        resource_type: str,
        model_class: Type[ModelType],
        cache_type: str = "redis",
        namespace: str = "",
        expire: Optional[Union[int, timedelta]] = 3600,
        tenant_aware: bool = True,
        auto_invalidate: bool = True
    ):
        """初始化资源缓存
        
        Args:
            resource_type: 资源类型名称，用于构建缓存键前缀
            model_class: 资源模型类，用于序列化/反序列化
            cache_type: 缓存类型，支持"redis"和"memory"
            namespace: 缓存命名空间
            expire: 默认过期时间（秒或timedelta对象）
            tenant_aware: 是否支持租户隔离
            auto_invalidate: 是否自动注册事件监听以失效缓存
        """
        self.resource_type = resource_type
        self.model_class = model_class
        self.cache_type = cache_type
        self.namespace = namespace
        self.expire = expire
        self.tenant_aware = tenant_aware
        self.auto_invalidate = auto_invalidate
        self._cache: Optional[CacheBase] = None
        
        if auto_invalidate:
            # TODO: 在此处注册事件监听
            pass
    
    async def _get_cache(self) -> CacheBase:
        """获取缓存实例
        
        Returns:
            CacheBase: 缓存实例
        """
        if self._cache is None:
            if self.cache_type == "redis":
                self._cache = await get_redis_cache(self.namespace)
            else:
                self._cache = MemoryCache(self.namespace)
        return self._cache
    
    def _build_resource_key(
        self,
        resource_id: Union[str, int],
        tenant_id: Optional[str] = None,
        property_name: Optional[str] = None
    ) -> str:
        """构建资源缓存键
        
        Args:
            resource_id: 资源ID
            tenant_id: 租户ID，用于租户隔离
            property_name: 属性名，用于缓存特定属性
            
        Returns:
            str: 缓存键
        """
        key_parts = [self.resource_type, str(resource_id)]
        
        # 添加租户ID
        if self.tenant_aware and tenant_id:
            key_parts.insert(0, f"tenant:{tenant_id}")
        
        # 添加属性名
        if property_name:
            key_parts.append(property_name)
        
        return ":".join(key_parts)
    
    def _build_collection_key(
        self,
        collection_name: str,
        tenant_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> str:
        """构建集合缓存键
        
        Args:
            collection_name: 集合名称
            tenant_id: 租户ID，用于租户隔离
            filters: 过滤条件
            
        Returns:
            str: 缓存键
        """
        key_parts = [self.resource_type, "collection", collection_name]
        
        # 添加租户ID
        if self.tenant_aware and tenant_id:
            key_parts.insert(0, f"tenant:{tenant_id}")
        
        # 添加过滤条件
        if filters:
            filter_parts = []
            for key, value in sorted(filters.items()):
                filter_parts.append(f"{key}={value}")
            if filter_parts:
                key_parts.append("filter")
                key_parts.append(",".join(filter_parts))
        
        return ":".join(key_parts)
    
    async def get(
        self,
        resource_id: Union[str, int],
        tenant_id: Optional[str] = None
    ) -> Optional[ModelType]:
        """获取缓存的资源对象
        
        Args:
            resource_id: 资源ID
            tenant_id: 租户ID，用于租户隔离
            
        Returns:
            Optional[ModelType]: 资源对象，如果不存在则返回None
        """
        cache = await self._get_cache()
        key = self._build_resource_key(resource_id, tenant_id)
        
        data = await cache.get(key)
        if data:
            if isinstance(data, str):
                return self.model_class.model_validate_json(data)
            return self.model_class.model_validate(data)
        return None
    
    async def set(
        self,
        resource_id: Union[str, int],
        resource: ModelType,
        tenant_id: Optional[str] = None,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """缓存资源对象
        
        Args:
            resource_id: 资源ID
            resource: 资源对象
            tenant_id: 租户ID，用于租户隔离
            expire: 过期时间（秒或timedelta对象），默认使用构造函数设置的值
            
        Returns:
            bool: 是否成功
        """
        cache = await self._get_cache()
        key = self._build_resource_key(resource_id, tenant_id)
        
        # 使用默认过期时间
        if expire is None:
            expire = self.expire
        
        # 序列化资源对象
        if hasattr(resource, "model_dump_json"):
            data = resource.model_dump_json()
        else:
            data = jsonable_encoder(resource)
        
        return await cache.set(key, data, expire)
    
    async def delete(
        self,
        resource_id: Union[str, int],
        tenant_id: Optional[str] = None
    ) -> bool:
        """删除缓存的资源对象
        
        Args:
            resource_id: 资源ID
            tenant_id: 租户ID，用于租户隔离
            
        Returns:
            bool: 是否成功
        """
        cache = await self._get_cache()
        key = self._build_resource_key(resource_id, tenant_id)
        return await cache.delete(key)
    
    async def get_property(
        self,
        resource_id: Union[str, int],
        property_name: str,
        tenant_id: Optional[str] = None
    ) -> Optional[Any]:
        """获取缓存的资源属性
        
        Args:
            resource_id: 资源ID
            property_name: 属性名
            tenant_id: 租户ID，用于租户隔离
            
        Returns:
            Optional[Any]: 属性值，如果不存在则返回None
        """
        cache = await self._get_cache()
        key = self._build_resource_key(resource_id, tenant_id, property_name)
        return await cache.get(key)
    
    async def set_property(
        self,
        resource_id: Union[str, int],
        property_name: str,
        value: Any,
        tenant_id: Optional[str] = None,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """缓存资源属性
        
        Args:
            resource_id: 资源ID
            property_name: 属性名
            value: 属性值
            tenant_id: 租户ID，用于租户隔离
            expire: 过期时间（秒或timedelta对象），默认使用构造函数设置的值
            
        Returns:
            bool: 是否成功
        """
        cache = await self._get_cache()
        key = self._build_resource_key(resource_id, tenant_id, property_name)
        
        # 使用默认过期时间
        if expire is None:
            expire = self.expire
        
        return await cache.set(key, value, expire)
    
    async def get_collection(
        self,
        collection_name: str,
        tenant_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> Optional[List[ModelType]]:
        """获取缓存的资源集合
        
        Args:
            collection_name: 集合名称
            tenant_id: 租户ID，用于租户隔离
            filters: 过滤条件
            
        Returns:
            Optional[List[ModelType]]: 资源对象列表，如果不存在则返回None
        """
        cache = await self._get_cache()
        key = self._build_collection_key(collection_name, tenant_id, filters)
        
        data = await cache.get(key)
        if data:
            if isinstance(data, str):
                items = json.loads(data)
            else:
                items = data
            return [self.model_class.model_validate(item) for item in items]
        return None
    
    async def set_collection(
        self,
        collection_name: str,
        items: List[ModelType],
        tenant_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """缓存资源集合
        
        Args:
            collection_name: 集合名称
            items: 资源对象列表
            tenant_id: 租户ID，用于租户隔离
            filters: 过滤条件
            expire: 过期时间（秒或timedelta对象），默认使用构造函数设置的值
            
        Returns:
            bool: 是否成功
        """
        cache = await self._get_cache()
        key = self._build_collection_key(collection_name, tenant_id, filters)
        
        # 使用默认过期时间
        if expire is None:
            expire = self.expire
        
        # 序列化资源对象列表
        serialized_items = []
        for item in items:
            if hasattr(item, "model_dump"):
                serialized_items.append(item.model_dump())
            else:
                serialized_items.append(jsonable_encoder(item))
        
        return await cache.set(key, json.dumps(serialized_items), expire)
    
    async def delete_collection(
        self,
        collection_name: str,
        tenant_id: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None
    ) -> bool:
        """删除缓存的资源集合
        
        Args:
            collection_name: 集合名称
            tenant_id: 租户ID，用于租户隔离
            filters: 过滤条件
            
        Returns:
            bool: 是否成功
        """
        cache = await self._get_cache()
        key = self._build_collection_key(collection_name, tenant_id, filters)
        return await cache.delete(key)
    
    async def clear_all(self, tenant_id: Optional[str] = None) -> bool:
        """清除所有与此资源类型相关的缓存
        
        Args:
            tenant_id: 租户ID，仅清除指定租户的缓存
            
        Returns:
            bool: 是否成功
        """
        cache = await self._get_cache()
        
        # 构建模式
        if tenant_id and self.tenant_aware:
            pattern = f"tenant:{tenant_id}:{self.resource_type}:*"
        else:
            pattern = f"{self.resource_type}:*"
        
        # 使用scan查找匹配的键
        if isinstance(cache, RedisCache):
            # 对于Redis，使用scan命令批量删除
            redis = cache.redis
            cursor = 0
            deleted = 0
            
            while True:
                cursor, keys = await redis.scan(cursor=cursor, match=pattern, count=100)
                if keys:
                    deleted += await redis.delete(*keys)
                
                if cursor == 0:
                    break
            
            return deleted > 0
        else:
            # 对于内存缓存，使用clear方法
            keys_to_delete = [
                key for key in cache._cache.keys()
                if key.startswith(pattern)
            ]
            
            for key in keys_to_delete:
                await cache.delete(key)
            
            return True
    
    async def clear_on_event(self, event_name: str, event_data: Dict[str, Any]) -> bool:
        """根据事件清除相关缓存
        
        此方法将根据事件数据中的资源ID和租户ID清除相关缓存。
        例如，当资源更新事件发生时，可以调用此方法清除该资源的缓存。
        
        Args:
            event_name: 事件名称
            event_data: 事件数据
            
        Returns:
            bool: 是否成功
        """
        # 提取资源ID
        resource_id = event_data.get("id") or event_data.get(f"{self.resource_type}_id")
        if not resource_id:
            logger.warning(f"无法从事件数据中提取资源ID: {event_data}")
            return False
        
        # 提取租户ID
        tenant_id = event_data.get("tenant_id")
        
        # 清除资源缓存
        success = await self.delete(resource_id, tenant_id)
        
        # 清除相关集合缓存
        if self.tenant_aware and tenant_id:
            collection_pattern = f"tenant:{tenant_id}:{self.resource_type}:collection:*"
        else:
            collection_pattern = f"{self.resource_type}:collection:*"
        
        cache = await self._get_cache()
        
        # 使用scan查找匹配的键
        if isinstance(cache, RedisCache):
            redis = cache.redis
            cursor = 0
            
            while True:
                cursor, keys = await redis.scan(cursor=cursor, match=collection_pattern, count=100)
                if keys:
                    await redis.delete(*keys)
                
                if cursor == 0:
                    break
        
        return success


# 装饰器函数
def resource_cached(
    resource_type: str,
    model_class: Type[ModelType],
    id_param: str = "id",
    tenant_param: Optional[str] = "tenant_id",
    cache_type: str = "redis",
    namespace: str = "",
    expire: Optional[Union[int, timedelta]] = 3600
) -> Callable:
    """资源缓存装饰器，用于自动缓存资源对象
    
    Args:
        resource_type: 资源类型名称
        model_class: 资源模型类
        id_param: 资源ID参数名
        tenant_param: 租户ID参数名，为None则不支持租户隔离
        cache_type: 缓存类型
        namespace: 缓存命名空间
        expire: 过期时间
        
    Returns:
        装饰后的函数
        
    Examples:
        @resource_cached("user", UserModel)
        async def get_user(db: AsyncSession, id: int, tenant_id: Optional[str] = None) -> UserModel:
            # 数据库查询逻辑
            return user
    """
    def decorator(func: Callable[..., ModelType]) -> Callable[..., ModelType]:
        # 创建资源缓存实例
        resource_cache = ResourceCache(
            resource_type=resource_type,
            model_class=model_class,
            cache_type=cache_type,
            namespace=namespace,
            expire=expire,
            tenant_aware=tenant_param is not None
        )
        
        # 获取函数签名
        sig = inspect.signature(func)
        
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> ModelType:
            # 提取资源ID
            resource_id = kwargs.get(id_param)
            if not resource_id:
                # 尝试从位置参数中获取
                bound_args = sig.bind_partial(*args, **kwargs)
                bound_args.apply_defaults()
                if id_param in bound_args.arguments:
                    resource_id = bound_args.arguments[id_param]
            
            if not resource_id:
                # 没有资源ID，直接调用原函数
                return await func(*args, **kwargs)
            
            # 提取租户ID
            tenant_id = None
            if tenant_param:
                tenant_id = kwargs.get(tenant_param)
                if not tenant_id and tenant_param in sig.parameters:
                    # 尝试从位置参数中获取
                    bound_args = sig.bind_partial(*args, **kwargs)
                    bound_args.apply_defaults()
                    if tenant_param in bound_args.arguments:
                        tenant_id = bound_args.arguments[tenant_param]
            
            # 尝试从缓存获取
            cached_resource = await resource_cache.get(resource_id, tenant_id)
            if cached_resource:
                return cached_resource
            
            # 执行原函数
            resource = await func(*args, **kwargs)
            
            # 缓存结果
            if resource:
                await resource_cache.set(resource_id, resource, tenant_id)
            
            return resource
        
        return cast(Callable[..., ModelType], wrapper)
    
    return decorator


def invalidate_resource_cache(
    resource_type: str,
    id_param: str = "id",
    tenant_param: Optional[str] = "tenant_id",
    cache_type: str = "redis",
    namespace: str = ""
) -> Callable:
    """资源缓存失效装饰器，用于在函数执行后使资源缓存失效
    
    Args:
        resource_type: 资源类型名称
        id_param: 资源ID参数名
        tenant_param: 租户ID参数名，为None则不支持租户隔离
        cache_type: 缓存类型
        namespace: 缓存命名空间
        
    Returns:
        装饰后的函数
        
    Examples:
        @invalidate_resource_cache("user")
        async def update_user(db: AsyncSession, id: int, data: Dict, tenant_id: Optional[str] = None) -> UserModel:
            # 更新用户数据
            return updated_user
    """
    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        # 获取函数签名
        sig = inspect.signature(func)
        
        @wraps(func)
        async def wrapper(*args: Any, **kwargs: Any) -> T:
            # 提取资源ID
            resource_id = kwargs.get(id_param)
            if not resource_id:
                # 尝试从位置参数中获取
                bound_args = sig.bind_partial(*args, **kwargs)
                bound_args.apply_defaults()
                if id_param in bound_args.arguments:
                    resource_id = bound_args.arguments[id_param]
            
            # 提取租户ID
            tenant_id = None
            if tenant_param:
                tenant_id = kwargs.get(tenant_param)
                if not tenant_id and tenant_param in sig.parameters:
                    # 尝试从位置参数中获取
                    bound_args = sig.bind_partial(*args, **kwargs)
                    bound_args.apply_defaults()
                    if tenant_param in bound_args.arguments:
                        tenant_id = bound_args.arguments[tenant_param]
            
            # 执行原函数
            result = await func(*args, **kwargs)
            
            # 创建资源缓存实例
            resource_cache = ResourceCache(
                resource_type=resource_type,
                model_class=type(result) if result else Any,
                cache_type=cache_type,
                namespace=namespace,
                tenant_aware=tenant_param is not None
            )
            
            # 删除资源缓存
            if resource_id:
                await resource_cache.delete(resource_id, tenant_id)
                
                # 清除相关集合缓存
                if tenant_param:
                    collection_pattern = f"{resource_type}:collection:*"
                    cache = await resource_cache._get_cache()
                    
                    # 对于Redis缓存，使用scan命令查找匹配的键
                    if isinstance(cache, RedisCache):
                        redis = cache.redis
                        cursor = 0
                        
                        while True:
                            cursor, keys = await redis.scan(
                                cursor=cursor,
                                match=collection_pattern,
                                count=100
                            )
                            if keys:
                                await redis.delete(*keys)
                            
                            if cursor == 0:
                                break
            
            return result
        
        return cast(Callable[..., T], wrapper)
    
    return decorator 