"""
令牌缓存实现。
提供对认证令牌的缓存服务，减轻每次请求的验证开销。
"""
from typing import Dict, Any
from svc.core.config.settings import get_settings
from svc.core.cache.config import CacheConfig
from svc.core.cache.manager import CacheManager

# 获取设置
settings = get_settings()

# 创建缓存配置
token_cache_config = CacheConfig(
    namespace="token",
    ttl=int(settings.access_token_expire_minutes * 60 * 0.9),  # 令牌有效期的90%
    cache_type="memory"
)

# 获取缓存实例
token_cache = CacheManager.get_cache("token", token_cache_config)

# 导出实例
__all__ = ["token_cache"] 