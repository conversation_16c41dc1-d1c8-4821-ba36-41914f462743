"""
用户缓存实现。
提供用户信息的缓存服务，减少数据库查询。
"""
from typing import Any
from svc.core.config.settings import get_settings
from svc.core.cache.config import CacheConfig
from svc.core.cache.manager import CacheManager

# 获取设置
settings = get_settings()

# 创建缓存配置
user_cache_config = CacheConfig(
    namespace="user",
    ttl=getattr(settings, "user_cache_ttl", 300),  # 默认5分钟
    cache_type="memory"
)

# 获取缓存实例
user_cache = CacheManager.get_cache("user", user_cache_config)

# 导出实例
__all__ = ["user_cache"] 