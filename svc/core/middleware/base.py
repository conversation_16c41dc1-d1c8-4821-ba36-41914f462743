from typing import Dict, Any
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

class BaseMiddleware(BaseHTTPMiddleware):
    """中间件基类"""
    
    priority: int = 100  # 默认优先级
    
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        **options: Dict[str, Any]
    ):
        super().__init__(app)
        self.enabled = enabled
        self.options = options
    
    @property
    def name(self) -> str:
        """获取中间件名称"""
        return self.__class__.__name__.lower().replace('middleware', '')
    
    def is_enabled(self) -> bool:
        """检查中间件是否启用"""
        return self.enabled 