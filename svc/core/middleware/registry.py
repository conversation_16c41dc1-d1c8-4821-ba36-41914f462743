from typing import List, Type, Dict, Any
import importlib
import pkgutil
import logging
from pathlib import Path

from .base import BaseMiddleware
logger=logging.getLogger(__name__)

class MiddlewareRegistry:
    """中间件注册器"""
    
    def __init__(self):
        self._middlewares: List[Type[BaseMiddleware]] = []
    
    def register(self, middleware_class: Type[BaseMiddleware]) -> None:
        """注册中间件类"""
        if (isinstance(middleware_class, type) and 
            issubclass(middleware_class, BaseMiddleware) and 
            middleware_class != BaseMiddleware):
            self._middlewares.append(middleware_class)
    
    def load_middlewares(self, package_path: str) -> None:
        """从指定包路径加载中间件"""
        package = importlib.import_module(package_path)
        package_dir = Path(package.__file__).parent
        
        for _, name, _ in pkgutil.iter_modules([package_dir]):
            if name not in ['base', 'registry', '__init__']:
                module = importlib.import_module(f"{package_path}.{name}")
                for attr_name in dir(module):
                    attr = getattr(module, attr_name)
                    if (isinstance(attr, type) and 
                        issubclass(attr, BaseMiddleware) and 
                        attr != BaseMiddleware):
                        self.register(attr)
    
    def get_sorted_middlewares(self) -> List[Type[BaseMiddleware]]:
        """获取按优先级排序的中间件列表"""
        return sorted(
            self._middlewares,
            key=lambda m: getattr(m, 'priority', 100)
        )

def setup_middlewares(app, config: Dict[str, Any] = None):
    """设置中间件
    
    Args:
        app: FastAPI应用实例
        config: 中间件配置字典，格式如:
            {
                "logging": {
                    "enabled": true,
                    "options": {
                        "exclude_paths": ["/health"]
                    }
                }
            }
    """
    config = config or {}
    
    # 创建注册器
    registry = MiddlewareRegistry()
    
    # 加载中间件
    registry.load_middlewares("svc.core.middleware")
    
    # 获取排序后的中间件列表
    sorted_middlewares = registry.get_sorted_middlewares()
    
    # 注册中间件
    for middleware_class in sorted_middlewares:
        middleware_name = middleware_class.__name__.lower().replace('middleware', '')
        middleware_config = config.get(middleware_name, {})
        
        # 检查是否启用
        if middleware_config.get('enabled', True):
            # 添加中间件到应用
            app.add_middleware(
                middleware_class,
                enabled=middleware_config.get('enabled', True),
                **middleware_config.get('options', {})
            )
            logger.info(f'{middleware_name}中间件加载成功')
            