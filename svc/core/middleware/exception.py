import traceback
import logging
from typing import Dict, Any, Optional
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.types import ASGIApp
from starlette.status import (
    HTTP_500_INTERNAL_SERVER_ERROR,
    HTTP_404_NOT_FOUND,
    HTTP_400_BAD_REQUEST,
    HTTP_401_UNAUTHORIZED,
    HTTP_403_FORBIDDEN
)
from .base import BaseMiddleware

# 配置日志
logger = logging.getLogger("api")

class ExceptionHandlerMiddleware(BaseMiddleware):
    """异常处理中间件，统一处理异常"""
    
    priority = 80  # 最后执行

    __name__ = "exception"
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        debug: bool = False,
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        self.debug = debug
    
    async def dispatch(self, request: Request, call_next) -> Response:
        if not self.is_enabled():
            return await call_next(request)
            
        try:
            return await call_next(request)
            
        except Exception as e:
            # 记录异常信息
            logger.error(
                f"Exception in request: {request.method} {request.url.path}\n"
                f"Error: {str(e)}\n"
                f"Traceback: {traceback.format_exc()}"
            )
            
            # 获取请求ID（如果存在）
            request_id = getattr(request.state, "request_id", None)
            
            # 构建错误响应
            return self._build_error_response(e, request_id)
    
    def _build_error_response(
        self,
        exc: Exception,
        request_id: Optional[str] = None
    ) -> JSONResponse:
        """构建错误响应"""
        
        # 默认错误信息
        error_response = {
            "success": False,
            "error": {
                "type": exc.__class__.__name__,
                "message": str(exc)
            }
        }
        
        # 添加请求ID
        if request_id:
            error_response["request_id"] = request_id
            
        # 在调试模式下添加堆栈跟踪
        if self.debug:
            error_response["error"]["traceback"] = traceback.format_exc()
        
        # 根据异常类型设置状态码和错误信息
        status_code = self._get_status_code(exc)
        
        return JSONResponse(
            status_code=status_code,
            content=error_response
        )
    
    def _get_status_code(self, exc: Exception) -> int:
        """根据异常类型获取HTTP状态码"""
        
        # 常见异常映射
        exception_map = {
            "NotFoundError": HTTP_404_NOT_FOUND,
            "ValidationError": HTTP_400_BAD_REQUEST,
            "AuthenticationError": HTTP_401_UNAUTHORIZED,
            "PermissionError": HTTP_403_FORBIDDEN
        }
        
        # 获取异常类名
        exc_name = exc.__class__.__name__
        
        # 返回映射的状态码，默认500
        return exception_map.get(exc_name, HTTP_500_INTERNAL_SERVER_ERROR) 