"""
中间件共享配置

定义中间件之间共享的配置项，避免重复定义和不一致。
"""

from typing import List, Set

# 通用排除路径 - 所有中间件都应该排除的系统路径
COMMON_EXCLUDE_PATHS: List[str] = [
    "/health",
    "/metrics", 
    "/docs",
    "/openapi.json",
    "/redoc",
    "/favicon.ico"
]

# 请求日志中间件排除路径
REQUEST_LOGGING_EXCLUDE_PATHS: List[str] = COMMON_EXCLUDE_PATHS.copy()

# 审计日志中间件排除路径 (包含额外的审计相关路径)
AUDIT_EXCLUDE_PATHS: Set[str] = set(COMMON_EXCLUDE_PATHS + [
    "/system/audit",  # 避免审计日志查询产生循环记录
])

# 审计日志排除的HTTP方法 (只记录修改操作)
AUDIT_EXCLUDE_METHODS: Set[str] = {"GET", "OPTIONS", "HEAD"}

# 请求状态字段名称常量
class RequestStateFields:
    """请求状态字段名称常量"""
    REQUEST_ID = "request_id"
    START_TIME = "start_time"
    PROCESS_TIME = "process_time"
    USER_ID = "user_id"
    TOKEN_PAYLOAD = "token_payload"

# 默认请求ID头名称
DEFAULT_REQUEST_ID_HEADER = "X-Request-ID"
