import time
from typing import Dict, Tuple
from fastapi import Request, Response
from starlette.types import <PERSON><PERSON><PERSON><PERSON>
from starlette.status import HTTP_429_TOO_MANY_REQUESTS
from .base import BaseMiddleware

class RateLimitMiddleware(BaseMiddleware):
    """速率限制中间件，实现请求限流"""
    
    priority = 40

    __name__ = "rate_limit"
    
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        rate_limit: int = 100,  # 请求次数
        time_window: int = 60,  # 时间窗口（秒）
        exclude_paths: list = None,
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        self.rate_limit = rate_limit
        self.time_window = time_window
        self.exclude_paths = exclude_paths or ["/health", "/metrics"]
        self.requests: Dict[str, list] = {}  # 存储请求记录
    
    async def dispatch(self, request: Request, call_next) -> Response:
        if not self.is_enabled():
            return await call_next(request)
            
        # 检查是否需要跳过限流
        if self._should_skip(request):
            return await call_next(request)
            
        # 获取客户端标识
        client_id = self._get_client_id(request)
        
        # 检查是否超过限制
        if self._is_rate_limited(client_id):
            return Response(
                content="Too many requests",
                status_code=HTTP_429_TOO_MANY_REQUESTS,
                headers={"Retry-After": str(self._get_retry_after(client_id))}
            )
            
        # 记录请求
        self._record_request(client_id)
        
        return await call_next(request)
    
    def _should_skip(self, request: Request) -> bool:
        """检查是否需要跳过限流"""
        return any(
            request.url.path.startswith(path)
            for path in self.exclude_paths
        )
    
    def _get_client_id(self, request: Request) -> str:
        """获取客户端标识
        
        优先级：
        1. X-Real-IP 头
        2. X-Forwarded-For 头的第一个IP
        3. 客户端IP
        """
        client_id = (
            request.headers.get("X-Real-IP") or
            request.headers.get("X-Forwarded-For", "").split(",")[0].strip() or
            request.client.host if request.client else "unknown"
        )
        return f"{client_id}:{request.url.path}"
    
    def _is_rate_limited(self, client_id: str) -> bool:
        """检查是否超过速率限制"""
        now = time.time()
        
        # 清理过期的请求记录
        self._cleanup_old_requests(client_id, now)
        
        # 获取当前时间窗口内的请求数
        requests = self.requests.get(client_id, [])
        return len(requests) >= self.rate_limit
    
    def _record_request(self, client_id: str):
        """记录请求"""
        now = time.time()
        
        if client_id not in self.requests:
            self.requests[client_id] = []
            
        self.requests[client_id].append(now)
        
        # 清理过期的请求记录
        self._cleanup_old_requests(client_id, now)
    
    def _cleanup_old_requests(self, client_id: str, now: float):
        """清理过期的请求记录"""
        if client_id in self.requests:
            self.requests[client_id] = [
                ts for ts in self.requests[client_id]
                if now - ts <= self.time_window
            ]
            
            # 如果没有有效请求，删除客户端记录
            if not self.requests[client_id]:
                del self.requests[client_id]
    
    def _get_retry_after(self, client_id: str) -> int:
        """获取需要等待的时间（秒）"""
        if client_id not in self.requests or not self.requests[client_id]:
            return 0
            
        oldest_request = min(self.requests[client_id])
        return max(0, int(oldest_request + self.time_window - time.time())) 