import logging
import time
import uuid
from typing import Awaitable, Callable

from fastapi import Request, Response
from starlette.types import <PERSON><PERSON><PERSON><PERSON>

from .base import BaseMiddleware
from .shared_config import (DEFAULT_REQUEST_ID_HEADER,
                            REQUEST_LOGGING_EXCLUDE_PATHS, RequestStateFields)

# 配置日志
logger = logging.getLogger("api")

class RequestLoggingMiddleware(BaseMiddleware):
    """请求日志记录和请求ID生成中间件，记录请求和响应信息并为每个请求生成唯一ID"""

    priority = 10  # 最高优先级，最外层中间件

    __name__ = "request_logging"

    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        exclude_paths: list = None,
        header_name: str = DEFAULT_REQUEST_ID_HEADER,
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        self.exclude_paths = exclude_paths or REQUEST_LOGGING_EXCLUDE_PATHS
        self.header_name = header_name
    
    async def dispatch(
        self, request: Request, call_next: Callable[[Request], Awaitable[Response]]
    ) -> Response:
        """处理请求，生成请求ID并记录日志"""

        # 如果中间件被禁用，直接处理请求
        if not self.is_enabled():
            return await call_next(request)

        # 生成或获取请求ID（合并自RequestIdMiddleware的逻辑）
        request_id = request.headers.get(self.header_name)
        if not request_id:
            request_id = str(uuid.uuid4())
        setattr(request.state, RequestStateFields.REQUEST_ID, request_id)

        # 跳过不需要记录日志的路径（但仍然处理请求ID）
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            response = await call_next(request)
            # 即使跳过日志记录，也要添加请求ID到响应头
            response.headers[self.header_name] = request_id
            return response

        # 记录请求开始时间并共享给其他中间件
        start_time = time.time()
        setattr(request.state, RequestStateFields.START_TIME, start_time)

        # 记录请求信息
        logger.info(
            f"Request started: {request.method} {request.url.path} "
            f"[ID: {request_id}] [Client: {request.client.host if request.client else 'unknown'}]"
        )

        # 处理请求
        try:
            response = await call_next(request)

            # 计算处理时间并共享给其他中间件
            process_time = time.time() - start_time
            setattr(request.state, RequestStateFields.PROCESS_TIME, process_time)

            # 记录响应信息
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"[ID: {request_id}] [Status: {response.status_code}] "
                f"[Time: {process_time:.4f}s]"
            )

            # 添加请求ID到响应头（使用配置的header_name）
            response.headers[self.header_name] = request_id

            return response

        except Exception as e:
            # 记录异常信息并共享处理时间
            process_time = time.time() - start_time
            setattr(request.state, RequestStateFields.PROCESS_TIME, process_time)
            logger.error(
                f"Request failed: {request.method} {request.url.path} "
                f"[ID: {request_id}] [Time: {process_time:.4f}s] "
                f"[Error: {str(e)}]"
            )
            raise

# 配置日志格式
def setup_logging():
    """配置应用日志"""
    
    # 创建处理器
    console_handler = logging.StreamHandler()
    
    # 设置格式
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(formatter)
    
    # 配置根日志
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    
    # 配置API日志
    api_logger = logging.getLogger("api")
    api_logger.setLevel(logging.INFO)
    
    # 配置SQLAlchemy日志
    sqlalchemy_logger = logging.getLogger("sqlalchemy.engine")
    sqlalchemy_logger.setLevel(logging.WARNING) 