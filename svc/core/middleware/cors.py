from typing import List, Optional
from fastapi import Request, Response
from starlette.types import <PERSON><PERSON><PERSON><PERSON>, Receive, Scope, Send
from .base import BaseMiddleware

class CORSMiddleware(BaseMiddleware):
    """CORS中间件，处理跨域请求"""
    
    priority = 50

    __name__ = "cors"
    
    def __init__(
        self,
        app: ASGIApp,
        enabled: bool = True,
        allow_origins: List[str] = None,
        allow_methods: List[str] = None,
        allow_headers: List[str] = None,
        allow_credentials: bool = True,
        expose_headers: List[str] = None,
        max_age: int = 600,
        **options
    ):
        super().__init__(app, enabled=enabled, **options)
        
        # 默认值设置
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["*"]
        self.allow_headers = allow_headers or ["*"]
        self.allow_credentials = allow_credentials
        self.expose_headers = expose_headers or []
        self.max_age = max_age
    
    async def dispatch(self, request: Request, call_next) -> Response:
        if not self.is_enabled():
            return await call_next(request)

        response = await call_next(request)
        
        origin = request.headers.get("origin")
        
        if origin and self._is_valid_origin(origin):
            # 添加CORS响应头
            response.headers["Access-Control-Allow-Origin"] = origin
            if self.allow_credentials:
                response.headers["Access-Control-Allow-Credentials"] = "true"
            
            if request.method == "OPTIONS":
                # 处理预检请求
                response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
                response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
                response.headers["Access-Control-Max-Age"] = str(self.max_age)
            
            if self.expose_headers:
                response.headers["Access-Control-Expose-Headers"] = ", ".join(self.expose_headers)
        
        return response
    
    def _is_valid_origin(self, origin: str) -> bool:
        """检查origin是否在允许列表中"""
        if "*" in self.allow_origins:
            return True
            
        return origin in self.allow_origins 