"""
环境配置加载模块。
提供环境配置加载和导出功能。
"""
import os
import logging
from typing import Dict, Any, List

from .settings import get_settings, get_environment_type, EnvironmentType

logger = logging.getLogger(__name__)

def get_config_info() -> Dict[str, Any]:
    """获取当前配置信息（不包含敏感数据）
    
    Returns:
        Dict[str, Any]: 当前配置信息
    """
    try:
        # 获取配置实例
        settings = get_settings()
        
        # 构建配置信息（排除敏感信息）
        config_info = {
            "环境": {
                "当前环境": settings.env,
                "调试模式": settings.debug
            },
            "应用": {
                "名称": settings.project_name,
                "版本": settings.version,
                "API前缀": settings.api_prefix
            },
            "数据库": {
                "主机": settings.db_server,
                "数据库": settings.db_name,
                "用户": settings.db_user,
                "连接池": {
                    "大小": settings.db_pool_size,
                    "最大溢出": settings.db_max_overflow
                }
            },
            "Redis": {
                "主机": settings.redis_host,
                "端口": settings.redis_port,
                "数据库": settings.redis_db
            },
            "安全配置": {
                "令牌过期时间": settings.access_token_expire_minutes,
                "算法": settings.algorithm
            },
            "CORS": {
                "允许的源": settings.cors_origins
            },
            "多租户": {
                "启用": settings.multi_tenant_enabled,
                "默认租户": settings.default_tenant_id
            }
        }
        
        # 打印配置信息
        logger.info("当前配置信息:")
        _log_config_info(config_info)
        
        return config_info
    except Exception as e:
        logger.error(f"获取配置信息失败: {str(e)}")
        raise

def _log_config_info(config: Dict[str, Any], indent: int = 0) -> None:
    """递归打印配置信息"""
    for key, value in config.items():
        if isinstance(value, dict):
            logger.info(f"{'  ' * indent}{key}:")
            _log_config_info(value, indent + 1)
        elif isinstance(value, list) and len(value) > 5:
            logger.info(f"{'  ' * indent}{key}: [{len(value)}个项目]")
        else:
            logger.info(f"{'  ' * indent}{key}: {value}")

def get_current_env() -> str:
    """获取当前环境。
    
    Returns:
        str: 当前环境名称
    """
    return get_settings().env

def get_env_vars(prefix: str = None) -> Dict[str, str]:
    """获取当前环境变量
    
    Args:
        prefix: 环境变量前缀过滤
    
    Returns:
        Dict[str, str]: 环境变量字典
    """
    if prefix:
        return {k: v for k, v in os.environ.items() if k.startswith(prefix)}
    return dict(os.environ)

def is_development() -> bool:
    """是否为开发环境
    
    Returns:
        bool: 是否为开发环境
    """
    return get_current_env() == EnvironmentType.DEVELOPMENT.value

def is_test() -> bool:
    """是否为测试环境
    
    Returns:
        bool: 是否为测试环境
    """
    return get_current_env() == EnvironmentType.TEST.value

def is_production() -> bool:
    """是否为生产环境
    
    Returns:
        bool: 是否为生产环境
    """
    return get_current_env() == EnvironmentType.PRODUCTION.value 