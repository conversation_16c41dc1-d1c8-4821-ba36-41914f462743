"""
邮件服务模块，提供异步邮件发送功能。
"""

import os
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional

import aiosmtplib

logger = logging.getLogger(__name__)


class EmailService:
    """
    邮件服务类，提供异步电子邮件发送功能。
    
    功能：
    1. 发送欢迎邮件 - 新用户注册后的欢迎信息
    2. 发送密码重置邮件 - 包含密码重置链接
    3. 发送密码变更通知 - 密码修改后的安全通知
    """
    
    def __init__(
        self,
        smtp_host: Optional[str] = None,
        smtp_port: Optional[int] = None,
        smtp_user: Optional[str] = None,
        smtp_password: Optional[str] = None,
        from_email: Optional[str] = None,
        use_tls: Optional[bool] = None
    ):
        """
        初始化邮件服务
        
        Args:
            smtp_host: SMTP服务器地址
            smtp_port: SMTP服务器端口
            smtp_user: SMTP用户名
            smtp_password: SMTP密码
            from_email: 发件人邮箱
            use_tls: 是否使用TLS加密
        """
        # 从环境变量或参数读取配置
        self.smtp_host = smtp_host or os.getenv("SMTP_HOST", "localhost")
        self.smtp_port = smtp_port or int(os.getenv("SMTP_PORT", "587"))
        self.smtp_user = smtp_user or os.getenv("SMTP_USER", "")
        self.smtp_password = smtp_password or os.getenv("SMTP_PASSWORD", "")
        self.from_email = from_email or os.getenv("FROM_EMAIL", "<EMAIL>")
        self.use_tls = use_tls if use_tls is not None else os.getenv("SMTP_USE_TLS", "true").lower() == "true"
        
        # 网站前端URL，用于构建密码重置链接
        self.frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
        
        # 配置日志记录器
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def send_welcome_email(self, to: str, nickname: str) -> bool:
        """
        发送欢迎邮件
        
        Args:
            to: 收件人邮箱
            nickname: 用户昵称
            
        Returns:
            bool: 发送是否成功
        """
        subject = "欢迎加入我们的平台"
        
        html_content = f"""
        <html>
            <body>
                <h1>欢迎，{nickname}！</h1>
                <p>感谢您注册我们的服务。我们很高兴您能成为我们社区的一员。</p>
                <p>如果您有任何问题或需要帮助，请随时联系我们的支持团队。</p>
                <p>祝您使用愉快！</p>
                <p>此致，<br>团队</p>
            </body>
        </html>
        """
        
        text_content = f"""
        欢迎，{nickname}！
        
        感谢您注册我们的服务。我们很高兴您能成为我们社区的一员。
        
        如果您有任何问题或需要帮助，请随时联系我们的支持团队。
        
        祝您使用愉快！
        
        此致，
        团队
        """
        
        return await self._send_email(to, subject, html_content, text_content)
    
    async def send_password_reset_email(self, to: str, reset_token: str, expires_in_minutes: int) -> bool:
        """
        发送密码重置邮件
        
        Args:
            to: 收件人邮箱
            reset_token: 重置令牌
            expires_in_minutes: 过期时间（分钟）
            
        Returns:
            bool: 发送是否成功
        """
        subject = "密码重置请求"
        
        # 构建重置链接
        reset_url = f"{self.frontend_url}/reset-password?token={reset_token}"
        
        html_content = f"""
        <html>
            <body>
                <h1>密码重置请求</h1>
                <p>我们收到了您的密码重置请求。请点击下方链接重置您的密码：</p>
                <p><a href="{reset_url}">重置密码</a></p>
                <p>或者复制以下链接到浏览器地址栏：</p>
                <p>{reset_url}</p>
                <p>此链接将在 {expires_in_minutes} 分钟后过期。</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <p>此致，<br>团队</p>
            </body>
        </html>
        """
        
        text_content = f"""
        密码重置请求
        
        我们收到了您的密码重置请求。请通过以下链接重置您的密码：
        
        {reset_url}
        
        此链接将在 {expires_in_minutes} 分钟后过期。
        
        如果您没有请求重置密码，请忽略此邮件。
        
        此致，
        团队
        """
        
        return await self._send_email(to, subject, html_content, text_content)
    
    async def send_password_changed_notification(self, to: str, reset_time: str) -> bool:
        """
        发送密码已更改通知邮件
        
        Args:
            to: 收件人邮箱
            reset_time: 重置时间
            
        Returns:
            bool: 发送是否成功
        """
        subject = "密码已更改通知"
        
        html_content = f"""
        <html>
            <body>
                <h1>密码已更改通知</h1>
                <p>您的账户密码已于 {reset_time} 成功更改。</p>
                <p>如果这不是您本人操作，请立即联系我们的支持团队或重新设置您的密码。</p>
                <p>此致，<br>团队</p>
            </body>
        </html>
        """
        
        text_content = f"""
        密码已更改通知
        
        您的账户密码已于 {reset_time} 成功更改。
        
        如果这不是您本人操作，请立即联系我们的支持团队或重新设置您的密码。
        
        此致，
        团队
        """
        
        return await self._send_email(to, subject, html_content, text_content)
    
    async def check_connection(self) -> bool:
        """
        检查与SMTP服务器的连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            async with aiosmtplib.SMTP(
                hostname=self.smtp_host,
                port=self.smtp_port,
                use_tls=self.use_tls
            ) as smtp:
                if self.smtp_user and self.smtp_password:
                    await smtp.login(self.smtp_user, self.smtp_password)
                
                self.logger.info(f"邮件服务连接检查成功: {self.smtp_host}:{self.smtp_port}")
                return True
        except Exception as e:
            self.logger.error(f"邮件服务连接检查失败: {str(e)}")
            return False
    
    async def _send_email(self, to: str, subject: str, html_content: str, text_content: Optional[str] = None) -> bool:
        """
        发送邮件的内部方法
        
        Args:
            to: 收件人邮箱
            subject: 邮件主题
            html_content: HTML格式内容
            text_content: 纯文本格式内容，默认为None
            
        Returns:
            bool: 发送是否成功
        """
        message = MIMEMultipart("alternative")
        message["Subject"] = subject
        message["From"] = self.from_email
        message["To"] = to
        
        # 添加纯文本内容
        if text_content:
            message.attach(MIMEText(text_content, "plain", "utf-8"))
        
        # 添加HTML内容
        message.attach(MIMEText(html_content, "html", "utf-8"))
        
        try:
            # 使用异步SMTP客户端
            async with aiosmtplib.SMTP(
                hostname=self.smtp_host,
                port=self.smtp_port,
                use_tls=self.use_tls
            ) as smtp:
                # 如果提供了凭据，则进行登录
                if self.smtp_user and self.smtp_password:
                    await smtp.login(self.smtp_user, self.smtp_password)
                
                # 发送邮件
                await smtp.send_message(message)
            
            self.logger.info(f"邮件发送成功: 主题='{subject}', 收件人={to}")
            return True
        except Exception as e:
            self.logger.error(f"邮件发送失败: {str(e)}")
            return False 