"""
输入验证工具模块。
提供验证错误消息格式化和翻译功能。
"""
from typing import Dict, Any, List, Optional, Union, Tuple
from pydantic import ValidationError

# 字段类型错误消息映射
TYPE_ERROR_MESSAGES = {
    "string": "请输入有效的字符串",
    "integer": "请输入有效的整数",
    "float": "请输入有效的数字",
    "boolean": "请选择是或否",
    "array": "请输入有效的列表",
    "object": "请输入有效的对象",
    "email": "请输入有效的电子邮箱地址",
    "uri": "请输入有效的URL",
    "uuid": "请输入有效的UUID",
    "date": "请输入有效的日期 (YYYY-MM-DD)",
    "datetime": "请输入有效的日期时间 (YYYY-MM-DD HH:MM:SS)",
    "time": "请输入有效的时间 (HH:MM:SS)",
    "ipv4": "请输入有效的IPv4地址",
    "ipv6": "请输入有效的IPv6地址",
    "phone": "请输入有效的电话号码",
}

# 验证错误类型到用户友好消息的映射
ERROR_TYPE_MESSAGES = {
    "missing": "此字段不能为空",
    "null-not-allowed": "此字段不能为空",
    "string-type": "请输入文本",
    "integer-type": "请输入整数",
    "float-type": "请输入数字",
    "boolean-type": "请选择是或否",
    "uuid-type": "请输入有效的UUID",
    "url-type": "请输入有效的网址",
    "email-type": "请输入有效的电子邮箱地址",
    "list-type": "请输入列表",
    "dict-type": "请输入有效的对象",
    "greater-than": "请输入更大的值",
    "greater-than-equal": "请输入更大或相等的值",
    "less-than": "请输入更小的值",
    "less-than-equal": "请输入更小或相等的值",
    "minimum-length": "内容过短",
    "maximum-length": "内容过长",
    "enum": "请选择有效的选项",
    "const": "值不正确",
    "invalid": "值无效",
    "pattern": "格式不正确",
    "json-invalid": "请输入有效的JSON",
    "json-object": "请输入有效的JSON对象",
    "json-array": "请输入有效的JSON数组"
}

def format_validation_errors(error: ValidationError) -> Dict[str, List[str]]:
    """
    将Pydantic验证错误转换为用户友好的格式
    
    Args:
        error: Pydantic ValidationError对象
        
    Returns:
        Dict[str, List[str]]: 字段名到错误消息列表的映射
    """
    formatted_errors = {}
    for err in error.errors():
        # 获取字段名
        if err["loc"]:
            field = ".".join(str(loc) for loc in err["loc"] if not isinstance(loc, int))
            if not field:  # 处理数组项验证错误的情况
                field = "items"
        else:
            field = "非字段错误"
            
        # 获取错误类型和错误消息
        error_type = err.get("type", "")
        original_msg = err.get("msg", "")
        
        # 转换为用户友好的消息
        user_message = _get_user_friendly_message(error_type, original_msg, err.get("ctx", {}))
        
        # 添加到错误字典
        if field not in formatted_errors:
            formatted_errors[field] = []
        formatted_errors[field].append(user_message)
        
    return formatted_errors

def _get_user_friendly_message(error_type: str, original_msg: str, context: Dict[str, Any]) -> str:
    """
    将技术错误消息转换为用户友好的消息
    
    Args:
        error_type: 错误类型
        original_msg: 原始错误消息
        context: 错误上下文数据
        
    Returns:
        str: 用户友好的错误消息
    """
    # 处理类型错误
    if error_type in ["type_error", "type_error.integer"]:
        expected_type = context.get("expected_type", "")
        if expected_type in TYPE_ERROR_MESSAGES:
            return TYPE_ERROR_MESSAGES[expected_type]
    
    # 处理已知错误类型
    if error_type in ERROR_TYPE_MESSAGES:
        message = ERROR_TYPE_MESSAGES[error_type]
        
        # 处理特殊错误类型的上下文信息
        if error_type == "minimum-length":
            min_length = context.get("min_length")
            if min_length:
                message = f"请至少输入 {min_length} 个字符"
        elif error_type == "maximum-length":
            max_length = context.get("max_length")
            if max_length:
                message = f"请不要超过 {max_length} 个字符"
        elif error_type == "greater-than":
            gt = context.get("gt")
            if gt is not None:
                message = f"请输入大于 {gt} 的值"
        elif error_type == "less-than":
            lt = context.get("lt")
            if lt is not None:
                message = f"请输入小于 {lt} 的值"
        elif error_type == "enum":
            expected = context.get("expected")
            if expected:
                expected_str = ", ".join(str(e) for e in expected)
                message = f"请选择有效的选项: {expected_str}"
        
        return message
    
    # 默认返回原始消息（但去除技术细节）
    # 去除消息中的类型信息和技术细节
    msg = original_msg
    msg = msg.replace("value is not a valid integer", "请输入有效的整数")
    msg = msg.replace("value is not a valid float", "请输入有效的数字")
    msg = msg.replace("value is not a valid boolean", "请选择是或否")
    msg = msg.replace("value is not a valid email address", "请输入有效的电子邮箱地址")
    msg = msg.replace("value is not a valid URL", "请输入有效的网址")
    msg = msg.replace("field required", "此字段必填")
    msg = msg.replace("value is not a valid list", "请输入有效的列表")
    
    return msg

def format_error_response(errors: Dict[str, List[str]]) -> Dict[str, Any]:
    """
    格式化错误响应
    
    Args:
        errors: 字段名到错误消息列表的映射
        
    Returns:
        Dict[str, Any]: 格式化的错误响应
    """
    formatted_errors = {
        "errors": errors,
        "error_count": sum(len(messages) for messages in errors.values())
    }
    
    # 提取第一个错误作为主要错误消息
    if errors:
        first_field = next(iter(errors))
        if errors[first_field]:
            formatted_errors["message"] = errors[first_field][0]
    
    return formatted_errors

class ValidationErrorFormatter:
    """
    验证错误格式化器
    提供可配置的验证错误格式化功能
    """
    
    def __init__(
        self,
        custom_field_names: Optional[Dict[str, str]] = None,
        custom_error_messages: Optional[Dict[str, str]] = None
    ):
        """
        初始化验证错误格式化器
        
        Args:
            custom_field_names: 自定义字段名称映射
            custom_error_messages: 自定义错误消息映射
        """
        self.custom_field_names = custom_field_names or {}
        self.custom_error_messages = custom_error_messages or {}
    
    def format_errors(self, error: ValidationError) -> Dict[str, List[str]]:
        """
        格式化验证错误
        
        Args:
            error: Pydantic ValidationError对象
            
        Returns:
            Dict[str, List[str]]: 字段名到错误消息列表的映射
        """
        formatted_errors = {}
        for err in error.errors():
            # 处理字段名称
            if err["loc"]:
                field_path = list(str(loc) for loc in err["loc"] if not isinstance(loc, int))
                if not field_path:
                    field = "items"
                else:
                    field = ".".join(field_path)
                    
                # 应用自定义字段名称
                display_field = self.custom_field_names.get(field, field)
            else:
                field = "非字段错误"
                display_field = field
            
            # 处理错误消息
            error_type = err.get("type", "")
            error_key = f"{field}.{error_type}"
            
            # 尝试获取自定义错误消息
            if error_key in self.custom_error_messages:
                user_message = self.custom_error_messages[error_key]
            elif field in self.custom_error_messages:
                user_message = self.custom_error_messages[field]
            else:
                # 使用通用格式化
                user_message = _get_user_friendly_message(
                    error_type, 
                    err.get("msg", ""), 
                    err.get("ctx", {})
                )
                
                # 如果有自定义字段名称，在错误消息中替换字段名
                if display_field != field and not display_field in user_message:
                    user_message = f"{display_field}: {user_message}"
            
            # 添加到错误字典
            if field not in formatted_errors:
                formatted_errors[field] = []
            formatted_errors[field].append(user_message)
        
        return formatted_errors 