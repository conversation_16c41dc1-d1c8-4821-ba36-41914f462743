"""
字符串处理工具函数
提供各种字符串操作和转换功能
"""
import re
from typing import Any, Callable


def to_camel_case(snake_str: str) -> str:
    """
    将snake_case字符串转换为camelCase
    
    Args:
        snake_str: 下划线分隔的字符串
        
    Returns:
        转换后的驼峰式字符串
    
    Examples:
        >>> to_camel_case("user_name")
        "userName"
        >>> to_camel_case("first_name_last_name")
        "firstNameLastName"
    """
    if not snake_str or not isinstance(snake_str, str):
        return snake_str
    
    # 处理已经是camelCase的情况
    if not re.search('_', snake_str):
        return snake_str
    
    # 使用正则表达式将snake_case转换为camelCase
    components = snake_str.split('_')
    return components[0] + ''.join(x.title() for x in components[1:]) 