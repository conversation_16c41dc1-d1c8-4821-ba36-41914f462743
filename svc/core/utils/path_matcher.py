import re
from typing import List, Union, Pattern

# 工具函数：编译排除路径为正则或字符串前缀
# 支持三种写法：
# 1. 普通前缀字符串（如 /api/v1/specs/product/）
# 2. 参数化路径（如 /api/v1/specs/product/{product_id}/specs）
# 3. 正则表达式（如 ^/api/v1/specs/product/\d+/specs$）
def compile_exclude_patterns(exclude_paths: List[str], only_param_and_regex: bool = False) -> List[Pattern]:
    """
    编译排除路径为正则对象：
    - 参数化路径如 /api/v1/specs/product/{product_id}/specs 自动转 ^/api/v1/specs/product/[^/]+/specs$
    - 正则表达式如 ^/api/v1/specs/product/\d+/specs$
    - 其他静态路径自动转 ^/xxx$ 正则
    """
    patterns = []
    for path in exclude_paths:
        if '{' in path and '}' in path:
            # 参数化路径转正则
            regex = re.sub(r'{[^/]+}', r'[^/]+', path)
            regex = '^' + regex + '$'
            patterns.append(re.compile(regex))
        elif path.startswith('^'):
            # 直接正则
            patterns.append(re.compile(path))
        else:
            # 静态路径自动转 ^/xxx$ 正则
            regex = '^' + re.escape(path) + '$'
            patterns.append(re.compile(regex))
    return patterns

# 工具函数：判断请求路径是否匹配任一排除规则
# patterns为compile_exclude_patterns的返回值
# path为实际请求路径
# 返回True表示应跳过认证

def match_path(path: str, patterns: List[Pattern]) -> bool:
    """
    判断请求路径是否匹配任一排除规则（全部正则）
    """
    for pattern in patterns:
        if pattern.match(path):
            return True
    return False 