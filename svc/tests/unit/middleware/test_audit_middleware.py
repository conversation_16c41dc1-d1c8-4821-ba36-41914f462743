"""
审计中间件测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.testclient import TestClient

from svc.core.middleware.audit import AuditMiddleware


@pytest.fixture
def app():
    """创建测试应用"""
    app = FastAPI()
    
    @app.get("/test")
    async def test_endpoint():
        return {"message": "test"}
    
    @app.post("/api/users")
    async def create_user():
        return {"id": 1, "name": "test"}
    
    @app.get("/health")
    async def health():
        return {"status": "ok"}
    
    return app


@pytest.fixture
def client_with_audit(app):
    """创建带审计中间件的测试客户端"""
    app.add_middleware(AuditMiddleware, enabled=True)
    return TestClient(app)


@pytest.fixture
def mock_request():
    """创建模拟请求"""
    request = MagicMock(spec=Request)
    request.method = "POST"
    request.url.path = "/api/users"
    request.query_params = {}
    request.headers = {"user-agent": "test-agent", "content-type": "application/json"}
    request.client.host = "127.0.0.1"
    request.state.user_id = 1
    request.state.token_payload = {"username": "testuser"}
    return request


class TestAuditMiddleware:
    """审计中间件测试类"""
    
    def test_middleware_initialization(self):
        """测试中间件初始化"""
        app = FastAPI()
        middleware = AuditMiddleware(
            app=app,
            enabled=True,
            exclude_paths={"/custom"},
            exclude_methods={"PATCH"},
            include_request_body=True,
            max_body_size=2048
        )
        
        assert middleware.is_enabled() is True
        assert "/custom" in middleware.exclude_paths
        assert "PATCH" in middleware.exclude_methods
        assert middleware.include_request_body is True
        assert middleware.max_body_size == 2048
    
    def test_should_skip_audit_excluded_paths(self):
        """测试排除路径的跳过逻辑"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 创建模拟请求
        request = MagicMock(spec=Request)
        request.url.path = "/health"
        request.method = "GET"
        
        assert middleware._should_skip_audit(request) is True
    
    def test_should_skip_audit_excluded_methods(self):
        """测试排除方法的跳过逻辑"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 创建模拟请求
        request = MagicMock(spec=Request)
        request.url.path = "/api/users"
        request.method = "GET"
        
        assert middleware._should_skip_audit(request) is True
    
    def test_should_not_skip_audit_normal_request(self):
        """测试正常请求不应该被跳过"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 创建模拟请求
        request = MagicMock(spec=Request)
        request.url.path = "/api/users"
        request.method = "POST"
        
        assert middleware._should_skip_audit(request) is False
    
    def test_parse_action_and_resource(self):
        """测试解析操作类型和资源类型"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 测试不同的请求
        test_cases = [
            ("POST", "/api/users", ("create", "users")),
            ("PUT", "/api/users/1", ("update", "users")),
            ("DELETE", "/api/users/1", ("delete", "users")),
            ("GET", "/api/users", ("read", "users")),
            ("POST", "/v1/products", ("create", "products")),
            ("PATCH", "/orders", ("update", "orders")),
        ]
        
        for method, path, expected in test_cases:
            request = MagicMock(spec=Request)
            request.method = method
            request.url.path = path
            
            action, resource_type = middleware._parse_action_and_resource(request)
            assert (action, resource_type) == expected
    
    @patch('svc.core.middleware.audit.AuditMiddleware._async_record_audit_log')
    async def test_dispatch_with_authenticated_user(self, mock_record, mock_request):
        """测试已认证用户的请求处理"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 模拟call_next函数
        async def mock_call_next(request):
            response = MagicMock(spec=Response)
            response.status_code = 200
            response.headers = {}
            return response
        
        # 执行中间件
        response = await middleware.dispatch(mock_request, mock_call_next)
        
        # 验证审计日志记录被调用
        assert mock_record.called
        assert response.status_code == 200
    
    @patch('svc.core.middleware.audit.AuditMiddleware._async_record_audit_log')
    async def test_dispatch_with_unauthenticated_user(self, mock_record):
        """测试未认证用户的请求处理"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 创建未认证的请求
        request = MagicMock(spec=Request)
        request.method = "POST"
        request.url.path = "/api/users"
        request.state.user_id = None  # 未认证
        
        # 模拟call_next函数
        async def mock_call_next(request):
            response = MagicMock(spec=Response)
            response.status_code = 200
            return response
        
        # 执行中间件
        response = await middleware.dispatch(request, mock_call_next)
        
        # 验证审计日志记录未被调用
        assert not mock_record.called
        assert response.status_code == 200
    
    @patch('svc.core.middleware.audit.AuditMiddleware._async_record_audit_log')
    async def test_dispatch_with_exception(self, mock_record, mock_request):
        """测试请求处理异常的情况"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 模拟抛出异常的call_next函数
        async def mock_call_next(request):
            raise ValueError("Test error")
        
        # 执行中间件并期望异常
        with pytest.raises(ValueError, match="Test error"):
            await middleware.dispatch(mock_request, mock_call_next)
        
        # 验证审计日志记录被调用（记录失败）
        assert mock_record.called
        
        # 检查调用参数
        call_args = mock_record.call_args
        assert call_args[1]['status'] == 'failure'
        assert 'error' in call_args[1]['details']
    
    async def test_read_request_body_json(self):
        """测试读取JSON请求体"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 模拟请求
        request = MagicMock(spec=Request)
        request.body = AsyncMock(return_value=b'{"name": "test"}')
        
        body = await middleware._read_request_body(request)
        assert body == {"name": "test"}
    
    async def test_read_request_body_too_large(self):
        """测试读取过大的请求体"""
        app = FastAPI()
        middleware = AuditMiddleware(app, max_body_size=10)
        
        # 模拟大请求体
        request = MagicMock(spec=Request)
        large_body = b'x' * 100
        request.body = AsyncMock(return_value=large_body)
        
        body = await middleware._read_request_body(request)
        assert body.startswith("<body too large:")
    
    @patch('svc.core.middleware.audit.get_session_for_script')
    @patch('svc.core.middleware.audit.AuditLogService')
    async def test_async_record_audit_log(self, mock_service_class, mock_get_session):
        """测试异步记录审计日志"""
        app = FastAPI()
        middleware = AuditMiddleware(app)
        
        # 模拟数据库会话
        mock_db = AsyncMock()
        mock_get_session.return_value = mock_db
        
        # 模拟审计日志服务
        mock_service = AsyncMock()
        mock_service_class.return_value = mock_service
        
        # 调用方法
        await middleware._async_record_audit_log(
            action="create",
            resource_type="user",
            user_id=1,
            username="testuser",
            request=MagicMock(),
            details={"test": "data"},
            status="success",
            message="Test message"
        )
        
        # 验证调用
        mock_service.create_audit_log.assert_called_once()
        mock_db.commit.assert_called_once()
        mock_db.close.assert_called_once()
