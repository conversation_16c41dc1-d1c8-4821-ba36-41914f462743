import uuid
from datetime import datetime, timedelta
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.audit_log_service import AuditLogService
from svc.apps.system.schemas.audit_log import (
    AuditLogCreate,
    AuditLogSearchParams
)

@pytest.mark.asyncio
async def test_create_log(db: AsyncSession):
    """测试创建审计日志"""
    # 准备测试数据
    user_id = uuid.uuid4()
    log_data = AuditLogCreate(
        action="create",
        resource_type="user",
        user_id=user_id,
        tenant_id="test_tenant",
        resource_id="123",
        description="Created new user",
        request_body={"name": "test"},
        response_status="200",
        metadata={"extra": "info"}
    )
    
    # 创建审计日志
    log_response = await AuditLogService.create_log(
        db,
        log_in=log_data,
        ip_address="127.0.0.1",
        user_agent="test-agent",
        request_path="/api/users",
        request_method="POST"
    )
    
    # 验证结果
    assert log_response.id is not None
    assert log_response.user_id == user_id
    assert log_response.action == "create"
    assert log_response.resource_type == "user"
    assert log_response.tenant_id == "test_tenant"
    assert log_response.ip_address == "127.0.0.1"
    assert log_response.user_agent == "test-agent"
    assert log_response.request_path == "/api/users"
    assert log_response.request_method == "POST"

@pytest.mark.asyncio
async def test_get_log(db: AsyncSession):
    """测试获取单个审计日志"""
    # 创建测试数据
    log_data = AuditLogCreate(
        action="read",
        resource_type="user",
        description="Test get log"
    )
    created_log = await AuditLogService.create_log(db, log_in=log_data)
    
    # 获取审计日志
    log_response = await AuditLogService.get_log(db, log_id=created_log.id)
    
    # 验证结果
    assert log_response is not None
    assert log_response.id == created_log.id
    assert log_response.action == "read"
    assert log_response.description == "Test get log"

@pytest.mark.asyncio
async def test_get_logs_by_user(db: AsyncSession):
    """测试获取用户的审计日志"""
    # 准备测试数据
    user_id = uuid.uuid4()
    log_data1 = AuditLogCreate(
        action="create",
        resource_type="user",
        user_id=user_id
    )
    log_data2 = AuditLogCreate(
        action="update",
        resource_type="user",
        user_id=user_id
    )
    
    await AuditLogService.create_log(db, log_in=log_data1)
    await AuditLogService.create_log(db, log_in=log_data2)
    
    # 获取用户的审计日志
    logs_response = await AuditLogService.get_logs_by_user(db, user_id=user_id)
    
    # 验证结果
    assert logs_response.total == 2
    assert len(logs_response.items) == 2
    assert all(log.user_id == user_id for log in logs_response.items)

@pytest.mark.asyncio
async def test_get_logs_by_tenant(db: AsyncSession):
    """测试获取租户的审计日志"""
    # 准备测试数据
    tenant_id = "test_tenant"
    log_data1 = AuditLogCreate(
        action="create",
        resource_type="subscription",
        tenant_id=tenant_id
    )
    log_data2 = AuditLogCreate(
        action="update",
        resource_type="subscription",
        tenant_id=tenant_id
    )
    
    await AuditLogService.create_log(db, log_in=log_data1)
    await AuditLogService.create_log(db, log_in=log_data2)
    
    # 获取租户的审计日志
    logs_response = await AuditLogService.get_logs_by_tenant(db, tenant_id=tenant_id)
    
    # 验证结果
    assert logs_response.total == 2
    assert len(logs_response.items) == 2
    assert all(log.tenant_id == tenant_id for log in logs_response.items)

@pytest.mark.asyncio
async def test_get_logs_by_resource(db: AsyncSession):
    """测试获取资源的审计日志"""
    # 准备测试数据
    resource_type = "user"
    resource_id = "123"
    log_data1 = AuditLogCreate(
        action="create",
        resource_type=resource_type,
        resource_id=resource_id
    )
    log_data2 = AuditLogCreate(
        action="update",
        resource_type=resource_type,
        resource_id=resource_id
    )
    
    await AuditLogService.create_log(db, log_in=log_data1)
    await AuditLogService.create_log(db, log_in=log_data2)
    
    # 获取资源的审计日志
    logs_response = await AuditLogService.get_logs_by_resource(
        db,
        resource_type=resource_type,
        resource_id=resource_id
    )
    
    # 验证结果
    assert logs_response.total == 2
    assert len(logs_response.items) == 2
    assert all(log.resource_type == resource_type for log in logs_response.items)
    assert all(log.resource_id == resource_id for log in logs_response.items)

@pytest.mark.asyncio
async def test_search_logs(db: AsyncSession):
    """测试搜索审计日志"""
    # 准备测试数据
    user_id = uuid.uuid4()
    tenant_id = "test_tenant"
    now = datetime.utcnow()
    
    log_data1 = AuditLogCreate(
        action="create",
        resource_type="user",
        user_id=user_id,
        tenant_id=tenant_id
    )
    log_data2 = AuditLogCreate(
        action="update",
        resource_type="user",
        user_id=user_id,
        tenant_id=tenant_id
    )
    
    await AuditLogService.create_log(db, log_in=log_data1)
    await AuditLogService.create_log(db, log_in=log_data2)
    
    # 测试不同的搜索条件
    # 1. 按用户ID搜索
    search_params = AuditLogSearchParams(user_id=user_id)
    logs_response = await AuditLogService.search_logs(db, search_params=search_params)
    assert logs_response.total == 2
    assert all(log.user_id == user_id for log in logs_response.items)
    
    # 2. 按租户ID搜索
    search_params = AuditLogSearchParams(tenant_id=tenant_id)
    logs_response = await AuditLogService.search_logs(db, search_params=search_params)
    assert logs_response.total == 2
    assert all(log.tenant_id == tenant_id for log in logs_response.items)
    
    # 3. 按时间范围搜索
    search_params = AuditLogSearchParams(
        start_time=now - timedelta(minutes=1),
        end_time=now + timedelta(minutes=1)
    )
    logs_response = await AuditLogService.search_logs(db, search_params=search_params)
    assert logs_response.total == 2
    
    # 4. 组合条件搜索
    search_params = AuditLogSearchParams(
        user_id=user_id,
        tenant_id=tenant_id,
        action="create"
    )
    logs_response = await AuditLogService.search_logs(db, search_params=search_params)
    assert logs_response.total == 1
    assert logs_response.items[0].action == "create"