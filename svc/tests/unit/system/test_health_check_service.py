import uuid
from datetime import datetime
import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.health_check_service import HealthCheckService
from svc.apps.system.schemas.health_check import (
    HealthCheckCreate,
    HealthCheckResponse,
    SystemHealthResponse
)

@pytest.mark.asyncio
async def test_create_check(db: AsyncSession):
    """测试创建健康检查记录"""
    # 准备测试数据
    check_data = HealthCheckCreate(
        component="test_component",
        status="healthy",
        is_healthy=True,
        response_time=100.0,
        error_message=None,
        details={"version": "1.0.0"}
    )
    
    # 创建健康检查记录
    check_response = await HealthCheckService.create_check(db, check_in=check_data)
    
    # 验证结果
    assert check_response.id is not None
    assert check_response.component == "test_component"
    assert check_response.status == "healthy"
    assert check_response.is_healthy is True
    assert check_response.response_time == 100.0
    assert check_response.details == {"version": "1.0.0"}

@pytest.mark.asyncio
async def test_get_check(db: AsyncSession):
    """测试获取单个健康检查记录"""
    # 创建测试数据
    check_data = HealthCheckCreate(
        component="test_component",
        status="healthy",
        is_healthy=True,
        response_time=100.0
    )
    created_check = await HealthCheckService.create_check(db, check_in=check_data)
    
    # 获取健康检查记录
    check_response = await HealthCheckService.get_check(db, check_id=created_check.id)
    
    # 验证结果
    assert check_response is not None
    assert check_response.id == created_check.id
    assert check_response.component == "test_component"
    assert check_response.status == "healthy"

@pytest.mark.asyncio
async def test_get_latest_by_component(db: AsyncSession):
    """测试获取组件的最新健康检查记录"""
    # 准备测试数据
    component = "test_component"
    check_data1 = HealthCheckCreate(
        component=component,
        status="healthy",
        is_healthy=True,
        response_time=100.0
    )
    check_data2 = HealthCheckCreate(
        component=component,
        status="unhealthy",
        is_healthy=False,
        response_time=200.0
    )
    
    await HealthCheckService.create_check(db, check_in=check_data1)
    await HealthCheckService.create_check(db, check_in=check_data2)
    
    # 获取最新记录
    latest_check = await HealthCheckService.get_latest_by_component(db, component=component)
    
    # 验证结果
    assert latest_check is not None
    assert latest_check.component == component
    assert latest_check.status == "unhealthy"
    assert latest_check.response_time == 200.0

@pytest.mark.asyncio
async def test_get_latest_checks(db: AsyncSession):
    """测试获取所有组件的最新健康检查记录"""
    # 准备测试数据
    components = ["component1", "component2"]
    for component in components:
        check_data = HealthCheckCreate(
            component=component,
            status="healthy",
            is_healthy=True,
            response_time=100.0
        )
        await HealthCheckService.create_check(db, check_in=check_data)
    
    # 获取所有最新记录
    latest_checks = await HealthCheckService.get_latest_checks(db)
    
    # 验证结果
    assert len(latest_checks) == 2
    assert all(check.component in components for check in latest_checks)

@pytest.mark.asyncio
async def test_get_component_history(db: AsyncSession):
    """测试获取组件的健康检查历史记录"""
    # 准备测试数据
    component = "test_component"
    for _ in range(3):
        check_data = HealthCheckCreate(
            component=component,
            status="healthy",
            is_healthy=True,
            response_time=100.0
        )
        await HealthCheckService.create_check(db, check_in=check_data)
    
    # 获取历史记录
    history_response = await HealthCheckService.get_component_history(
        db,
        component=component,
        skip=0,
        limit=10
    )
    
    # 验证结果
    assert history_response.total == 3
    assert len(history_response.items) == 3
    assert all(check.component == component for check in history_response.items)

@pytest.mark.asyncio
async def test_get_unhealthy_checks(db: AsyncSession):
    """测试获取不健康的检查记录"""
    # 准备测试数据
    check_data1 = HealthCheckCreate(
        component="component1",
        status="healthy",
        is_healthy=True,
        response_time=100.0
    )
    check_data2 = HealthCheckCreate(
        component="component2",
        status="unhealthy",
        is_healthy=False,
        response_time=200.0
    )
    
    await HealthCheckService.create_check(db, check_in=check_data1)
    await HealthCheckService.create_check(db, check_in=check_data2)
    
    # 获取不健康记录
    unhealthy_response = await HealthCheckService.get_unhealthy_checks(db)
    
    # 验证结果
    assert unhealthy_response.total == 1
    assert len(unhealthy_response.items) == 1
    assert unhealthy_response.items[0].is_healthy is False

@pytest.mark.asyncio
async def test_check_database(db: AsyncSession):
    """测试数据库健康检查"""
    # 执行数据库健康检查
    check_response = await HealthCheckService.check_database(db)
    
    # 验证结果
    assert check_response.component == "database"
    assert check_response.is_healthy is True
    assert check_response.status == "healthy"
    assert check_response.response_time > 0
    assert "db_time" in check_response.details

@pytest.mark.asyncio
async def test_check_redis(db: AsyncSession):
    """测试Redis健康检查"""
    # 创建模拟的Redis客户端
    mock_redis = AsyncMock()
    mock_redis.ping = AsyncMock()
    mock_redis.info = AsyncMock(return_value={
        "redis_version": "6.0.0",
        "used_memory_human": "1M",
        "connected_clients": 1
    })
    
    # 执行Redis健康检查
    check_response = await HealthCheckService.check_redis(db, mock_redis)
    
    # 验证结果
    assert check_response.component == "redis"
    assert check_response.is_healthy is True
    assert check_response.status == "healthy"
    assert check_response.response_time > 0
    assert check_response.details["version"] == "6.0.0"

@pytest.mark.asyncio
async def test_check_api(db: AsyncSession):
    """测试API健康检查"""
    # 执行API健康检查
    check_response = await HealthCheckService.check_api(db)
    
    # 验证结果
    assert check_response.component == "api"
    assert check_response.is_healthy is True
    assert check_response.status == "healthy"
    assert check_response.response_time > 0
    assert "version" in check_response.details

@pytest.mark.asyncio
async def test_check_system_health(db: AsyncSession):
    """测试系统整体健康状态检查"""
    # 创建模拟的Redis客户端
    mock_redis = AsyncMock()
    mock_redis.ping = AsyncMock()
    mock_redis.info = AsyncMock(return_value={
        "redis_version": "6.0.0",
        "used_memory_human": "1M",
        "connected_clients": 1
    })
    
    # 执行系统健康检查
    health_response = await HealthCheckService.check_system_health(db, mock_redis)
    
    # 验证结果
    assert isinstance(health_response, SystemHealthResponse)
    assert health_response.status in ["healthy", "degraded", "unhealthy"]
    assert isinstance(health_response.is_healthy, bool)
    assert len(health_response.components) >= 2  # 至少包含数据库和API检查
    assert isinstance(health_response.checked_at, datetime) 