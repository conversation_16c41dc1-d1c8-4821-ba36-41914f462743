import uuid
from datetime import datetime, timedelta
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.metric_service import MetricService
from svc.apps.system.schemas.metric import (
    MetricCreate,
    MetricDefinitionCreate,
    MetricDefinitionUpdate
)

@pytest.mark.asyncio
async def test_create_metric(db: AsyncSession):
    """测试创建监控指标"""
    # 准备测试数据
    metric_data = MetricCreate(
        name="test_metric",
        value=100.0,
        unit="bytes",
        tags={"env": "test"},
        dimensions={"host": "localhost"},
        timestamp=datetime.utcnow()
    )
    
    # 创建监控指标
    metric_response = await MetricService.create_metric(db, metric_in=metric_data)
    
    # 验证结果
    assert metric_response.id is not None
    assert metric_response.name == "test_metric"
    assert metric_response.value == 100.0
    assert metric_response.unit == "bytes"
    assert metric_response.tags == {"env": "test"}
    assert metric_response.dimensions == {"host": "localhost"}

@pytest.mark.asyncio
async def test_get_metric(db: AsyncSession):
    """测试获取单个监控指标"""
    # 创建测试数据
    metric_data = MetricCreate(
        name="test_metric",
        value=100.0,
        unit="bytes"
    )
    created_metric = await MetricService.create_metric(db, metric_in=metric_data)
    
    # 获取监控指标
    metric_response = await MetricService.get_metric(db, metric_id=created_metric.id)
    
    # 验证结果
    assert metric_response is not None
    assert metric_response.id == created_metric.id
    assert metric_response.name == "test_metric"
    assert metric_response.value == 100.0

@pytest.mark.asyncio
async def test_get_metrics_by_name(db: AsyncSession):
    """测试获取指定名称的监控指标"""
    # 准备测试数据
    metric_name = "test_metric"
    for i in range(3):
        metric_data = MetricCreate(
            name=metric_name,
            value=float(i),
            unit="count"
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    # 获取指标列表
    metrics_response = await MetricService.get_metrics_by_name(
        db,
        name=metric_name,
        skip=0,
        limit=10
    )
    
    # 验证结果
    assert metrics_response.total == 3
    assert len(metrics_response.items) == 3
    assert all(metric.name == metric_name for metric in metrics_response.items)

@pytest.mark.asyncio
async def test_get_metrics_by_tag(db: AsyncSession):
    """测试获取指定标签的监控指标"""
    # 准备测试数据
    tag_key = "env"
    tag_value = "test"
    for i in range(2):
        metric_data = MetricCreate(
            name=f"metric_{i}",
            value=float(i),
            unit="count",
            tags={tag_key: tag_value}
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    # 获取指标列表
    metrics_response = await MetricService.get_metrics_by_tag(
        db,
        tag_key=tag_key,
        tag_value=tag_value
    )
    
    # 验证结果
    assert metrics_response.total == 2
    assert len(metrics_response.items) == 2
    assert all(metric.tags[tag_key] == tag_value for metric in metrics_response.items)

@pytest.mark.asyncio
async def test_get_latest_metric(db: AsyncSession):
    """测试获取指定名称的最新监控指标"""
    # 准备测试数据
    metric_name = "test_metric"
    timestamps = [
        datetime.utcnow() - timedelta(minutes=2),
        datetime.utcnow() - timedelta(minutes=1),
        datetime.utcnow()
    ]
    
    for i, timestamp in enumerate(timestamps):
        metric_data = MetricCreate(
            name=metric_name,
            value=float(i),
            unit="count",
            timestamp=timestamp
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    # 获取最新指标
    latest_metric = await MetricService.get_latest_metric(db, name=metric_name)
    
    # 验证结果
    assert latest_metric is not None
    assert latest_metric.name == metric_name
    assert latest_metric.value == 2.0  # 最后创建的指标值

@pytest.mark.asyncio
async def test_get_aggregated_metric(db: AsyncSession):
    """测试获取指定名称的聚合监控指标"""
    # 准备测试数据
    metric_name = "test_metric"
    now = datetime.utcnow()
    start_time = now - timedelta(hours=1)
    end_time = now
    
    values = [1.0, 2.0, 3.0]
    for value in values:
        metric_data = MetricCreate(
            name=metric_name,
            value=value,
            unit="count",
            timestamp=now - timedelta(minutes=30)
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    # 获取聚合值
    avg_value = await MetricService.get_aggregated_metric(
        db,
        name=metric_name,
        start_time=start_time,
        end_time=end_time,
        aggregation="avg"
    )
    
    # 验证结果
    assert avg_value == 2.0  # (1 + 2 + 3) / 3

@pytest.mark.asyncio
async def test_get_time_series(db: AsyncSession):
    """测试获取指定名称的时间序列数据"""
    # 准备测试数据
    metric_name = "test_metric"
    now = datetime.utcnow()
    start_time = now - timedelta(minutes=15)
    end_time = now
    
    # 创建指标定义
    definition_data = MetricDefinitionCreate(
        name=metric_name,
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    await MetricService.create_metric_definition(db, definition_in=definition_data)
    
    # 创建测试指标数据
    timestamps = [
        now - timedelta(minutes=10),
        now - timedelta(minutes=5),
        now
    ]
    
    for i, timestamp in enumerate(timestamps):
        metric_data = MetricCreate(
            name=metric_name,
            value=float(i),
            unit="count",
            timestamp=timestamp
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    # 获取时间序列数据
    time_series = await MetricService.get_time_series(
        db,
        name=metric_name,
        start_time=start_time,
        end_time=end_time,
        interval_minutes=5
    )
    
    # 验证结果
    assert time_series.name == metric_name
    assert time_series.unit == "count"
    assert len(time_series.points) > 0

@pytest.mark.asyncio
async def test_create_metric_definition(db: AsyncSession):
    """测试创建监控指标定义"""
    # 准备测试数据
    definition_data = MetricDefinitionCreate(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7,
        alert_threshold={"warning": 80, "critical": 90},
        alert_enabled=True,
        metadata={"owner": "test_team"}
    )
    
    # 创建指标定义
    definition_response = await MetricService.create_metric_definition(
        db,
        definition_in=definition_data
    )
    
    # 验证结果
    assert definition_response.id is not None
    assert definition_response.name == "test_metric"
    assert definition_response.display_name == "Test Metric"
    assert definition_response.unit == "count"
    assert definition_response.enabled is True
    assert definition_response.alert_threshold == {"warning": 80, "critical": 90}

@pytest.mark.asyncio
async def test_get_metric_definition(db: AsyncSession):
    """测试获取监控指标定义"""
    # 创建测试数据
    definition_data = MetricDefinitionCreate(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    created_def = await MetricService.create_metric_definition(
        db,
        definition_in=definition_data
    )
    
    # 获取指标定义
    definition_response = await MetricService.get_metric_definition(
        db,
        definition_id=created_def.id
    )
    
    # 验证结果
    assert definition_response is not None
    assert definition_response.id == created_def.id
    assert definition_response.name == "test_metric"
    assert definition_response.unit == "count"

@pytest.mark.asyncio
async def test_update_metric_definition(db: AsyncSession):
    """测试更新监控指标定义"""
    # 创建测试数据
    definition_data = MetricDefinitionCreate(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    created_def = await MetricService.create_metric_definition(
        db,
        definition_in=definition_data
    )
    
    # 更新数据
    update_data = MetricDefinitionUpdate(
        display_name="Updated Test Metric",
        description="Updated description",
        enabled=False
    )
    
    # 更新指标定义
    updated_def = await MetricService.update_metric_definition(
        db,
        definition_id=created_def.id,
        definition_in=update_data
    )
    
    # 验证结果
    assert updated_def is not None
    assert updated_def.id == created_def.id
    assert updated_def.display_name == "Updated Test Metric"
    assert updated_def.description == "Updated description"
    assert updated_def.enabled is False

@pytest.mark.asyncio
async def test_delete_metric_definition(db: AsyncSession):
    """测试删除监控指标定义"""
    # 创建测试数据
    definition_data = MetricDefinitionCreate(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    created_def = await MetricService.create_metric_definition(
        db,
        definition_in=definition_data
    )
    
    # 删除指标定义
    result = await MetricService.delete_metric_definition(
        db,
        definition_id=created_def.id
    )
    
    # 验证结果
    assert result is True
    
    # 确认已删除
    deleted_def = await MetricService.get_metric_definition(
        db,
        definition_id=created_def.id
    )
    assert deleted_def is None 