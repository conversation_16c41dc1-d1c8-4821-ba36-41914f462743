import uuid
from datetime import datetime, timedelta
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.models.audit_log import AuditLog

@pytest.mark.asyncio
async def test_create_audit_log(db: AsyncSession):
    """测试创建审计日志"""
    # 准备测试数据
    user_id = uuid.uuid4()
    test_data = {
        "action": "create",
        "resource_type": "user",
        "user_id": user_id,
        "tenant_id": "test_tenant",
        "resource_id": "123",
        "description": "Created new user",
        "ip_address": "127.0.0.1",
        "user_agent": "test-agent",
        "request_path": "/api/users",
        "request_method": "POST",
        "request_body": {"name": "test"},
        "response_status": "200",
        "metadata": {"extra": "info"}
    }
    
    # 创建审计日志
    audit_log = await AuditLog.create(db, **test_data)
    
    # 验证结果
    assert audit_log.id is not None
    assert audit_log.user_id == user_id
    assert audit_log.action == "create"
    assert audit_log.resource_type == "user"
    assert audit_log.tenant_id == "test_tenant"
    assert audit_log.description == "Created new user"
    assert audit_log.created_at is not None

@pytest.mark.asyncio
async def test_get_by_id(db: AsyncSession):
    """测试通过ID获取审计日志"""
    # 创建测试数据
    audit_log = await AuditLog.create(
        db,
        action="read",
        resource_type="user",
        description="Test get by id"
    )
    
    # 获取审计日志
    retrieved_log = await AuditLog.get_by_id(db, audit_log.id)
    
    # 验证结果
    assert retrieved_log is not None
    assert retrieved_log.id == audit_log.id
    assert retrieved_log.action == "read"
    assert retrieved_log.description == "Test get by id"

@pytest.mark.asyncio
async def test_get_by_user_id(db: AsyncSession):
    """测试获取用户的审计日志"""
    # 准备测试数据
    user_id = uuid.uuid4()
    await AuditLog.create(
        db,
        action="create",
        resource_type="user",
        user_id=user_id
    )
    await AuditLog.create(
        db,
        action="update",
        resource_type="user",
        user_id=user_id
    )
    
    # 获取用户的审计日志
    logs = await AuditLog.get_by_user_id(db, user_id)
    
    # 验证结果
    assert len(logs) == 2
    assert all(log.user_id == user_id for log in logs)

@pytest.mark.asyncio
async def test_get_by_tenant_id(db: AsyncSession):
    """测试获取租户的审计日志"""
    # 准备测试数据
    tenant_id = "test_tenant"
    await AuditLog.create(
        db,
        action="create",
        resource_type="subscription",
        tenant_id=tenant_id
    )
    await AuditLog.create(
        db,
        action="update",
        resource_type="subscription",
        tenant_id=tenant_id
    )
    
    # 获取租户的审计日志
    logs = await AuditLog.get_by_tenant_id(db, tenant_id)
    
    # 验证结果
    assert len(logs) == 2
    assert all(log.tenant_id == tenant_id for log in logs)

@pytest.mark.asyncio
async def test_get_by_resource(db: AsyncSession):
    """测试获取资源的审计日志"""
    # 准备测试数据
    resource_type = "user"
    resource_id = "123"
    await AuditLog.create(
        db,
        action="create",
        resource_type=resource_type,
        resource_id=resource_id
    )
    await AuditLog.create(
        db,
        action="update",
        resource_type=resource_type,
        resource_id=resource_id
    )
    
    # 获取资源的审计日志
    logs = await AuditLog.get_by_resource(db, resource_type, resource_id)
    
    # 验证结果
    assert len(logs) == 2
    assert all(log.resource_type == resource_type for log in logs)
    assert all(log.resource_id == resource_id for log in logs)

@pytest.mark.asyncio
async def test_get_by_action(db: AsyncSession):
    """测试获取特定操作的审计日志"""
    # 准备测试数据
    action = "delete"
    await AuditLog.create(
        db,
        action=action,
        resource_type="user"
    )
    await AuditLog.create(
        db,
        action=action,
        resource_type="role"
    )
    
    # 获取特定操作的审计日志
    logs = await AuditLog.get_by_action(db, action)
    
    # 验证结果
    assert len(logs) == 2
    assert all(log.action == action for log in logs)

@pytest.mark.asyncio
async def test_search(db: AsyncSession):
    """测试搜索审计日志"""
    # 准备测试数据
    user_id = uuid.uuid4()
    tenant_id = "test_tenant"
    now = datetime.utcnow()
    
    await AuditLog.create(
        db,
        action="create",
        resource_type="user",
        user_id=user_id,
        tenant_id=tenant_id
    )
    await AuditLog.create(
        db,
        action="update",
        resource_type="user",
        user_id=user_id,
        tenant_id=tenant_id
    )
    
    # 测试不同的搜索条件
    # 1. 按用户ID搜索
    logs = await AuditLog.search(db, user_id=user_id)
    assert len(logs) == 2
    assert all(log.user_id == user_id for log in logs)
    
    # 2. 按租户ID搜索
    logs = await AuditLog.search(db, tenant_id=tenant_id)
    assert len(logs) == 2
    assert all(log.tenant_id == tenant_id for log in logs)
    
    # 3. 按时间范围搜索
    logs = await AuditLog.search(
        db,
        start_time=now - timedelta(minutes=1),
        end_time=now + timedelta(minutes=1)
    )
    assert len(logs) == 2
    
    # 4. 组合条件搜索
    logs = await AuditLog.search(
        db,
        user_id=user_id,
        tenant_id=tenant_id,
        action="create"
    )
    assert len(logs) == 1
    assert logs[0].action == "create" 