import pytest
import uuid
from typing import Dict, Any
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException

from svc.apps.auth.services.user_service import UserService
from svc.apps.auth.models import User
from svc.apps.auth.models.role import Role
from svc.apps.auth.models import UserCreate, UserUpdate


@pytest.mark.asyncio
async def test_get_user_by_id(db: AsyncSession, test_user: Dict[str, Any]):
    """测试通过ID获取用户"""
    # 创建测试用户
    user = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    # 测试获取用户
    found_user = await UserService.get_user_by_id(db=db, user_id=user.id)
    
    # 验证结果
    assert found_user is not None
    assert found_user.id == user.id
    assert found_user.email == user.email
    assert found_user.full_name == user.full_name
    assert found_user.tenant_id == user.tenant_id
    assert found_user.is_superuser == user.is_superuser


@pytest.mark.asyncio
async def test_get_user_by_email(db: AsyncSession, test_user: Dict[str, Any]):
    """测试通过邮箱获取用户"""
    # 创建测试用户
    user = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    # 测试获取用户
    found_user = await UserService.get_user_by_email(db=db, email=user.email)
    
    # 验证结果
    assert found_user is not None
    assert found_user.id == user.id
    assert found_user.email == user.email
    assert found_user.full_name == user.full_name
    assert found_user.tenant_id == user.tenant_id
    assert found_user.is_superuser == user.is_superuser


@pytest.mark.asyncio
async def test_get_users(db: AsyncSession, test_user: Dict[str, Any], test_superuser: Dict[str, Any]):
    """测试获取用户列表"""
    # 创建测试用户
    user1 = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    user2 = await User.create(
        db=db,
        email=test_superuser["email"],
        password=test_superuser["password"],
        full_name=test_superuser["full_name"],
        tenant_id=test_superuser["tenant_id"],
        is_superuser=test_superuser["is_superuser"],
    )
    
    # 测试获取用户列表
    users = await UserService.get_users(db=db, tenant_id=test_user["tenant_id"])
    
    # 验证结果
    assert len(users) == 2
    assert any(u.id == user1.id for u in users)
    assert any(u.id == user2.id for u in users)


@pytest.mark.asyncio
async def test_create_user(db: AsyncSession, test_user: Dict[str, Any]):
    """测试创建用户"""
    # 创建用户输入数据
    user_in = UserCreate(
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
    )
    
    # 测试创建用户
    user = await UserService.create_user(db=db, user_in=user_in)
    
    # 验证结果
    assert user is not None
    assert user.email == user_in.email
    assert user.full_name == user_in.full_name
    assert user.tenant_id == user_in.tenant_id
    assert user.is_active is True
    assert user.is_superuser is False


@pytest.mark.asyncio
async def test_create_user_duplicate_email(db: AsyncSession, test_user: Dict[str, Any]):
    """测试创建用户时邮箱重复"""
    # 创建第一个用户
    user_in1 = UserCreate(
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
    )
    await UserService.create_user(db=db, user_in=user_in1)
    
    # 创建第二个用户，邮箱相同
    user_in2 = UserCreate(
        email=test_user["email"],
        password="anotherpassword",
        full_name="Another User",
        tenant_id=test_user["tenant_id"],
    )
    
    # 验证抛出异常
    with pytest.raises(HTTPException) as excinfo:
        await UserService.create_user(db=db, user_in=user_in2)
    
    assert excinfo.value.status_code == 400
    assert "该邮箱已注册" in excinfo.value.detail


@pytest.mark.asyncio
async def test_update_user(db: AsyncSession, test_user: Dict[str, Any]):
    """测试更新用户"""
    # 创建测试用户
    user = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    # 更新用户输入数据
    user_in = UserUpdate(
        full_name="Updated Name",
        password="newpassword",
        preferences={"theme": "dark"},
    )
    
    # 测试更新用户
    updated_user = await UserService.update_user(db=db, user=user, user_in=user_in)
    
    # 验证结果
    assert updated_user is not None
    assert updated_user.id == user.id
    assert updated_user.full_name == user_in.full_name
    assert updated_user.preferences == user_in.preferences


@pytest.mark.asyncio
async def test_update_user_email_duplicate(db: AsyncSession, test_user: Dict[str, Any], test_superuser: Dict[str, Any]):
    """测试更新用户邮箱时重复"""
    # 创建两个测试用户
    user1 = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    user2 = await User.create(
        db=db,
        email=test_superuser["email"],
        password=test_superuser["password"],
        full_name=test_superuser["full_name"],
        tenant_id=test_superuser["tenant_id"],
        is_superuser=test_superuser["is_superuser"],
    )
    
    # 更新用户1的邮箱为用户2的邮箱
    user_in = UserUpdate(
        email=user2.email,
    )
    
    # 验证抛出异常
    with pytest.raises(HTTPException) as excinfo:
        await UserService.update_user(db=db, user=user1, user_in=user_in)
    
    assert excinfo.value.status_code == 400
    assert "该邮箱已被使用" in excinfo.value.detail


@pytest.mark.asyncio
async def test_delete_user(db: AsyncSession, test_user: Dict[str, Any]):
    """测试删除用户"""
    # 创建测试用户
    user = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    # 测试删除用户
    await UserService.delete_user(db=db, user=user)
    
    # 验证用户已删除
    deleted_user = await UserService.get_user_by_id(db=db, user_id=user.id)
    assert deleted_user is None


@pytest.mark.asyncio
async def test_activate_deactivate_user(db: AsyncSession, test_user: Dict[str, Any]):
    """测试激活和停用用户"""
    # 创建测试用户，默认是激活状态
    user = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    # 测试停用用户
    deactivated_user = await UserService.deactivate_user(db=db, user=user)
    assert deactivated_user.is_active is False
    
    # 测试激活用户
    activated_user = await UserService.activate_user(db=db, user=deactivated_user)
    assert activated_user.is_active is True


@pytest.mark.asyncio
async def test_user_role_management(db: AsyncSession, test_user: Dict[str, Any]):
    """测试用户角色管理"""
    # 创建测试用户
    user = await User.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_superuser=test_user["is_superuser"],
    )
    
    # 创建测试角色
    role = Role(
        name="test_role",
        description="Test Role",
        permissions={"read": True, "write": False},
    )
    db.add(role)
    await db.commit()
    await db.refresh(role)
    
    # 测试分配角色
    await UserService.assign_role_to_user(db=db, user=user, role=role)
    
    # 验证角色已分配
    roles = await UserService.get_user_roles(db=db, user=user)
    assert len(roles) == 1
    assert roles[0].id == role.id
    assert roles[0].name == role.name
    
    # 测试移除角色
    await UserService.remove_role_from_user(db=db, user=user, role=role)
    
    # 验证角色已移除
    roles = await UserService.get_user_roles(db=db, user=user)
    assert len(roles) == 0 