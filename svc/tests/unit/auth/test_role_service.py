"""
角色服务(RoleService)的单元测试
"""
import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from typing import Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from svc.apps.auth.services.role import RoleService
from svc.apps.auth.models.role import Role
from svc.apps.auth.models.permission import Permission
from svc.apps.auth.schemas.role import RoleCreate, RoleResponse, RoleUpdate, RoleQuery
from svc.core.exceptions.error_codes import ErrorCode

@pytest.fixture
async def role_service(db: AsyncSession):
    """创建角色服务实例"""
    return RoleService(db=db)

@pytest.fixture
async def test_role_obj(db: AsyncSession, test_role: Dict[str, Any]) -> Role:
    """创建测试角色对象"""
    from svc.apps.auth.repositories.role import RoleRepository
    repo = RoleRepository()
    role = await repo.create(
        db=db,
        name=test_role["name"],
        description=test_role["description"],
        permissions=test_role["permissions"]
    )
    return role

@pytest.fixture
async def test_admin_role_obj(db: AsyncSession, test_admin_role: Dict[str, Any]) -> Role:
    """创建管理员角色对象"""
    from svc.apps.auth.repositories.role import RoleRepository
    repo = RoleRepository()
    role = await repo.create(
        db=db,
        name=test_admin_role["name"],
        description=test_admin_role["description"],
        permissions=test_admin_role["permissions"]
    )
    return role

@pytest.mark.asyncio
async def test_get_role_success(db: AsyncSession, role_service: RoleService, test_role_obj: Role):
    """测试成功获取角色"""
    # 调用服务方法
    result = await role_service.get_role(role_id=test_role_obj.id)
    
    # 验证结果
    assert result.success is True
    assert result.data is not None
    assert result.data.id == test_role_obj.id
    assert result.data.name == test_role_obj.name
    assert result.data.description == test_role_obj.description
    assert set(result.data.permissions) == set(["user:read", "user:create"])
    
@pytest.mark.asyncio
async def test_get_role_not_found(db: AsyncSession, role_service: RoleService):
    """测试获取不存在的角色"""
    # 调用服务方法
    result = await role_service.get_role(role_id=999)
    
    # 验证结果
    assert result.success is False
    assert result.error_code == ErrorCode.ROLE_NOT_FOUND

@pytest.mark.asyncio
async def test_get_roles(db: AsyncSession, role_service: RoleService, 
                         test_role_obj: Role, test_admin_role_obj: Role,
                         test_user: Dict[str, Any]):
    """测试获取角色列表"""
    # 关联角色到用户
    from svc.apps.auth.repositories.user import UserRepository
    from svc.apps.auth.models.user import User
    
    # 创建测试用户
    user_repo = UserRepository()
    user = await user_repo.create(
        db=db,
        email=test_user["email"],
        password=test_user["password"],
        full_name=test_user["full_name"],
        tenant_id=test_user["tenant_id"],
        is_active=test_user["is_active"],
        is_superuser=test_user["is_superuser"]
    )
    
    # 将角色分配给用户
    await role_service.role_repo.add_user_role(db, user, test_role_obj)
    await role_service.role_repo.add_user_role(db, user, test_admin_role_obj)
    
    # 测试获取用户角色
    params = RoleQuery(skip=0, limit=10)
    result = await role_service.get_roles(user_id=user.id, params=params)
    
    # 验证结果
    assert result.success is True
    assert result.data is not None
    assert result.data["total"] == 2
    assert len(result.data["items"]) == 2
    
    # 测试搜索功能
    params = RoleQuery(skip=0, limit=10, search_term="admin")
    result = await role_service.get_roles(user_id=user.id, params=params)
    
    # 验证结果
    assert result.success is True
    assert result.data["total"] == 1
    assert len(result.data["items"]) == 1
    assert result.data["items"][0].name == "admin_role"

@pytest.mark.asyncio
async def test_create_role_success(db: AsyncSession, role_service: RoleService, test_role: Dict[str, Any]):
    """测试成功创建角色"""
    # 创建角色数据
    role_data = RoleCreate(
        role_data=RoleResponse(
            id=None,
            name="new_role",
            description="新角色",
            permissions=["user:read"],
            created_at=None,
            updated_at=None
        )
    )
    
    # 调用服务方法
    result = await role_service.create_role(params=role_data)
    
    # 验证结果
    assert result.success is True
    assert result.data is not None
    assert result.data.name == "new_role"
    assert result.data.description == "新角色"
    assert "user:read" in result.data.permissions
    assert len(result.data.permissions) == 1
    
@pytest.mark.asyncio
async def test_create_role_duplicate(db: AsyncSession, role_service: RoleService, test_role_obj: Role):
    """测试创建重名角色失败"""
    # 创建同名角色
    role_data = RoleCreate(
        role_data=RoleResponse(
            id=None,
            name=test_role_obj.name,  # 使用已存在的名称
            description="测试重复角色",
            permissions=["user:read"],
            created_at=None,
            updated_at=None
        )
    )
    
    # 调用服务方法
    result = await role_service.create_role(params=role_data)
    
    # 验证结果
    assert result.success is False
    assert result.error_code == ErrorCode.ROLE_EXISTS
    
@pytest.mark.asyncio
async def test_update_role_success(db: AsyncSession, role_service: RoleService, 
                                 test_role_obj: Role, test_updated_role: Dict[str, Any]):
    """测试成功更新角色"""
    # 创建更新数据
    update_data = RoleUpdate(
        role_id=test_role_obj.id,
        role_data=RoleResponse(
            id=test_role_obj.id,
            name=test_updated_role["name"],
            description=test_updated_role["description"],
            permissions=test_updated_role["permissions"],
            created_at=None,
            updated_at=None
        )
    )
    
    # 调用服务方法
    result = await role_service.update_role(params=update_data)
    
    # 验证结果
    assert result.success is True
    assert result.data is not None
    assert result.data.id == test_role_obj.id
    assert result.data.name == test_updated_role["name"]
    assert result.data.description == test_updated_role["description"]
    assert set(result.data.permissions) == set(test_updated_role["permissions"])
    
@pytest.mark.asyncio
async def test_update_role_not_found(db: AsyncSession, role_service: RoleService, 
                                  test_updated_role: Dict[str, Any]):
    """测试更新不存在的角色"""
    # 创建更新数据
    update_data = RoleUpdate(
        role_id=999,  # 不存在的ID
        role_data=RoleResponse(
            id=999,
            name=test_updated_role["name"],
            description=test_updated_role["description"],
            permissions=test_updated_role["permissions"],
            created_at=None,
            updated_at=None
        )
    )
    
    # 调用服务方法
    result = await role_service.update_role(params=update_data)
    
    # 验证结果
    assert result.success is False
    assert result.error_code == ErrorCode.ROLE_NOT_FOUND

@pytest.mark.asyncio
async def test_delete_role_success(db: AsyncSession, role_service: RoleService, test_role_obj: Role):
    """测试成功删除角色"""
    # 调用服务方法
    result = await role_service.delete_role(RoleUpdate(role_id=test_role_obj.id))
    
    # 验证结果
    assert result.success is True
    assert result.data["deleted"] is True
    
    # 验证角色已被删除
    deleted_result = await role_service.get_role(role_id=test_role_obj.id)
    assert deleted_result.success is False

@pytest.mark.asyncio
async def test_delete_role_not_found(db: AsyncSession, role_service: RoleService):
    """测试删除不存在的角色"""
    # 调用服务方法
    result = await role_service.delete_role(RoleUpdate(role_id=999))
    
    # 验证结果
    assert result.success is False
    assert result.error_code == ErrorCode.ROLE_NOT_FOUND

@pytest.mark.asyncio
@patch('redis.asyncio.Redis')
async def test_role_caching(mock_redis, db: AsyncSession, test_role_obj: Role, test_role: Dict[str, Any]):
    """测试角色缓存功能"""
    # 设置 Redis Mock
    mock_redis_instance = AsyncMock()
    mock_redis.return_value = mock_redis_instance
    mock_redis_instance.get.return_value = None  # 第一次调用缓存未命中
    
    # 创建服务实例
    role_service = RoleService(db=db, redis=mock_redis_instance)
    
    # 第一次获取角色 - 应该从数据库获取
    result1 = await role_service.get_role(role_id=test_role_obj.id)
    
    # 验证缓存尝试获取
    mock_redis_instance.get.assert_called_once()
    
    # 验证缓存设置
    mock_redis_instance.set.assert_called_once()
    
    # 设置下一次缓存命中
    import json
    from datetime import datetime, timezone
    
    # 创建模拟的缓存数据
    mock_data = {
        "id": test_role_obj.id,
        "name": test_role["name"],
        "description": test_role["description"],
        "permissions": test_role["permissions"],
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat()
    }
    mock_redis_instance.get.reset_mock()
    mock_redis_instance.get.return_value = json.dumps(mock_data)
    
    # 第二次获取角色 - 应该从缓存获取
    result2 = await role_service.get_role(role_id=test_role_obj.id)
    
    # 验证结果
    assert result2.success is True
    assert result2.data is not None
    assert result2.data.id == test_role_obj.id
    assert result2.data.name == test_role["name"]
    
    # 验证第二次调用使用了缓存
    mock_redis_instance.get.assert_called_once()
    # 不应再次设置缓存
    assert mock_redis_instance.set.call_count == 1 