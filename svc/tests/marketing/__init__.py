"""
营销模块测试包

提供了以下测试模块：
- test_models.py: 数据模型单元测试
- test_services.py: 服务层单元测试
- test_routes.py: API路由集成测试
- test_utils.py: 测试工具函数
- test_config.py: 测试配置参数
"""

from .test_config import (
    TEST_TENANT_ID,
    TEST_USER_ID,
    CAMPAIGN_CONFIG,
    INVITATION_CONFIG,
    REWARD_CONFIG,
    API_CONFIG,
    TEST_DATA,
)

from .test_utils import (
    create_test_campaign,
    create_test_invitation,
    create_test_reward,
    assert_campaign_response,
    assert_invitation_response,
    assert_reward_response,
)

__all__ = [
    # 配置
    "TEST_TENANT_ID",
    "TEST_USER_ID",
    "CAMPAIGN_CONFIG",
    "INVITATION_CONFIG",
    "REWARD_CONFIG",
    "API_CONFIG",
    "TEST_DATA",
    
    # 工具函数
    "create_test_campaign",
    "create_test_invitation",
    "create_test_reward",
    "assert_campaign_response",
    "assert_invitation_response",
    "assert_reward_response",
] 