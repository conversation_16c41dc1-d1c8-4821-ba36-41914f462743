from datetime import datetime, timedelta
from typing import Dict, Any
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.marketing.models import Campaign, Invitation, Reward
from svc.apps.marketing.schemas import (
    CampaignCreate,
    InvitationCreate,
    RewardCreate,
)


async def create_test_campaign(
    db: AsyncSession,
    tenant_id: UUID,
    name: str = "Test Campaign",
    campaign_type: str = "fission",
    status: str = "active",
) -> Campaign:
    """创建测试营销活动"""
    campaign_data = CampaignCreate(
        tenant_id=tenant_id,
        name=name,
        type=campaign_type,
        description="Test campaign description",
        start_time=datetime.utcnow(),
        end_time=datetime.utcnow() + timedelta(days=30),
        invitation_limit=10,
        max_invitees=100,
        reward_rules={
            "inviter_reward": 10.0,
            "invitee_reward": 5.0,
            "currency": "CNY"
        },
        status=status,
    )
    return await Campaign.create(db=db, **campaign_data.dict())


async def create_test_invitation(
    db: AsyncSession,
    campaign_id: UUID,
    inviter_id: UUID,
    tenant_id: UUID,
    code: str = "TEST123",
) -> Invitation:
    """创建测试邀请"""
    invitation_data = InvitationCreate(
        campaign_id=campaign_id,
        inviter_id=inviter_id,
        tenant_id=tenant_id,
        code=code,
        expires_at=datetime.utcnow() + timedelta(days=7),
    )
    return await Invitation.create(db=db, **invitation_data.dict())


async def create_test_reward(
    db: AsyncSession,
    campaign_id: UUID,
    invitation_id: UUID,
    user_id: UUID,
    tenant_id: UUID,
    reward_type: str = "inviter_reward",
    amount: float = 10.0,
) -> Reward:
    """创建测试奖励"""
    reward_data = RewardCreate(
        campaign_id=campaign_id,
        invitation_id=invitation_id,
        user_id=user_id,
        tenant_id=tenant_id,
        type=reward_type,
        amount=amount,
        currency="CNY",
    )
    return await Reward.create(db=db, **reward_data.dict())


def assert_campaign_response(response_data: Dict[str, Any], expected_data: Dict[str, Any]):
    """验证营销活动响应数据"""
    assert response_data["tenant_id"] == str(expected_data["tenant_id"])
    assert response_data["name"] == expected_data["name"]
    assert response_data["type"] == expected_data["type"]
    assert response_data["description"] == expected_data["description"]
    assert response_data["invitation_limit"] == expected_data["invitation_limit"]
    assert response_data["max_invitees"] == expected_data["max_invitees"]
    assert response_data["reward_rules"] == expected_data["reward_rules"]


def assert_invitation_response(response_data: Dict[str, Any], expected_data: Dict[str, Any]):
    """验证邀请响应数据"""
    assert response_data["tenant_id"] == str(expected_data["tenant_id"])
    assert response_data["code"] == expected_data["code"]
    assert "expires_at" in response_data
    assert response_data["status"] == "pending"


def assert_reward_response(response_data: Dict[str, Any], expected_data: Dict[str, Any]):
    """验证奖励响应数据"""
    assert response_data["tenant_id"] == str(expected_data["tenant_id"])
    assert response_data["type"] == expected_data["type"]
    assert response_data["amount"] == expected_data["amount"]
    assert response_data["currency"] == expected_data["currency"]
    assert response_data["status"] == "pending" 