import pytest
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.exc import IntegrityError

from svc.apps.marketing.models import Campaign, Invitation, Reward
from svc.apps.marketing.schemas import CampaignCreate
from svc.tests.marketing.test_config import CAMP<PERSON>IGN_CONFIG, INVITATION_CONFIG, REWARD_CONFIG


@pytest.mark.asyncio
async def test_create_campaign(db, test_campaign):
    """测试创建营销活动"""
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        description=test_campaign["description"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        invitation_limit=test_campaign["invitation_limit"],
        max_invitees=test_campaign["max_invitees"],
        reward_rules=test_campaign["reward_rules"],
    )
    
    assert campaign.tenant_id == test_campaign["tenant_id"]
    assert campaign.name == test_campaign["name"]
    assert campaign.type == test_campaign["type"]
    assert campaign.status == "draft"
    assert campaign.total_participants == 0
    assert campaign.total_rewards == 0.0


@pytest.mark.asyncio
async def test_create_campaign_with_invalid_type(db, test_campaign):
    """测试使用无效活动类型创建活动"""
    with pytest.raises(ValueError, match="Invalid campaign type"):
        await Campaign.create(
            db=db,
            tenant_id=test_campaign["tenant_id"],
            name=test_campaign["name"],
            type="invalid_type",
            start_time=test_campaign["start_time"],
            end_time=test_campaign["end_time"],
        )


@pytest.mark.asyncio
async def test_create_campaign_with_invalid_dates(db, test_campaign):
    """测试使用无效日期创建活动"""
    with pytest.raises(ValueError, match="End time must be after start time"):
        await Campaign.create(
            db=db,
            tenant_id=test_campaign["tenant_id"],
            name=test_campaign["name"],
            type=test_campaign["type"],
            start_time=datetime.utcnow() + timedelta(days=1),
            end_time=datetime.utcnow(),
        )


@pytest.mark.asyncio
async def test_campaign_is_active(db, test_campaign):
    """测试活动状态检查"""
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=datetime.utcnow() - timedelta(days=1),
        end_time=datetime.utcnow() + timedelta(days=1),
    )
    
    # 测试草稿状态
    assert not campaign.is_active()
    
    # 测试激活状态
    await campaign.update(db, status="active")
    assert campaign.is_active()
    
    # 测试已结束状态
    await campaign.update(db, end_time=datetime.utcnow() - timedelta(hours=1))
    assert not campaign.is_active()


@pytest.mark.asyncio
async def test_campaign_invalid_status_transition(db, test_campaign):
    """测试无效的状态转换"""
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
    )
    
    # 测试从草稿状态直接转换到结束状态
    with pytest.raises(ValueError, match="Invalid status transition"):
        await campaign.update(db, status="ended")


@pytest.mark.asyncio
async def test_create_invitation(db, test_campaign, test_invitation, test_user):
    """测试创建邀请"""
    # 先创建活动
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    # 创建邀请
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
        expires_at=test_invitation["expires_at"],
    )
    
    assert invitation.campaign_id == campaign.id
    assert invitation.inviter_id == test_user["id"]
    assert invitation.status == "pending"
    assert invitation.level == 1
    assert invitation.path == "1"


@pytest.mark.asyncio
async def test_create_invitation_with_inactive_campaign(db, test_campaign, test_invitation, test_user):
    """测试在非活跃活动中创建邀请"""
    # 创建草稿状态的活动
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
    )
    
    # 尝试创建邀请
    with pytest.raises(ValueError, match="Campaign is not active"):
        await Invitation.create(
            db=db,
            campaign_id=campaign.id,
            inviter_id=test_user["id"],
            tenant_id=test_invitation["tenant_id"],
            code=test_invitation["code"],
            expires_at=test_invitation["expires_at"],
        )


@pytest.mark.asyncio
async def test_create_invitation_exceed_limit(db, test_campaign, test_invitation, test_user):
    """测试超出邀请限制"""
    # 创建活动，设置邀请限制为1
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
        invitation_limit=1,
    )
    
    # 创建第一个邀请
    await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code="TEST1",
        expires_at=test_invitation["expires_at"],
    )
    
    # 尝试创建第二个邀请
    with pytest.raises(ValueError, match="Invitation limit exceeded"):
        await Invitation.create(
            db=db,
            campaign_id=campaign.id,
            inviter_id=test_user["id"],
            tenant_id=test_invitation["tenant_id"],
            code="TEST2",
            expires_at=test_invitation["expires_at"],
        )


@pytest.mark.asyncio
async def test_invitation_accept(db, test_campaign, test_invitation, test_user):
    """测试接受邀请"""
    # 创建活动和邀请
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
        expires_at=datetime.utcnow() + timedelta(days=1),
    )
    
    # 测试接受邀请
    invitee_id = UUID("12345678-1234-5678-1234-************")
    await invitation.accept(db, invitee_id=invitee_id)
    
    assert invitation.status == "accepted"
    assert invitation.invitee_id == invitee_id
    assert invitation.accepted_at is not None
    
    # 验证活动参与人数增加
    campaign = await Campaign.get_by_id(db, campaign.id)
    assert campaign.total_participants == 1


@pytest.mark.asyncio
async def test_accept_expired_invitation(db, test_campaign, test_invitation, test_user):
    """测试接受已过期邀请"""
    # 创建活动和已过期邀请
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
        expires_at=datetime.utcnow() - timedelta(days=1),
    )
    
    # 尝试接受已过期邀请
    invitee_id = UUID("12345678-1234-5678-1234-************")
    with pytest.raises(ValueError, match="Invitation has expired"):
        await invitation.accept(db, invitee_id=invitee_id)


@pytest.mark.asyncio
async def test_accept_already_accepted_invitation(db, test_campaign, test_invitation, test_user):
    """测试重复接受邀请"""
    # 创建活动和邀请
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
        expires_at=datetime.utcnow() + timedelta(days=1),
    )
    
    # 第一次接受邀请
    invitee_id = UUID("12345678-1234-5678-1234-************")
    await invitation.accept(db, invitee_id=invitee_id)
    
    # 尝试再次接受邀请
    with pytest.raises(ValueError, match="Invitation already accepted"):
        await invitation.accept(db, invitee_id=invitee_id)


@pytest.mark.asyncio
async def test_create_reward(db, test_campaign, test_invitation, test_reward, test_user):
    """测试创建奖励"""
    # 创建活动
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    # 创建邀请
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
    )
    
    # 创建奖励
    reward = await Reward.create(
        db=db,
        campaign_id=campaign.id,
        invitation_id=invitation.id,
        user_id=test_user["id"],
        tenant_id=test_reward["tenant_id"],
        type=test_reward["type"],
        amount=test_reward["amount"],
        currency=test_reward["currency"],
    )
    
    assert reward.campaign_id == campaign.id
    assert reward.invitation_id == invitation.id
    assert reward.user_id == test_user["id"]
    assert reward.status == "pending"
    assert reward.amount == test_reward["amount"]


@pytest.mark.asyncio
async def test_create_reward_with_invalid_amount(db, test_campaign, test_invitation, test_reward, test_user):
    """测试创建金额无效的奖励"""
    # 创建活动和邀请
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
    )
    
    # 尝试创建金额为负数的奖励
    with pytest.raises(ValueError, match="Amount must be positive"):
        await Reward.create(
            db=db,
            campaign_id=campaign.id,
            invitation_id=invitation.id,
            user_id=test_user["id"],
            tenant_id=test_reward["tenant_id"],
            type=test_reward["type"],
            amount=-10.0,
            currency=test_reward["currency"],
        )


@pytest.mark.asyncio
async def test_reward_issue(db, test_campaign, test_invitation, test_reward, test_user):
    """测试发放奖励"""
    # 创建活动
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    # 创建邀请
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
    )
    
    # 创建奖励
    reward = await Reward.create(
        db=db,
        campaign_id=campaign.id,
        invitation_id=invitation.id,
        user_id=test_user["id"],
        tenant_id=test_reward["tenant_id"],
        type=test_reward["type"],
        amount=test_reward["amount"],
        currency=test_reward["currency"],
    )
    
    # 测试发放奖励
    await reward.mark_as_issued(db)
    
    assert reward.status == "issued"
    assert reward.issued_at is not None
    
    # 验证活动奖励总额增加
    campaign = await Campaign.get_by_id(db, campaign.id)
    assert campaign.total_rewards == test_reward["amount"]


@pytest.mark.asyncio
async def test_reward_issue_already_issued(db, test_campaign, test_invitation, test_reward, test_user):
    """测试重复发放奖励"""
    # 创建活动和邀请
    campaign = await Campaign.create(
        db=db,
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active",
    )
    
    invitation = await Invitation.create(
        db=db,
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
        code=test_invitation["code"],
    )
    
    # 创建奖励
    reward = await Reward.create(
        db=db,
        campaign_id=campaign.id,
        invitation_id=invitation.id,
        user_id=test_user["id"],
        tenant_id=test_reward["tenant_id"],
        type=test_reward["type"],
        amount=test_reward["amount"],
        currency=test_reward["currency"],
    )
    
    # 第一次发放奖励
    await reward.mark_as_issued(db)
    
    # 尝试再次发放奖励
    with pytest.raises(ValueError, match="Reward already issued"):
        await reward.mark_as_issued(db) 