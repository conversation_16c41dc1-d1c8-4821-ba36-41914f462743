import pytest
from datetime import datetime, timedelta
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.marketing.services import CampaignService, InvitationService, RewardStrategyService, RewardRecordService
from svc.apps.marketing.schemas import (
    CampaignCreate,
    CampaignUpdate,
    InvitationCreate,
    RewardStrategyCreate,
)


@pytest.mark.asyncio
async def test_campaign_service_create(db, test_campaign):
    """测试创建营销活动服务"""
    campaign_service = CampaignService()
    
    campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active"
    )
    
    campaign = await campaign_service.create(db, campaign_data)
    
    assert campaign.tenant_id == test_campaign["tenant_id"]
    assert campaign.name == test_campaign["name"]
    assert campaign.type == test_campaign["type"]
    assert campaign.status == "active"
    

@pytest.mark.asyncio
async def test_campaign_service_update(db, test_campaign):
    """测试更新营销活动服务"""
    campaign_service = CampaignService()
    
    # 先创建活动
    campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active"
    )
    
    campaign = await campaign_service.create(db, campaign_data)
    
    # 然后更新活动
    updated_campaign = await campaign_service.update(
        db, 
        campaign.id, 
        {
            "name": "Updated Campaign Name",
            "status": "inactive"
        }
    )
    
    assert updated_campaign.name == "Updated Campaign Name"
    assert updated_campaign.status == "inactive"


@pytest.mark.asyncio
async def test_campaign_service_get_active_campaigns(db, test_campaign):
    """测试获取活动活动的服务"""
    campaign_service = CampaignService()
    
    # 创建一个活动的活动
    active_campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=datetime.now() - timedelta(days=1),
        end_time=datetime.now() + timedelta(days=1),
        status="active"
    )
    
    await campaign_service.create(db, active_campaign_data)
    
    # 创建一个非活动的活动
    inactive_campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name="Inactive Campaign",
        type=test_campaign["type"],
        start_time=datetime.now() - timedelta(days=3),
        end_time=datetime.now() - timedelta(days=2),
        status="inactive"
    )
    
    await campaign_service.create(db, inactive_campaign_data)
    
    # 获取活动活动
    active_campaigns = await campaign_service.get_active_campaigns(db, test_campaign["tenant_id"])
    
    assert len(active_campaigns) > 0
    for campaign in active_campaigns:
        assert campaign.status == "active"
        assert campaign.start_time <= datetime.now()
        assert campaign.end_time >= datetime.now()


@pytest.mark.asyncio
async def test_invitation_service_create(db, test_campaign, test_invitation, test_user):
    """测试创建邀请服务"""
    campaign_service = CampaignService()
    invitation_service = InvitationService()
    
    # 创建活动
    campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active"
    )
    campaign = await campaign_service.create(db, campaign_data)
    
    # 创建邀请
    invitation_data = InvitationCreate(
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
    )
    invitation = await invitation_service.create(db, invitation_data)
    
    assert invitation.campaign_id == campaign.id
    assert invitation.inviter_id == test_user["id"]
    assert invitation.tenant_id == test_invitation["tenant_id"]
    assert invitation.code is not None
    assert invitation.status == "pending"


@pytest.mark.asyncio
async def test_invitation_service_accept(db, test_campaign, test_invitation, test_user):
    """测试接受邀请服务"""
    campaign_service = CampaignService()
    invitation_service = InvitationService()
    
    # 创建活动
    campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active"
    )
    campaign = await campaign_service.create(db, campaign_data)
    
    # 创建邀请
    invitation_data = InvitationCreate(
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
    )
    invitation = await invitation_service.create(db, invitation_data)
    
    # 接受邀请
    invitee_id = test_user["id"] + 1  # 假设这是另一个用户
    accepted_invitation = await invitation_service.accept(db, invitation.code, invitee_id)
    
    assert accepted_invitation.status == "accepted"
    assert accepted_invitation.invitee_id == invitee_id
    assert accepted_invitation.accepted_at is not None


@pytest.mark.asyncio
async def test_reward_strategy_and_record_service(
    db, test_campaign, test_invitation, test_user
):
    """测试奖励策略和奖励记录服务"""
    campaign_service = CampaignService()
    invitation_service = InvitationService()
    reward_strategy_service = RewardStrategyService(db)
    reward_record_service = RewardRecordService(db)
    
    # 创建活动
    campaign_data = CampaignCreate(
        tenant_id=test_campaign["tenant_id"],
        name=test_campaign["name"],
        type=test_campaign["type"],
        start_time=test_campaign["start_time"],
        end_time=test_campaign["end_time"],
        status="active"
    )
    campaign_result = await campaign_service.create_campaign(
        name=test_campaign["name"],
        description="Test campaign",
        start_date=test_campaign["start_time"],
        end_date=test_campaign["end_time"],
        is_active=True
    )
    campaign = campaign_result.data
    
    # 创建邀请
    invitation_data = InvitationCreate(
        campaign_id=campaign.id,
        inviter_id=test_user["id"],
        tenant_id=test_invitation["tenant_id"],
    )
    invitation_result = await invitation_service.create_invitation(
        campaign_id=campaign.id,
        inviter_id=test_user["id"]
    )
    invitation = invitation_result.data
    
    # 创建奖励策略
    strategy_result = await reward_strategy_service.create_strategy(
        campaign_id=campaign.id,
        name="Test Strategy",
        description="Test reward strategy",
        reward_type="fixed",
        is_for_inviter=True,
        is_for_invitee=False,
        base_reward=10.0
    )
    strategy = strategy_result.data
    
    assert strategy.campaign_id == campaign.id
    assert strategy.name == "Test Strategy"
    assert strategy.base_reward == 10.0
    
    # 接受邀请
    invitee_id = test_user["id"] + 1  # 假设这是另一个用户
    await invitation_service.accept_invitation(invitation.code, invitee_id)
    
    # 处理奖励
    reward_result = await reward_record_service.process_invitation_rewards(invitation.id)
    rewards = reward_result.data.get("rewards", [])
    
    assert len(rewards) > 0
    for reward in rewards:
        assert reward.campaign_id == campaign.id
        assert reward.invitation_id == invitation.id
        assert reward.is_issued == False
    
    # 发放奖励
    if rewards:
        record_id = rewards[0].id
        issue_result = await reward_record_service.issue_reward(record_id)
        issued_reward = issue_result.data
        
        assert issued_reward["record_id"] == record_id
        assert issued_reward["issued"] == True
        assert issued_reward["issued_at"] is not None 