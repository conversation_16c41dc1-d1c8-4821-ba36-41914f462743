import pytest
from datetime import datetime, timedelta
from fastapi import status
from uuid import UUID

from svc.apps.marketing.schemas import (
    CampaignCreate,
    CampaignUpdate,
    InvitationCreate,
    RewardCreate,
)


@pytest.mark.asyncio
async def test_create_campaign(client, test_campaign, test_token):
    """测试创建营销活动API"""
    campaign_data = {
        "tenant_id": str(test_campaign["tenant_id"]),
        "name": test_campaign["name"],
        "type": test_campaign["type"],
        "description": test_campaign["description"],
        "start_time": test_campaign["start_time"].isoformat(),
        "end_time": test_campaign["end_time"].isoformat(),
        "invitation_limit": test_campaign["invitation_limit"],
        "max_invitees": test_campaign["max_invitees"],
        "reward_rules": test_campaign["reward_rules"],
    }
    
    response = await client.post(
        "/api/v1/marketing/campaigns",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["name"] == test_campaign["name"]
    assert data["type"] == test_campaign["type"]
    assert data["status"] == "draft"


@pytest.mark.asyncio
async def test_get_campaign(client, test_campaign, test_token):
    """测试获取营销活动API"""
    # 先创建活动
    campaign_data = {
        "tenant_id": str(test_campaign["tenant_id"]),
        "name": test_campaign["name"],
        "type": test_campaign["type"],
        "start_time": test_campaign["start_time"].isoformat(),
        "end_time": test_campaign["end_time"].isoformat(),
    }
    
    create_response = await client.post(
        "/api/v1/marketing/campaigns",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    created_campaign = create_response.json()
    
    # 获取活动详情
    response = await client.get(
        f"/api/v1/marketing/campaigns/{created_campaign['id']}",
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["id"] == created_campaign["id"]
    assert data["name"] == test_campaign["name"]


@pytest.mark.asyncio
async def test_update_campaign(client, test_campaign, test_token):
    """测试更新营销活动API"""
    # 先创建活动
    campaign_data = {
        "tenant_id": str(test_campaign["tenant_id"]),
        "name": test_campaign["name"],
        "type": test_campaign["type"],
        "start_time": test_campaign["start_time"].isoformat(),
        "end_time": test_campaign["end_time"].isoformat(),
    }
    
    create_response = await client.post(
        "/api/v1/marketing/campaigns",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    created_campaign = create_response.json()
    
    # 更新活动
    update_data = {
        "name": "Updated Campaign",
        "status": "active"
    }
    response = await client.patch(
        f"/api/v1/marketing/campaigns/{created_campaign['id']}",
        json=update_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["name"] == "Updated Campaign"
    assert data["status"] == "active"


@pytest.mark.asyncio
async def test_create_invitation(client, test_campaign, test_invitation, test_user, test_token):
    """测试创建邀请API"""
    # 先创建活动
    campaign_data = {
        "tenant_id": str(test_campaign["tenant_id"]),
        "name": test_campaign["name"],
        "type": test_campaign["type"],
        "start_time": test_campaign["start_time"].isoformat(),
        "end_time": test_campaign["end_time"].isoformat(),
        "status": "active"
    }
    
    campaign_response = await client.post(
        "/api/v1/marketing/campaigns",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    campaign = campaign_response.json()
    
    # 创建邀请
    invitation_data = {
        "campaign_id": campaign["id"],
        "inviter_id": str(test_user["id"]),
        "tenant_id": str(test_invitation["tenant_id"]),
    }
    
    response = await client.post(
        "/api/v1/marketing/invitations",
        json=invitation_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert response.status_code == status.HTTP_201_CREATED
    data = response.json()
    assert data["campaign_id"] == campaign["id"]
    assert data["inviter_id"] == str(test_user["id"])
    assert data["status"] == "pending"
    assert "code" in data


@pytest.mark.asyncio
async def test_accept_invitation(client, test_campaign, test_invitation, test_user, test_token):
    """测试接受邀请API"""
    # 先创建活动
    campaign_data = {
        "tenant_id": str(test_campaign["tenant_id"]),
        "name": test_campaign["name"],
        "type": test_campaign["type"],
        "start_time": test_campaign["start_time"].isoformat(),
        "end_time": test_campaign["end_time"].isoformat(),
        "status": "active"
    }
    
    campaign_response = await client.post(
        "/api/v1/marketing/campaigns",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    campaign = campaign_response.json()
    
    # 创建邀请
    invitation_data = {
        "campaign_id": campaign["id"],
        "inviter_id": str(test_user["id"]),
        "tenant_id": str(test_invitation["tenant_id"]),
    }
    
    invitation_response = await client.post(
        "/api/v1/marketing/invitations",
        json=invitation_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    invitation = invitation_response.json()
    
    # 接受邀请
    invitee_id = "12345678-1234-5678-1234-567812345678"
    accept_data = {
        "invitee_id": invitee_id
    }
    
    response = await client.post(
        f"/api/v1/marketing/invitations/{invitation['code']}/accept",
        json=accept_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["status"] == "accepted"
    assert data["invitee_id"] == invitee_id
    assert data["accepted_at"] is not None


@pytest.mark.asyncio
async def test_create_and_issue_reward(
    client, test_campaign, test_invitation, test_reward, test_user, test_token
):
    """测试创建和发放奖励API"""
    # 先创建活动
    campaign_data = {
        "tenant_id": str(test_campaign["tenant_id"]),
        "name": test_campaign["name"],
        "type": test_campaign["type"],
        "start_time": test_campaign["start_time"].isoformat(),
        "end_time": test_campaign["end_time"].isoformat(),
        "status": "active"
    }
    
    campaign_response = await client.post(
        "/api/v1/marketing/campaigns",
        json=campaign_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    campaign = campaign_response.json()
    
    # 创建邀请
    invitation_data = {
        "campaign_id": campaign["id"],
        "inviter_id": str(test_user["id"]),
        "tenant_id": str(test_invitation["tenant_id"]),
    }
    
    invitation_response = await client.post(
        "/api/v1/marketing/invitations",
        json=invitation_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    invitation = invitation_response.json()
    
    # 创建奖励
    reward_data = {
        "campaign_id": campaign["id"],
        "invitation_id": invitation["id"],
        "user_id": str(test_user["id"]),
        "tenant_id": str(test_reward["tenant_id"]),
        "type": test_reward["type"],
        "amount": test_reward["amount"],
        "currency": test_reward["currency"],
    }
    
    reward_response = await client.post(
        "/api/v1/marketing/rewards",
        json=reward_data,
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert reward_response.status_code == status.HTTP_201_CREATED
    reward = reward_response.json()
    assert reward["status"] == "pending"
    
    # 发放奖励
    response = await client.post(
        f"/api/v1/marketing/rewards/{reward['id']}/issue",
        headers={"Authorization": f"Bearer {test_token}"}
    )
    
    assert response.status_code == status.HTTP_200_OK
    data = response.json()
    assert data["status"] == "issued"
    assert data["issued_at"] is not None 