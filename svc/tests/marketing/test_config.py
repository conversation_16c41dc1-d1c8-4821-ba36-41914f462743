from datetime import timedelta
from uuid import UUID

# 测试租户ID
TEST_TENANT_ID = UUID("11111111-1111-1111-1111-111111111111")

# 测试用户ID
TEST_USER_ID = UUID("*************-2222-2222-************")

# 营销活动配置
CAMPAIGN_CONFIG = {
    # 活动类型
    "TYPES": ["fission", "invite", "referral"],
    
    # 活动状态
    "STATUS": ["draft", "active", "paused", "ended"],
    
    # 默认邀请限制
    "DEFAULT_INVITATION_LIMIT": 10,
    
    # 默认最大邀请人数
    "DEFAULT_MAX_INVITEES": 100,
    
    # 默认活动有效期（天）
    "DEFAULT_DURATION_DAYS": 30,
    
    # 奖励规则配置
    "REWARD_RULES": {
        "fission": {
            "inviter_reward": 10.0,
            "invitee_reward": 5.0,
            "currency": "CNY"
        },
        "invite": {
            "direct_reward": 8.0,
            "indirect_reward": 2.0,
            "currency": "CNY"
        },
        "referral": {
            "referrer_reward": 15.0,
            "referee_reward": 5.0,
            "currency": "CNY"
        }
    }
}

# 邀请配置
INVITATION_CONFIG = {
    # 邀请码长度
    "CODE_LENGTH": 8,
    
    # 默认邀请有效期（天）
    "DEFAULT_EXPIRES_DAYS": 7,
    
    # 邀请状态
    "STATUS": ["pending", "accepted", "expired", "cancelled"],
    
    # 最大邀请层级
    "MAX_LEVEL": 3,
}

# 奖励配置
REWARD_CONFIG = {
    # 奖励类型
    "TYPES": ["inviter_reward", "invitee_reward", "direct_reward", 
              "indirect_reward", "referrer_reward", "referee_reward"],
    
    # 奖励状态
    "STATUS": ["pending", "issued", "failed", "cancelled"],
    
    # 支持的货币类型
    "CURRENCIES": ["CNY", "USD", "EUR"],
    
    # 最小奖励金额
    "MIN_AMOUNT": 0.01,
    
    # 最大奖励金额
    "MAX_AMOUNT": 1000.00,
}

# API配置
API_CONFIG = {
    # API版本
    "VERSION": "v1",
    
    # API前缀
    "PREFIX": "/api/v1/marketing",
    
    # 分页配置
    "PAGINATION": {
        "DEFAULT_PAGE": 1,
        "DEFAULT_SIZE": 10,
        "MAX_SIZE": 100,
    },
    
    # 接口限流配置
    "RATE_LIMIT": {
        "WINDOW_SIZE": timedelta(minutes=1),
        "MAX_REQUESTS": 100,
    },
}

# 测试数据配置
TEST_DATA = {
    # 测试营销活动
    "campaign": {
        "tenant_id": TEST_TENANT_ID,
        "name": "Test Campaign",
        "type": "fission",
        "description": "Test campaign description",
        "invitation_limit": CAMPAIGN_CONFIG["DEFAULT_INVITATION_LIMIT"],
        "max_invitees": CAMPAIGN_CONFIG["DEFAULT_MAX_INVITEES"],
        "reward_rules": CAMPAIGN_CONFIG["REWARD_RULES"]["fission"],
    },
    
    # 测试邀请
    "invitation": {
        "tenant_id": TEST_TENANT_ID,
        "code": "TEST123",
        "expires_at_days": INVITATION_CONFIG["DEFAULT_EXPIRES_DAYS"],
    },
    
    # 测试奖励
    "reward": {
        "tenant_id": TEST_TENANT_ID,
        "type": "inviter_reward",
        "amount": 10.0,
        "currency": "CNY",
    },
} 