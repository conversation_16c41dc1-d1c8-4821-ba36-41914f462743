from datetime import datetime, <PERSON><PERSON><PERSON>
import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.audit_log_service import AuditLogService
from svc.apps.system.services.health_check_service import HealthCheckService
from svc.apps.system.services.metric_service import MetricService
from svc.apps.system.schemas.audit_log import AuditLogCreate
from svc.apps.system.schemas.health_check import HealthCheckCreate
from svc.apps.system.schemas.metric import MetricCreate, MetricDefinitionCreate
from svc.core.exceptions import NotFoundError

@pytest.mark.asyncio
async def test_audit_log_service_integration(db: AsyncSession):
    """测试审计日志服务集成"""
    # 创建审计日志
    log_data = AuditLogCreate(
        action="test_action",
        resource_type="test_resource",
        resource_id="123",
        description="Test log",
        user_id="test_user",
        tenant_id="test_tenant"
    )
    log = await AuditLogService.create_log(db, log_in=log_data)
    assert log.id is not None
    assert log.action == "test_action"
    
    # 获取审计日志
    retrieved_log = await AuditLogService.get_log(db, log_id=log.id)
    assert retrieved_log.id == log.id
    assert retrieved_log.action == log.action
    
    # 获取用户审计日志
    user_logs = await AuditLogService.get_logs_by_user(
        db,
        user_id="test_user",
        skip=0,
        limit=10
    )
    assert user_logs.total >= 1
    assert any(l.id == log.id for l in user_logs.items)
    
    # 获取租户审计日志
    tenant_logs = await AuditLogService.get_logs_by_tenant(
        db,
        tenant_id="test_tenant",
        skip=0,
        limit=10
    )
    assert tenant_logs.total >= 1
    assert any(l.id == log.id for l in tenant_logs.items)
    
    # 搜索审计日志
    search_result = await AuditLogService.search_logs(
        db,
        action="test_action",
        resource_type="test_resource",
        skip=0,
        limit=10
    )
    assert search_result.total >= 1
    assert any(l.id == log.id for l in search_result.items)

@pytest.mark.asyncio
async def test_health_check_service_integration(db: AsyncSession):
    """测试健康检查服务集成"""
    # 创建健康检查记录
    check_data = HealthCheckCreate(
        component="test_component",
        status="healthy",
        details={"message": "Test check"}
    )
    check = await HealthCheckService.create_check(db, check_in=check_data)
    assert check.id is not None
    assert check.component == "test_component"
    assert check.status == "healthy"
    
    # 获取健康检查记录
    retrieved_check = await HealthCheckService.get_check(db, check_id=check.id)
    assert retrieved_check.id == check.id
    assert retrieved_check.component == check.component
    
    # 获取组件最新健康检查
    latest_check = await HealthCheckService.get_latest_by_component(
        db,
        component="test_component"
    )
    assert latest_check.id == check.id
    
    # 获取组件健康检查历史
    history = await HealthCheckService.get_component_history(
        db,
        component="test_component",
        skip=0,
        limit=10
    )
    assert history.total >= 1
    assert any(c.id == check.id for c in history.items)
    
    # 获取不健康的检查记录
    unhealthy_check_data = HealthCheckCreate(
        component="test_component",
        status="unhealthy",
        details={"error": "Test error"}
    )
    await HealthCheckService.create_check(db, check_in=unhealthy_check_data)
    
    unhealthy_checks = await HealthCheckService.get_unhealthy_checks(
        db,
        skip=0,
        limit=10
    )
    assert unhealthy_checks.total >= 1
    assert any(c.status == "unhealthy" for c in unhealthy_checks.items)
    
    # 检查系统健康状态
    system_health = await HealthCheckService.check_system_health(db)
    assert isinstance(system_health, dict)
    assert "status" in system_health
    assert "components" in system_health

@pytest.mark.asyncio
async def test_metric_service_integration(db: AsyncSession):
    """测试监控指标服务集成"""
    # 创建指标定义
    definition_data = MetricDefinitionCreate(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    definition = await MetricService.create_metric_definition(
        db,
        definition_in=definition_data
    )
    assert definition.id is not None
    assert definition.name == "test_metric"
    
    # 创建指标数据
    now = datetime.utcnow()
    metric_data = MetricCreate(
        name="test_metric",
        value=100.0,
        unit="count",
        tags={"env": "test"},
        dimensions={"host": "localhost"},
        timestamp=now
    )
    metric = await MetricService.create_metric(db, metric_in=metric_data)
    assert metric.id is not None
    assert metric.name == "test_metric"
    assert metric.value == 100.0
    
    # 获取指标
    retrieved_metric = await MetricService.get_metric(db, metric_id=metric.id)
    assert retrieved_metric.id == metric.id
    assert retrieved_metric.name == metric.name
    
    # 获取指标定义
    retrieved_definition = await MetricService.get_metric_definition(
        db,
        definition_id=definition.id
    )
    assert retrieved_definition.id == definition.id
    assert retrieved_definition.name == definition.name
    
    # 获取指标时间序列数据
    for i in range(3):
        metric_data = MetricCreate(
            name="test_metric",
            value=float(i),
            unit="count",
            timestamp=now - timedelta(minutes=i * 5)
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    time_series = await MetricService.get_time_series(
        db,
        metric_name="test_metric",
        start_time=now - timedelta(minutes=15),
        end_time=now,
        interval_minutes=5
    )
    assert time_series.name == "test_metric"
    assert len(time_series.points) > 0
    
    # 获取聚合指标
    aggregated = await MetricService.get_aggregated_metric(
        db,
        metric_name="test_metric",
        start_time=now - timedelta(minutes=15),
        end_time=now,
        aggregation="avg"
    )
    assert aggregated.name == "test_metric"
    assert isinstance(aggregated.value, float)
    
    # 更新指标定义
    definition.description = "Updated description"
    updated_definition = await MetricService.update_metric_definition(
        db,
        definition=definition
    )
    assert updated_definition.description == "Updated description"
    
    # 删除指标定义
    await MetricService.delete_metric_definition(db, definition_id=definition.id)
    with pytest.raises(NotFoundError):
        await MetricService.get_metric_definition(db, definition_id=definition.id)

@pytest.mark.asyncio
async def test_error_handling(db: AsyncSession):
    """测试错误处理"""
    # 测试获取不存在的审计日志
    with pytest.raises(NotFoundError):
        await AuditLogService.get_log(db, log_id="non_existent_id")
    
    # 测试获取不存在的健康检查记录
    with pytest.raises(NotFoundError):
        await HealthCheckService.get_check(db, check_id="non_existent_id")
    
    # 测试获取不存在的指标
    with pytest.raises(NotFoundError):
        await MetricService.get_metric(db, metric_id="non_existent_id")
    
    # 测试获取不存在的指标定义
    with pytest.raises(NotFoundError):
        await MetricService.get_metric_definition(
            db,
            definition_id="non_existent_id"
        ) 