from datetime import datetime, timedelta
import pytest
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.models.audit_log import AuditLog
from svc.apps.system.models.health_check import HealthCheck
from svc.apps.system.models.metric import Metric, MetricDefinition
from svc.core.database.session import async_session

@pytest.mark.asyncio
async def test_audit_log_model_integration(db: AsyncSession):
    """测试审计日志模型集成"""
    # 创建审计日志
    log = AuditLog(
        action="test_action",
        resource_type="test_resource",
        resource_id="123",
        description="Test log",
        user_id="test_user",
        tenant_id="test_tenant"
    )
    db.add(log)
    await db.commit()
    await db.refresh(log)
    
    assert log.id is not None
    assert log.action == "test_action"
    assert log.created_at is not None
    
    # 查询审计日志
    stmt = select(AuditLog).where(AuditLog.id == log.id)
    result = await db.execute(stmt)
    retrieved_log = result.scalar_one()
    
    assert retrieved_log.id == log.id
    assert retrieved_log.action == log.action
    assert retrieved_log.user_id == "test_user"
    assert retrieved_log.tenant_id == "test_tenant"
    
    # 更新审计日志
    log.description = "Updated description"
    await db.commit()
    await db.refresh(log)
    
    assert log.description == "Updated description"
    
    # 删除审计日志
    await db.delete(log)
    await db.commit()
    
    result = await db.execute(stmt)
    assert result.first() is None

@pytest.mark.asyncio
async def test_health_check_model_integration(db: AsyncSession):
    """测试健康检查模型集成"""
    # 创建健康检查记录
    check = HealthCheck(
        component="test_component",
        status="healthy",
        details={"message": "Test check"}
    )
    db.add(check)
    await db.commit()
    await db.refresh(check)
    
    assert check.id is not None
    assert check.component == "test_component"
    assert check.status == "healthy"
    assert check.created_at is not None
    
    # 查询健康检查记录
    stmt = select(HealthCheck).where(HealthCheck.id == check.id)
    result = await db.execute(stmt)
    retrieved_check = result.scalar_one()
    
    assert retrieved_check.id == check.id
    assert retrieved_check.component == check.component
    assert retrieved_check.status == "healthy"
    
    # 更新健康检查记录
    check.status = "unhealthy"
    check.details = {"error": "Test error"}
    await db.commit()
    await db.refresh(check)
    
    assert check.status == "unhealthy"
    assert check.details["error"] == "Test error"
    
    # 删除健康检查记录
    await db.delete(check)
    await db.commit()
    
    result = await db.execute(stmt)
    assert result.first() is None

@pytest.mark.asyncio
async def test_metric_model_integration(db: AsyncSession):
    """测试监控指标模型集成"""
    # 创建指标定义
    definition = MetricDefinition(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    db.add(definition)
    await db.commit()
    await db.refresh(definition)
    
    assert definition.id is not None
    assert definition.name == "test_metric"
    assert definition.enabled is True
    
    # 创建指标数据
    now = datetime.utcnow()
    metric = Metric(
        name="test_metric",
        value=100.0,
        unit="count",
        tags={"env": "test"},
        dimensions={"host": "localhost"},
        timestamp=now
    )
    db.add(metric)
    await db.commit()
    await db.refresh(metric)
    
    assert metric.id is not None
    assert metric.name == "test_metric"
    assert metric.value == 100.0
    assert metric.timestamp is not None
    
    # 查询指标定义
    stmt = select(MetricDefinition).where(MetricDefinition.id == definition.id)
    result = await db.execute(stmt)
    retrieved_definition = result.scalar_one()
    
    assert retrieved_definition.id == definition.id
    assert retrieved_definition.name == definition.name
    
    # 查询指标数据
    stmt = select(Metric).where(Metric.id == metric.id)
    result = await db.execute(stmt)
    retrieved_metric = result.scalar_one()
    
    assert retrieved_metric.id == metric.id
    assert retrieved_metric.name == metric.name
    assert retrieved_metric.value == metric.value
    
    # 更新指标定义
    definition.description = "Updated description"
    await db.commit()
    await db.refresh(definition)
    
    assert definition.description == "Updated description"
    
    # 更新指标数据
    metric.value = 200.0
    await db.commit()
    await db.refresh(metric)
    
    assert metric.value == 200.0
    
    # 删除指标数据和定义
    await db.delete(metric)
    await db.delete(definition)
    await db.commit()
    
    result = await db.execute(select(Metric).where(Metric.id == metric.id))
    assert result.first() is None
    
    result = await db.execute(select(MetricDefinition).where(
        MetricDefinition.id == definition.id
    ))
    assert result.first() is None

@pytest.mark.asyncio
async def test_model_relationships(db: AsyncSession):
    """测试模型关系"""
    # 创建指标定义和相关指标
    definition = MetricDefinition(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    db.add(definition)
    await db.commit()
    
    now = datetime.utcnow()
    metrics = []
    for i in range(3):
        metric = Metric(
            name="test_metric",
            value=float(i),
            unit="count",
            timestamp=now - timedelta(minutes=i * 5)
        )
        metrics.append(metric)
    
    db.add_all(metrics)
    await db.commit()
    
    # 查询指标定义相关的指标数据
    stmt = select(Metric).where(Metric.name == definition.name)
    result = await db.execute(stmt)
    related_metrics = result.scalars().all()
    
    assert len(related_metrics) == 3
    assert all(m.name == "test_metric" for m in related_metrics)
    
    # 清理测试数据
    for metric in metrics:
        await db.delete(metric)
    await db.delete(definition)
    await db.commit()

@pytest.mark.asyncio
async def test_model_constraints(db: AsyncSession):
    """测试模型约束"""
    # 测试唯一约束
    definition1 = MetricDefinition(
        name="unique_metric",
        display_name="Unique Metric",
        description="Test unique constraint",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    db.add(definition1)
    await db.commit()
    
    # 尝试创建同名的指标定义
    definition2 = MetricDefinition(
        name="unique_metric",
        display_name="Duplicate Metric",
        description="Test unique constraint",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    db.add(definition2)
    
    with pytest.raises(Exception):  # 违反唯一约束
        await db.commit()
    
    await db.rollback()
    
    # 测试非空约束
    with pytest.raises(Exception):  # 违反非空约束
        invalid_check = HealthCheck(
            component=None,  # component 字段不能为空
            status="healthy"
        )
        db.add(invalid_check)
        await db.commit()
    
    await db.rollback()
    
    # 清理测试数据
    await db.delete(definition1)
    await db.commit() 