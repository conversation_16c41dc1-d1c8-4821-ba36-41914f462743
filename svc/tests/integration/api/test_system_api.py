from datetime import datetime, timed<PERSON>ta
import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.system.services.audit_log_service import AuditLogService
from svc.apps.system.services.health_check_service import HealthCheckService
from svc.apps.system.services.metric_service import MetricService
from svc.apps.system.schemas.audit_log import AuditLogCreate
from svc.apps.system.schemas.metric import MetricCreate, MetricDefinitionCreate

def test_get_health(client: TestClient):
    """测试获取系统健康状态"""
    response = client.get("/api/v1/system/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "components" in data
    assert isinstance(data["components"], list)

def test_get_metrics(client: TestClient):
    """测试获取系统指标"""
    response = client.get("/api/v1/system/metrics")
    assert response.status_code == 200
    data = response.json()
    assert isinstance(data, list)

@pytest.mark.asyncio
async def test_get_audit_logs(
    client: TestClient,
    db: AsyncSession,
    superuser_client: TestClient
):
    """测试获取审计日志"""
    # 创建测试数据
    for i in range(3):
        log_data = AuditLogCreate(
            action="test_action",
            resource_type="test_resource",
            description=f"Test log {i}"
        )
        await AuditLogService.create_log(db, log_in=log_data)
    
    # 使用超级用户客户端获取审计日志
    response = superuser_client.get("/api/v1/system/audit-logs")
    assert response.status_code == 200
    data = response.json()
    assert data["total"] >= 3
    assert len(data["items"]) >= 3

@pytest.mark.asyncio
async def test_get_audit_log_by_id(
    client: TestClient,
    db: AsyncSession,
    superuser_client: TestClient
):
    """测试通过ID获取审计日志"""
    # 创建测试数据
    log_data = AuditLogCreate(
        action="test_action",
        resource_type="test_resource",
        description="Test log"
    )
    log = await AuditLogService.create_log(db, log_in=log_data)
    
    # 使用超级用户客户端获取审计日志
    response = superuser_client.get(f"/api/v1/system/audit-logs/{log.id}")
    assert response.status_code == 200
    data = response.json()
    assert data["id"] == str(log.id)
    assert data["action"] == "test_action"

@pytest.mark.asyncio
async def test_search_audit_logs(
    client: TestClient,
    db: AsyncSession,
    superuser_client: TestClient
):
    """测试搜索审计日志"""
    # 创建测试数据
    action = "test_search"
    for i in range(3):
        log_data = AuditLogCreate(
            action=action,
            resource_type="test_resource",
            description=f"Test log {i}"
        )
        await AuditLogService.create_log(db, log_in=log_data)
    
    # 使用超级用户客户端搜索审计日志
    response = superuser_client.get(
        "/api/v1/system/audit-logs/search",
        params={"action": action}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["total"] >= 3
    assert all(item["action"] == action for item in data["items"])

@pytest.mark.asyncio
async def test_create_metric(
    client: TestClient,
    db: AsyncSession,
    superuser_client: TestClient
):
    """测试创建监控指标"""
    # 创建指标定义
    definition_data = MetricDefinitionCreate(
        name="test_metric",
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    await MetricService.create_metric_definition(db, definition_in=definition_data)
    
    # 创建指标数据
    metric_data = {
        "name": "test_metric",
        "value": 100.0,
        "unit": "count",
        "tags": {"env": "test"},
        "dimensions": {"host": "localhost"}
    }
    
    # 使用超级用户客户端创建指标
    response = superuser_client.post(
        "/api/v1/system/metrics",
        json=metric_data
    )
    assert response.status_code == 201
    data = response.json()
    assert data["name"] == "test_metric"
    assert data["value"] == 100.0

@pytest.mark.asyncio
async def test_get_metric_by_name(
    client: TestClient,
    db: AsyncSession,
    superuser_client: TestClient
):
    """测试获取指定名称的监控指标"""
    # 创建测试数据
    metric_name = "test_metric"
    metric_data = MetricCreate(
        name=metric_name,
        value=100.0,
        unit="count"
    )
    await MetricService.create_metric(db, metric_in=metric_data)
    
    # 使用超级用户客户端获取指标
    response = superuser_client.get(f"/api/v1/system/metrics/{metric_name}")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == metric_name
    assert isinstance(data["value"], float)

@pytest.mark.asyncio
async def test_get_metric_time_series(
    client: TestClient,
    db: AsyncSession,
    superuser_client: TestClient
):
    """测试获取指标时间序列数据"""
    # 创建测试数据
    metric_name = "test_metric"
    now = datetime.utcnow()
    
    # 创建指标定义
    definition_data = MetricDefinitionCreate(
        name=metric_name,
        display_name="Test Metric",
        description="Test metric description",
        unit="count",
        category="test",
        data_type="float",
        enabled=True,
        collection_interval=60,
        retention_days=7
    )
    await MetricService.create_metric_definition(db, definition_in=definition_data)
    
    # 创建指标数据
    for i in range(3):
        metric_data = MetricCreate(
            name=metric_name,
            value=float(i),
            unit="count",
            timestamp=now - timedelta(minutes=i * 5)
        )
        await MetricService.create_metric(db, metric_in=metric_data)
    
    # 使用超级用户客户端获取时间序列数据
    response = superuser_client.get(
        f"/api/v1/system/metrics/{metric_name}/time-series",
        params={
            "start_time": (now - timedelta(minutes=15)).isoformat(),
            "end_time": now.isoformat(),
            "interval_minutes": 5
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == metric_name
    assert len(data["points"]) > 0

def test_unauthorized_access(client: TestClient):
    """测试未授权访问"""
    # 测试访问需要认证的端点
    endpoints = [
        "/api/v1/system/audit-logs",
        "/api/v1/system/metrics"
    ]
    
    for endpoint in endpoints:
        response = client.get(endpoint)
        assert response.status_code == 401  # 未授权

def test_forbidden_access(authorized_client: TestClient):
    """测试禁止访问（非管理员）"""
    # 测试访问需要管理员权限的端点
    endpoints = [
        "/api/v1/system/audit-logs",
        "/api/v1/system/metrics"
    ]
    
    for endpoint in endpoints:
        response = authorized_client.get(endpoint)
        assert response.status_code == 403  # 禁止访问 