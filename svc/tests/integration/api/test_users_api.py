import pytest
import json
from typing import Dict, Any
from uuid import UUID

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.auth.models.user import User
from svc.apps.auth.models.role import Role
from svc.core.security.dependencise import get_current_user, get_current_active_user, get_current_superuser


def test_get_users_superuser(superuser_client: TestClient, test_superuser: Dict[str, Any]):
    """测试超级用户获取用户列表"""
    response = superuser_client.get("/api/v1/users/")
    assert response.status_code == 200
    users = response.json()
    assert isinstance(users, list)
    # 至少应该有一个用户（超级用户自己）
    assert len(users) >= 1


def test_get_users_normal_user(authorized_client: TestClient):
    """测试普通用户获取用户列表（应该被拒绝）"""
    response = authorized_client.get("/api/v1/users/")
    assert response.status_code == 403


def test_get_current_user(authorized_client: TestClient, test_user: Dict[str, Any]):
    """测试获取当前用户信息"""
    response = authorized_client.get("/api/v1/users/me")
    assert response.status_code == 200
    user = response.json()
    assert user["email"] == test_user["email"]
    assert user["full_name"] == test_user["full_name"]
    assert user["is_active"] is True
    assert user["is_superuser"] is False
    assert "roles" in user


def test_get_user_by_id_self(authorized_client: TestClient, test_user: Dict[str, Any]):
    """测试普通用户获取自己的信息"""
    response = authorized_client.get(f"/api/v1/users/{test_user['id']}")
    assert response.status_code == 200
    user = response.json()
    assert user["email"] == test_user["email"]
    assert user["full_name"] == test_user["full_name"]


def test_get_user_by_id_other_normal_user(authorized_client: TestClient, test_superuser: Dict[str, Any]):
    """测试普通用户获取其他用户的信息（应该被拒绝）"""
    response = authorized_client.get(f"/api/v1/users/{test_superuser['id']}")
    assert response.status_code == 403


def test_get_user_by_id_superuser(superuser_client: TestClient, test_user: Dict[str, Any]):
    """测试超级用户获取其他用户的信息"""
    response = superuser_client.get(f"/api/v1/users/{test_user['id']}")
    assert response.status_code == 200
    user = response.json()
    assert user["email"] == test_user["email"]
    assert user["full_name"] == test_user["full_name"]


def test_create_user_superuser(superuser_client: TestClient, test_superuser: Dict[str, Any]):
    """测试超级用户创建新用户"""
    new_user = {
        "email": "<EMAIL>",
        "password": "newpassword",
        "full_name": "New User",
        "tenant_id": test_superuser["tenant_id"],
    }
    response = superuser_client.post("/api/v1/users/", json=new_user)
    assert response.status_code == 200
    user = response.json()
    assert user["email"] == new_user["email"]
    assert user["full_name"] == new_user["full_name"]
    assert user["tenant_id"] == new_user["tenant_id"]
    assert user["is_active"] is True
    assert user["is_superuser"] is False


def test_create_user_normal_user(authorized_client: TestClient, test_user: Dict[str, Any]):
    """测试普通用户创建新用户（应该被拒绝）"""
    new_user = {
        "email": "<EMAIL>",
        "password": "anotherpassword",
        "full_name": "Another User",
        "tenant_id": test_user["tenant_id"],
    }
    response = authorized_client.post("/api/v1/users/", json=new_user)
    assert response.status_code == 403


def test_update_user_self(authorized_client: TestClient, test_user: Dict[str, Any]):
    """测试普通用户更新自己的信息"""
    update_data = {
        "full_name": "Updated Name",
        "preferences": {"theme": "dark"}
    }
    response = authorized_client.put(f"/api/v1/users/{test_user['id']}", json=update_data)
    assert response.status_code == 200
    user = response.json()
    assert user["full_name"] == update_data["full_name"]
    assert user["preferences"] == update_data["preferences"]


def test_update_user_other_normal_user(authorized_client: TestClient, test_superuser: Dict[str, Any]):
    """测试普通用户更新其他用户的信息（应该被拒绝）"""
    update_data = {
        "full_name": "Hacked Name"
    }
    response = authorized_client.put(f"/api/v1/users/{test_superuser['id']}", json=update_data)
    assert response.status_code == 403


def test_update_user_superuser(superuser_client: TestClient, test_user: Dict[str, Any]):
    """测试超级用户更新其他用户的信息"""
    update_data = {
        "full_name": "Admin Updated Name",
        "is_superuser": True
    }
    response = superuser_client.put(f"/api/v1/users/{test_user['id']}", json=update_data)
    assert response.status_code == 200
    user = response.json()
    assert user["full_name"] == update_data["full_name"]
    assert user["is_superuser"] is True


def test_update_user_self_superuser_status(authorized_client: TestClient, test_user: Dict[str, Any]):
    """测试普通用户更新自己的超级用户状态（应该被拒绝）"""
    update_data = {
        "is_superuser": True
    }
    response = authorized_client.put(f"/api/v1/users/{test_user['id']}", json=update_data)
    assert response.status_code == 403


def test_delete_user_normal_user(authorized_client: TestClient, superuser_client: TestClient, test_superuser: Dict[str, Any]):
    """测试普通用户删除用户（应该被拒绝）"""
    # 先创建一个新用户
    new_user = {
        "email": "<EMAIL>",
        "password": "deletepassword",
        "full_name": "To Delete",
        "tenant_id": test_superuser["tenant_id"],
    }
    response = superuser_client.post("/api/v1/users/", json=new_user)
    assert response.status_code == 200
    user_id = response.json()["id"]
    
    # 普通用户尝试删除
    response = authorized_client.delete(f"/api/v1/users/{user_id}")
    assert response.status_code == 403


def test_delete_user_superuser(superuser_client: TestClient, test_superuser: Dict[str, Any]):
    """测试超级用户删除用户"""
    # 先创建一个新用户
    new_user = {
        "email": "<EMAIL>",
        "password": "deletepassword2",
        "full_name": "To Delete 2",
        "tenant_id": test_superuser["tenant_id"],
    }
    response = superuser_client.post("/api/v1/users/", json=new_user)
    assert response.status_code == 200
    user_id = response.json()["id"]
    
    # 超级用户删除
    response = superuser_client.delete(f"/api/v1/users/{user_id}")
    assert response.status_code == 204
    
    # 验证用户已删除
    response = superuser_client.get(f"/api/v1/users/{user_id}")
    assert response.status_code == 404


def test_delete_self_superuser(superuser_client: TestClient, test_superuser: Dict[str, Any]):
    """测试超级用户删除自己（应该被拒绝）"""
    response = superuser_client.delete(f"/api/v1/users/{test_superuser['id']}")
    assert response.status_code == 400
    assert "不能删除自己" in response.json()["detail"]


def test_activate_deactivate_user(superuser_client: TestClient, test_user: Dict[str, Any]):
    """测试激活和停用用户"""
    # 停用用户
    response = superuser_client.post(f"/api/v1/users/{test_user['id']}/deactivate")
    assert response.status_code == 200
    user = response.json()
    assert user["is_active"] is False
    
    # 激活用户
    response = superuser_client.post(f"/api/v1/users/{test_user['id']}/activate")
    assert response.status_code == 200
    user = response.json()
    assert user["is_active"] is True


def test_activate_deactivate_user_normal_user(authorized_client: TestClient, test_superuser: Dict[str, Any]):
    """测试普通用户激活和停用用户（应该被拒绝）"""
    response = authorized_client.post(f"/api/v1/users/{test_superuser['id']}/deactivate")
    assert response.status_code == 403
    
    response = authorized_client.post(f"/api/v1/users/{test_superuser['id']}/activate")
    assert response.status_code == 403


def test_deactivate_self_superuser(superuser_client: TestClient, test_superuser: Dict[str, Any]):
    """测试超级用户停用自己（应该被拒绝）"""
    response = superuser_client.post(f"/api/v1/users/{test_superuser['id']}/deactivate")
    assert response.status_code == 400
    assert "不能停用自己" in response.json()["detail"]


@pytest.mark.asyncio
async def test_user_role_management(superuser_client: TestClient, authorized_client: TestClient, db: AsyncSession, test_user: Dict[str, Any], test_superuser: Dict[str, Any]):
    """测试用户角色管理"""
    # 创建测试角色
    role = Role(
        name="test_api_role",
        description="Test API Role",
        permissions={"read": True, "write": False},
    )
    db.add(role)
    await db.commit()
    await db.refresh(role)
    role_id = str(role.id)
    
    # 超级用户为普通用户分配角色
    response = superuser_client.post(f"/api/v1/users/{test_user['id']}/roles/{role_id}")
    assert response.status_code == 204
    
    # 获取用户角色
    response = superuser_client.get(f"/api/v1/users/{test_user['id']}/roles")
    assert response.status_code == 200
    roles = response.json()
    assert len(roles) >= 1
    assert any(r["id"] == role_id for r in roles)
    
    # 普通用户获取自己的角色
    response = authorized_client.get(f"/api/v1/users/{test_user['id']}/roles")
    assert response.status_code == 200
    roles = response.json()
    assert len(roles) >= 1
    assert any(r["id"] == role_id for r in roles)
    
    # 普通用户尝试为自己分配角色（应该被拒绝）
    response = authorized_client.post(f"/api/v1/users/{test_user['id']}/roles/{role_id}")
    assert response.status_code == 403
    
    # 超级用户移除普通用户的角色
    response = superuser_client.delete(f"/api/v1/users/{test_user['id']}/roles/{role_id}")
    assert response.status_code == 204
    
    # 验证角色已移除
    response = superuser_client.get(f"/api/v1/users/{test_user['id']}/roles")
    assert response.status_code == 200
    roles = response.json()
    assert not any(r["id"] == role_id for r in roles)


def test_update_user_preferences(authorized_client: TestClient, test_user: Dict[str, Any]):
    """测试更新用户偏好设置"""
    preferences = {
        "preferences": {
            "theme": "dark",
            "language": "zh-CN",
            "notifications": {
                "email": True,
                "push": False
            }
        }
    }
    response = authorized_client.put(f"/api/v1/users/{test_user['id']}/preferences", json=preferences)
    assert response.status_code == 200
    user = response.json()
    assert user["preferences"] == preferences["preferences"]


def test_update_other_user_preferences(authorized_client: TestClient, test_superuser: Dict[str, Any]):
    """测试更新其他用户的偏好设置（应该被拒绝）"""
    preferences = {
        "preferences": {
            "theme": "hacked"
        }
    }
    response = authorized_client.put(f"/api/v1/users/{test_superuser['id']}/preferences", json=preferences)
    assert response.status_code == 403


def test_update_user_preferences_superuser(superuser_client: TestClient, test_user: Dict[str, Any]):
    """测试超级用户更新其他用户的偏好设置"""
    preferences = {
        "preferences": {
            "theme": "light",
            "language": "en-US"
        }
    }
    response = superuser_client.put(f"/api/v1/users/{test_user['id']}/preferences", json=preferences)
    assert response.status_code == 200
    user = response.json()
    assert user["preferences"] == preferences["preferences"] 