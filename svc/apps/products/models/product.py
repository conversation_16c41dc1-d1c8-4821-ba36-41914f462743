"""
商品模型，定义商品的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Integer, Text, Float, ForeignKey, Enum
from sqlalchemy.orm import relationship
from typing import List, Optional, Dict

from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo
from svc.apps.albums.models.album import Album

# 商品状态枚举
class ProductStatus(str, enum.Enum):
    DRAFT = "draft"        # 草稿状态，尚未发布
    ACTIVE = "active"      # 上架销售
    INACTIVE = "inactive"  # 下架
    DISCONTINUED = "discontinued"  # 停产
    DELETED = "deleted"    # 已删除

class Product(Base):
    """
    商品模型，定义商品的基本属性和规则
    """
    __tablename__ = "products"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment="商品名称")
    description = Column(Text, nullable=True, comment="商品描述")
    short_description = Column(String(500), nullable=True, comment="商品简短描述")
    sku = Column(String(100), nullable=False, unique=True, index=True, comment="商品SKU")
    barcode = Column(String(100), nullable=True, index=True, comment="商品条码")
    
    # 分类关联
    category_id = Column(BigInteger, ForeignKey("categories.id"), nullable=True, comment="分类ID")
    album_id = Column(BigInteger, ForeignKey("albums.id"), nullable=True, unique=True, comment="关联图册ID")
    
    # 价格信息
    price = Column(Float, nullable=False, default=0.0, comment="商品价格")
    cost_price = Column(Float, nullable=True, comment="成本价格")
    market_price = Column(Float, nullable=True, comment="市场价格")
    currency = Column(String(10), nullable=False, default="CNY", comment="货币单位")
    
    # 商品状态
    status = Column(String(20), nullable=False, default=ProductStatus.DRAFT, comment="商品状态")
    is_featured = Column(Boolean, default=False, comment="是否为推荐商品")
    is_digital = Column(Boolean, default=False, comment="是否为数字商品")
    
    # 库存信息
    track_inventory = Column(Boolean, default=True, comment="是否跟踪库存")
    stock_quantity = Column(Integer, default=0, comment="库存数量")
    min_stock_level = Column(Integer, default=0, comment="最低库存警戒线")
    max_stock_level = Column(Integer, nullable=True, comment="最高库存限制")
    
    # 商品属性
    weight = Column(Float, nullable=True, comment="重量(kg)")
    dimensions = Column(DatabaseCompatibleJSON, default=dict, comment="尺寸信息")
    attributes = Column(DatabaseCompatibleJSON, default=dict, comment="商品属性")
    meta_data = Column(DatabaseCompatibleJSON, default=dict, comment="元数据")
    rich_description = Column(Text, nullable=True, comment="产品富文本详情")
    
    # SEO信息
    seo_title = Column(String(200), nullable=True, comment="SEO标题")
    seo_description = Column(Text, nullable=True, comment="SEO描述")
    seo_keywords = Column(String(500), nullable=True, comment="SEO关键词")
    
    # 统计数据
    view_count = Column(Integer, default=0, comment="浏览次数")
    sales_count = Column(Integer, default=0, comment="销售次数")
    rating_average = Column(Float, default=0.0, comment="平均评分")
    rating_count = Column(Integer, default=0, comment="评分次数")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    category = relationship("Category", back_populates="products", lazy="selectin")
    album = relationship("Album", lazy="selectin", uselist=False, foreign_keys=[album_id])
    inventories = relationship("Inventory", back_populates="product", cascade="all, delete-orphan", lazy="dynamic")
    
    def is_available(self) -> bool:
        """判断商品是否可用"""
        if self.status != ProductStatus.ACTIVE:
            return False
            
        if self.track_inventory and self.stock_quantity <= 0:
            return False
            
        return True
        
    def is_low_stock(self) -> bool:
        """判断是否库存不足"""
        if not self.track_inventory:
            return False
        return self.stock_quantity <= self.min_stock_level
        
    def to_dict(self) -> Dict:
        """将商品对象转换为字典，用于JSON序列化和缓存
        
        Returns:
            dict: 商品的字典表示
        """
        product_dict = {c.name: getattr(self, c.name) for c in self.__table__.columns if c.name not in ['created_at', 'updated_at']}
        product_dict['created_at'] = self.created_at.isoformat() if self.created_at else None
        product_dict['updated_at'] = self.updated_at.isoformat() if self.updated_at else None

        if self.album:
            product_dict['album'] = self.album.to_dict()
        else:
            product_dict['album'] = None
            
        return product_dict
