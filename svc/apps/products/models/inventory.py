"""
库存模型，定义库存的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Integer, Text, ForeignKey, Enum
from sqlalchemy.orm import relationship
from typing import Dict

from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 库存状态枚举
class InventoryStatus(str, enum.Enum):
    AVAILABLE = "available"    # 可用
    RESERVED = "reserved"      # 预留
    DAMAGED = "damaged"        # 损坏
    EXPIRED = "expired"        # 过期
    RETURNED = "returned"      # 退货

class Inventory(Base):
    """
    库存模型，定义库存的基本属性和规则
    用于跟踪商品和变体的库存变化
    """
    __tablename__ = "inventories"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    product_id = Column(BigInteger, ForeignKey("products.id"), nullable=False, comment="商品ID")
    
    # 库存信息
    quantity = Column(Integer, nullable=False, default=0, comment="库存数量")
    reserved_quantity = Column(Integer, default=0, comment="预留数量")
    available_quantity = Column(Integer, default=0, comment="可用数量")
    
    # 库存状态
    status = Column(String(20), nullable=False, default=InventoryStatus.AVAILABLE, comment="库存状态")
    
    # 库存位置
    warehouse_location = Column(String(100), nullable=True, comment="仓库位置")
    shelf_location = Column(String(100), nullable=True, comment="货架位置")
    
    # 批次信息
    batch_number = Column(String(100), nullable=True, comment="批次号")
    lot_number = Column(String(100), nullable=True, comment="批号")
    expiry_date = Column(DateTime, nullable=True, comment="过期日期")
    
    # 成本信息
    unit_cost = Column(Integer, nullable=True, comment="单位成本(分)")
    total_cost = Column(Integer, nullable=True, comment="总成本(分)")
    
    # 供应商信息
    supplier_id = Column(BigInteger, nullable=True, comment="供应商ID")
    purchase_order_id = Column(BigInteger, nullable=True, comment="采购订单ID")
    
    # 备注和元数据
    notes = Column(Text, nullable=True, comment="备注")
    meta_data = Column(DatabaseCompatibleJSON, default=dict, comment="元数据")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    product = relationship("Product", back_populates="inventories", lazy="selectin")
    
    def is_available(self) -> bool:
        """判断库存是否可用"""
        return (
            self.status == InventoryStatus.AVAILABLE and 
            self.available_quantity > 0 and
            (self.expiry_date is None or self.expiry_date > get_utc_now_without_tzinfo())
        )
        
    def is_expired(self) -> bool:
        """判断是否过期"""
        if self.expiry_date is None:
            return False
        return self.expiry_date <= get_utc_now_without_tzinfo()
        
    def reserve_quantity(self, quantity: int) -> bool:
        """预留库存数量"""
        if self.available_quantity >= quantity:
            self.reserved_quantity += quantity
            self.available_quantity -= quantity
            return True
        return False
        
    def release_reservation(self, quantity: int) -> bool:
        """释放预留库存"""
        if self.reserved_quantity >= quantity:
            self.reserved_quantity -= quantity
            self.available_quantity += quantity
            return True
        return False
        
    def to_dict(self) -> Dict:
        """将库存对象转换为字典，用于JSON序列化和缓存
        
        Returns:
            dict: 库存的字典表示
        """
        return {
            "id": self.id,
            "product_id": self.product_id,
            "quantity": self.quantity,
            "reserved_quantity": self.reserved_quantity,
            "available_quantity": self.available_quantity,
            "status": self.status,
            "warehouse_location": self.warehouse_location,
            "shelf_location": self.shelf_location,
            "batch_number": self.batch_number,
            "lot_number": self.lot_number,
            "expiry_date": self.expiry_date.isoformat() if self.expiry_date else None,
            "unit_cost": self.unit_cost,
            "total_cost": self.total_cost,
            "supplier_id": self.supplier_id,
            "purchase_order_id": self.purchase_order_id,
            "notes": self.notes,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
