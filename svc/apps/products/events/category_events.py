"""
分类事件处理器

该模块处理分类相关的事件，包括：
- 分类创建事件
- 分类更新事件
- 分类删除事件
"""

import logging
from typing import Dict, Any

from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

logger = logging.getLogger(__name__)

class CategoryEventHandler:
    """分类事件处理器类"""
    
    @staticmethod
    @local_handler.register(event_name="products:category:created")
    async def handle_category_created(event: Event):
        """处理分类创建事件
        
        当分类被创建时触发，执行以下操作：
        1. 记录日志
        2. 发送通知
        3. 更新缓存
        
        Args:
            event: 事件对象，包含分类数据
        """
        try:
            logger.info(f"处理分类创建事件: {event}")
            
            category_data = event[1]  # 获取事件数据
            category_id = category_data.get("id")
            category_name = category_data.get("name")
            parent_id = category_data.get("parent_id")
            
            logger.info(f"分类已创建: ID={category_id}, 名称={category_name}, 父分类ID={parent_id}")
            
            # 这里可以添加其他业务逻辑，如：
            # - 发送邮件通知
            # - 更新分类树缓存
            # - 同步到其他系统
            
        except Exception as e:
            logger.error(f"处理分类创建事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:category:updated")
    async def handle_category_updated(event: Event):
        """处理分类更新事件
        
        当分类被更新时触发，执行以下操作：
        1. 清除相关缓存
        2. 记录日志
        3. 发送通知
        
        Args:
            event: 事件对象，包含分类数据和更新字段
        """
        try:
            logger.info(f"处理分类更新事件: {event}")
            
            category_data = event[1]  # 获取事件数据
            category_id = category_data.get("id")
            updated_fields = category_data.get("updated_fields", [])
            category_name = category_data.get("name")
            
            logger.info(f"分类已更新: ID={category_id}, 名称={category_name}, 更新字段={updated_fields}")
            
            # 如果父分类发生变更，需要特殊处理
            if "parent_id" in updated_fields:
                logger.info(f"分类层级已变更: category_id={category_id}")
                # 可能需要重新计算分类树结构
            
            # 如果状态发生变更，可能需要特殊处理
            if "status" in updated_fields:
                status = category_data.get("status")
                logger.info(f"分类状态已变更: category_id={category_id}, status={status}")
            
            # 这里可以添加其他业务逻辑，如：
            # - 清除分类树缓存
            # - 更新搜索索引
            # - 同步到其他系统
            
        except Exception as e:
            logger.error(f"处理分类更新事件失败: {str(e)}", exc_info=True)
    
    @staticmethod
    @local_handler.register(event_name="products:category:deleted")
    async def handle_category_deleted(event: Event):
        """处理分类删除事件
        
        当分类被删除时触发，执行以下操作：
        1. 清除相关缓存
        2. 处理子分类和商品
        3. 记录日志
        
        Args:
            event: 事件对象，包含被删除的分类数据
        """
        try:
            logger.info(f"处理分类删除事件: {event}")
            
            category_data = event[1]  # 获取事件数据
            category_id = category_data.get("id")
            category_name = category_data.get("name")
            
            logger.info(f"分类已删除: ID={category_id}, 名称={category_name}")
            
            # 这里可以添加其他业务逻辑，如：
            # - 清除分类树缓存
            # - 处理孤儿子分类
            # - 处理该分类下的商品
            # - 从搜索索引中删除
            # - 同步到其他系统
            
        except Exception as e:
            logger.error(f"处理分类删除事件失败: {str(e)}", exc_info=True)
