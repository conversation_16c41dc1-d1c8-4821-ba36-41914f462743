"""
库存API路由
包含库存的创建、查询、更新和调整功能
"""
from typing import Any

from fastapi import APIRouter, Depends, Query, Path, status

from svc.core.services.result import Result
from svc.apps.auth.dependencies import (
    get_current_active_user,
    has_permission,
    resource_permission
)
from svc.apps.auth.models.user import User
from svc.apps.products.schemas.inventory import (
    InventoryCreate,
    InventoryUpdate,
    InventoryListResponse,
    GetInventoriesParams,
    InventoryAdjustmentRequest,
    InventoryReservationRequest
)
from svc.apps.products.services.inventory import InventoryService
from svc.apps.products.dependencies import get_inventory_service
from svc.core.exceptions import (
    handle_route_errors,
    INVENTORY_ERROR_MAPPING
)
from svc.core.schemas.base import PageParams

# 创建路由器
router = APIRouter(
    tags=["库存管理"]
)

# === 管理端路由 (Admin Routes) ===

@router.get("/admin/list", response_model=Result[InventoryListResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_list_inventories(
    params: PageParams = Depends(),
    product_id: int = Query(None, description="商品ID"),
    variant_id: int = Query(None, description="变体ID"),
    status: str = Query(None, description="库存状态"),
    warehouse_location: str = Query(None, description="仓库位置"),
    supplier_id: int = Query(None, description="供应商ID"),
    low_stock_only: bool = Query(None, description="仅显示低库存"),
    expired_only: bool = Query(None, description="仅显示过期库存"),
    order_by: str = Query("created_at", description="排序字段"),
    order_desc: bool = Query(True, description="是否降序"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:read"))
) -> Result[InventoryListResponse]:
    """获取库存列表 (管理端)"""
    params_obj = GetInventoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        product_id=product_id,
        variant_id=variant_id,
        status=status,
        warehouse_location=warehouse_location,
        supplier_id=supplier_id,
        low_stock_only=low_stock_only,
        expired_only=expired_only,
        order_by=order_by,
        order_desc=order_desc
    )
    result = await inventory_service.get_inventories(params=params_obj)
    return result

@router.get("/admin/{inventory_id}", response_model=Result)
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_get_inventory_details(
    inventory_id: int = Path(..., description="库存ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("inventory", "read")),
) -> Result:
    """获取库存详情 (管理端)"""
    result = await inventory_service.get_inventory(inventory_id=inventory_id)
    return result

@router.post("/admin/create", response_model=Result, status_code=status.HTTP_201_CREATED)
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_create_inventory(
    inventory_data: InventoryCreate,
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(lambda: has_permission("inventory:create")),
) -> Result:
    """创建库存记录 (管理端)"""
    result = await inventory_service.create_inventory(params=inventory_data)
    return result

@router.post("/admin/{inventory_id}/adjust", response_model=Result)
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_adjust_inventory(
    adjustment_data: InventoryAdjustmentRequest,
    inventory_id: int = Path(..., description="库存ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("inventory", "update")),
) -> Result:
    """调整库存 (管理端)"""
    result = await inventory_service.adjust_inventory(
        inventory_id=inventory_id, 
        params=adjustment_data
    )
    return result

@router.post("/admin/{inventory_id}/reserve", response_model=Result)
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def admin_reserve_inventory(
    reservation_data: InventoryReservationRequest,
    inventory_id: int = Path(..., description="库存ID"),
    inventory_service: InventoryService = Depends(get_inventory_service),
    current_user: User = Depends(get_current_active_user),
    _: Any = Depends(resource_permission("inventory", "update")),
) -> Result:
    """预留库存 (管理端)"""
    result = await inventory_service.reserve_inventory(
        inventory_id=inventory_id, 
        params=reservation_data
    )
    return result

# === 内部API路由 (Internal Routes) ===

@router.get("/internal/product/{product_id}", response_model=Result[InventoryListResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def internal_get_product_inventory(
    product_id: int = Path(..., description="商品ID"),
    params: PageParams = Depends(),
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> Result[InventoryListResponse]:
    """获取商品库存 (内部API)"""
    params_obj = GetInventoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        product_id=product_id,
        status="available"  # 只返回可用库存
    )
    result = await inventory_service.get_inventories(params=params_obj)
    return result

@router.get("/internal/variant/{variant_id}", response_model=Result[InventoryListResponse])
@handle_route_errors(INVENTORY_ERROR_MAPPING)
async def internal_get_variant_inventory(
    variant_id: int = Path(..., description="变体ID"),
    params: PageParams = Depends(),
    inventory_service: InventoryService = Depends(get_inventory_service),
) -> Result[InventoryListResponse]:
    """获取变体库存 (内部API)"""
    params_obj = GetInventoriesParams(
        page_num=params.page_num,
        page_size=params.page_size,
        variant_id=variant_id,
        status="available"  # 只返回可用库存
    )
    result = await inventory_service.get_inventories(params=params_obj)
    return result
