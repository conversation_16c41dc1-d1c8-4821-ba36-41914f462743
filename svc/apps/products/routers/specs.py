from fastapi import APIRouter, Depends, Query, Path, status
from typing import List
from svc.apps.products.schemas.specs import (
    SpecCreate, SpecUpdate, SpecResponse,
    SpecOptionCreate, SpecOptionUpdate, SpecOptionResponse,
    ProductSpecCombinationCreate, ProductSpecCombinationUpdate, ProductSpecCombinationResponse,
    SpecListResponse, SpecOptionListResponse, ProductSpecCombinationListResponse
)
from svc.apps.products.dependencies import (
    get_spec_service, get_spec_option_service, get_product_spec_combination_service
)
from fastapi import HTTPException
from svc.core.services.result import Result
from svc.core.exceptions import handle_route_errors
from svc.core.schemas.base import PageParams
from svc.apps.auth.dependencies import get_current_active_user, has_permission
from svc.apps.auth.models.user import User

# 假设已实现依赖注入和服务实例获取
# from svc.apps.products.dependencies import get_spec_service, get_spec_option_service, get_product_spec_combination_service

router = APIRouter(tags=["商品规格管理"])

# 规格相关接口
@router.post("/product/{product_id}/specs", response_model=Result[SpecResponse], summary="新增商品规格")
@handle_route_errors()
async def create_spec(
    product_id: int,
    spec: SpecCreate,
    service=Depends(get_spec_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("spec:create"))
) -> Result[SpecResponse]:
    """
    新增商品规格（需认证和spec:create权限）
    """
    return await service.create_spec(spec)

@router.get("/product/{product_id}/specs", response_model=Result[SpecListResponse], summary="获取商品规格及规格值（分页）")
@handle_route_errors()
async def list_specs(
    product_id: int,
    params: PageParams = Depends(),
    service=Depends(get_spec_service)
) -> Result[SpecListResponse]:
    """
    获取商品规格及规格值（分页）
    """
    return await service.list_specs(product_id, params)

# 规格值相关接口
@router.post("/product/{product_id}/specs/{spec_id}/options", response_model=Result[SpecOptionResponse], summary="新增商品规格值")
@handle_route_errors()
async def create_spec_option(
    product_id: int,
    spec_id: int,
    option: SpecOptionCreate,
    service=Depends(get_spec_option_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("spec:create"))
) -> Result[SpecOptionResponse]:
    """
    新增商品规格值（需认证和spec:create权限）
    """
    return await service.create_option(option)

@router.get("/product/{product_id}/specs/{spec_id}/options", response_model=Result[SpecOptionListResponse], summary="获取规格值列表（分页）")
@handle_route_errors()
async def list_spec_options(
    product_id: int,
    spec_id: int,
    params: PageParams = Depends(),
    service=Depends(get_spec_option_service)
) -> Result[SpecOptionListResponse]:
    """
    获取指定规格的规格值列表（分页）
    """
    return await service.list_options(spec_id, params)

# 规格组合相关接口
@router.post("/product/{product_id}/combinations/generate", response_model=Result[List[ProductSpecCombinationResponse]], summary="生成商品所有规格组合")
@handle_route_errors()
async def generate_combinations(
    product_id: int,
    service=Depends(get_product_spec_combination_service),
    spec_service=Depends(get_spec_service),
    spec_option_service=Depends(get_spec_option_service)
) -> Result[List[ProductSpecCombinationResponse]]:
    """
    生成商品所有规格组合
    只负责参数校验、依赖注入、调用Service，返回标准响应
    """
    return await service.generate_and_save_combinations(product_id, spec_service, spec_option_service)

@router.get("/product/{product_id}/combinations", response_model=Result[ProductSpecCombinationListResponse], summary="获取商品所有规格组合（分页）")
@handle_route_errors()
async def list_combinations(
    product_id: int,
    params: PageParams = Depends(),
    service=Depends(get_product_spec_combination_service)
) -> Result[ProductSpecCombinationListResponse]:
    """
    获取商品所有规格组合（分页）
    """
    return await service.list_combinations(product_id, params)

@router.put("/product/{product_id}/combinations/{combination_id}", response_model=Result[ProductSpecCombinationResponse], summary="更新规格组合")
@handle_route_errors()
async def update_combination(
    product_id: int,
    combination_id: int,
    update: ProductSpecCombinationUpdate,
    service=Depends(get_product_spec_combination_service),
    current_user: User = Depends(get_current_active_user),
    _: bool = Depends(has_permission("spec:update"))
) -> Result[ProductSpecCombinationResponse]:
    """
    更新规格组合（需认证和spec:update权限）
    """
    return await service.update_combination(combination_id, update.model_dump(exclude_unset=True)) 