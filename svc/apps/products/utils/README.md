# 产品管理工具类

本目录包含产品管理模块的工具类，提供价格计算、库存管理、产品验证和SKU生成等功能。

## 工具类概览

### 1. PriceCalculator（价格计算器）
提供各种价格相关的计算功能。

**主要功能：**
- 基础价格计算
- 折扣价格计算（百分比折扣、固定金额折扣）
- 批量价格计算
- 税费计算
- 利润率计算
- 价格比较和分析

**使用示例：**
```python
from svc.apps.products.utils.price_calculator import PriceCalculator

calculator = PriceCalculator(currency="CNY", precision=2)

# 计算折扣价格
discount_result = calculator.calculate_discount_price(
    original_price=100.00,
    discount_type='percentage',
    discount_value=20.0
)
print(f"折扣后价格: {discount_result['final_price']}")

# 计算批量价格
bulk_rules = [
    {'min_qty': 10, 'discount_type': 'percentage', 'discount_value': 5},
    {'min_qty': 50, 'discount_type': 'percentage', 'discount_value': 10}
]
bulk_result = calculator.calculate_bulk_price(
    unit_price=50.00,
    quantity=25,
    bulk_rules=bulk_rules
)

# 计算税费
tax_result = calculator.calculate_tax(
    price=100.00,
    tax_rate=13.0,
    tax_inclusive=False
)

# 计算利润率
profit_result = calculator.calculate_profit_margin(
    selling_price=150.00,
    cost_price=100.00
)
```

### 2. InventoryCalculator（库存计算器）
提供库存相关的计算和分析功能。

**主要功能：**
- 可用库存计算
- 安全库存计算
- 库存周转率计算
- 库存需求预测
- 缺货风险评估
- 库存价值计算

**使用示例：**
```python
from svc.apps.products.utils.inventory_calculator import InventoryCalculator

calculator = InventoryCalculator()

# 计算安全库存
safety_result = calculator.calculate_safety_stock(
    average_demand=10.0,
    lead_time_days=7,
    demand_variability=0.2,
    service_level=0.95
)

# 评估缺货风险
risk_result = calculator.assess_stockout_risk(
    current_stock=50,
    daily_demand=8.0,
    lead_time_days=7,
    safety_stock=20
)

# 计算库存周转率
turnover_result = calculator.calculate_turnover_rate(
    cost_of_goods_sold=120000.0,
    average_inventory_value=20000.0,
    period_days=365
)

# 预测库存需求
historical_sales = [
    {'date': '2023-01-01', 'quantity': 8},
    {'date': '2023-01-02', 'quantity': 12},
    # ... 更多历史数据
]
prediction_result = calculator.predict_stock_needs(
    historical_sales=historical_sales,
    forecast_days=30,
    growth_rate=0.1
)
```

### 3. ProductValidator（产品验证器）
提供产品数据验证功能。

**主要功能：**
- 基础数据验证
- 业务规则验证
- SKU格式验证
- 条码格式验证
- 价格合理性验证
- 库存一致性验证

**使用示例：**
```python
from svc.apps.products.utils.product_validator import ProductValidator

validator = ProductValidator()

# 验证产品数据
product_data = {
    'name': '智能手机',
    'sku': 'PRD123456',
    'price': 2999.00,
    'cost_price': 2000.00,
    'stock_quantity': 50,
    'track_inventory': True,
    'status': 'active'
}

validation_result = validator.validate_product(product_data, strict_mode=True)
if validation_result['valid']:
    print("产品数据验证通过")
else:
    print(f"验证失败: {validation_result['errors']}")

# 验证产品变体
variant_data = {
    'name': '红色-大号',
    'sku': 'PRD123456-RED-L',
    'product_id': 1,
    'price': 3099.00
}

variant_result = validator.validate_product_variant(variant_data, product_data)

# 验证库存一致性
inventory_result = validator.validate_inventory_consistency(
    product=product_data,
    inventories=inventory_list
)
```

### 4. SKUGenerator（SKU生成器）
提供多种SKU生成策略。

**主要功能：**
- 基于时间戳的SKU生成
- 基于分类的SKU生成
- 基于属性的变体SKU生成
- 随机SKU生成
- 基于条码的SKU生成
- SKU格式验证

**使用示例：**
```python
from svc.apps.products.utils.sku_generator import SKUGenerator

# 生成时间戳SKU
timestamp_sku = SKUGenerator.generate_timestamp_sku("PRD")
print(f"时间戳SKU: {timestamp_sku}")

# 生成分类SKU
category_sku = SKUGenerator.generate_category_sku("ELEC", 123, "PRD")
print(f"分类SKU: {category_sku}")

# 生成变体SKU
attributes = {"color": "red", "size": "L"}
variant_sku = SKUGenerator.generate_attribute_sku("PRD123456", attributes)
print(f"变体SKU: {variant_sku}")

# 生成随机SKU
random_sku = SKUGenerator.generate_random_sku(12, "PRD")
print(f"随机SKU: {random_sku}")

# 验证SKU格式
is_valid = SKUGenerator.validate_sku("PRD123456")
print(f"SKU有效性: {is_valid}")
```

## 集成使用示例

```python
from svc.apps.products.utils import (
    PriceCalculator, 
    InventoryCalculator, 
    ProductValidator, 
    SKUGenerator
)

# 创建新产品的完整流程
def create_product_with_validation():
    # 1. 生成SKU
    sku = SKUGenerator.generate_category_sku("ELEC", 1, "PRD")
    
    # 2. 创建产品数据
    product_data = {
        'name': '智能手机',
        'sku': sku,
        'price': 2999.00,
        'cost_price': 2000.00,
        'stock_quantity': 50,
        'min_stock_level': 5,
        'track_inventory': True,
        'status': 'active'
    }
    
    # 3. 验证产品数据
    validator = ProductValidator()
    validation = validator.validate_product(product_data, strict_mode=True)
    
    if not validation['valid']:
        print(f"产品验证失败: {validation['errors']}")
        return None
    
    # 4. 计算价格信息
    price_calc = PriceCalculator()
    profit = price_calc.calculate_profit_margin(
        product_data['price'],
        product_data['cost_price']
    )
    print(f"利润率: {profit['profit_margin']}%")
    
    # 5. 评估库存状况
    inventory_calc = InventoryCalculator()
    risk = inventory_calc.assess_stockout_risk(
        current_stock=product_data['stock_quantity'],
        daily_demand=2.0,
        lead_time_days=10,
        safety_stock=product_data['min_stock_level']
    )
    print(f"库存风险等级: {risk['risk_level']}")
    
    return product_data

# 使用示例
product = create_product_with_validation()
```

## 注意事项

1. **精度处理**: 价格计算使用Decimal类型确保精度
2. **错误处理**: 所有工具类都包含完善的异常处理
3. **参数验证**: 输入参数会进行基础验证
4. **扩展性**: 工具类设计支持功能扩展
5. **性能**: 计算方法经过优化，适合批量处理

## 测试

运行测试文件验证工具类功能：

```bash
cd svc/apps/products/utils
python simple_test.py
```

测试覆盖所有主要功能，确保工具类正常工作。
