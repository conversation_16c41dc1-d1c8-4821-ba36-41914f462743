"""
SKU生成器工具

该模块提供SKU（Stock Keeping Unit）生成功能，
支持多种SKU生成策略和格式。
"""

import re
import random
import string
from typing import Optional, Dict, Any
from datetime import datetime

class SKUGenerator:
    """SKU生成器类
    
    提供多种SKU生成策略：
    1. 基于时间戳的SKU
    2. 基于分类的SKU
    3. 基于属性的SKU
    4. 随机SKU
    """
    
    @staticmethod
    def generate_timestamp_sku(prefix: str = "PRD") -> str:
        """生成基于时间戳的SKU
        
        格式: {prefix}{YYYYMMDDHHMMSS}{random_suffix}
        
        Args:
            prefix: SKU前缀，默认为"PRD"
            
        Returns:
            str: 生成的SKU
        """
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = ''.join(random.choices(string.digits, k=3))
        return f"{prefix}{timestamp}{random_suffix}"
    
    @staticmethod
    def generate_category_sku(
        category_code: str, 
        sequence: int, 
        prefix: str = "PRD"
    ) -> str:
        """生成基于分类的SKU
        
        格式: {prefix}{category_code}{sequence:06d}
        
        Args:
            category_code: 分类代码
            sequence: 序列号
            prefix: SKU前缀，默认为"PRD"
            
        Returns:
            str: 生成的SKU
        """
        return f"{prefix}{category_code}{sequence:06d}"
    
    @staticmethod
    def generate_attribute_sku(
        base_sku: str,
        attributes: Dict[str, Any]
    ) -> str:
        """生成基于属性的变体SKU
        
        格式: {base_sku}-{attr1_code}-{attr2_code}...
        
        Args:
            base_sku: 基础商品SKU
            attributes: 属性字典，如 {"color": "red", "size": "L"}
            
        Returns:
            str: 生成的变体SKU
        """
        if not attributes:
            return base_sku
        
        # 生成属性代码
        attr_codes = []
        for key, value in sorted(attributes.items()):
            # 简化属性值为代码
            code = SKUGenerator._simplify_attribute_value(str(value))
            attr_codes.append(code)
        
        return f"{base_sku}-{'-'.join(attr_codes)}"
    
    @staticmethod
    def generate_random_sku(
        length: int = 12, 
        prefix: str = "PRD",
        include_letters: bool = True,
        include_numbers: bool = True
    ) -> str:
        """生成随机SKU
        
        Args:
            length: SKU总长度（包括前缀）
            prefix: SKU前缀
            include_letters: 是否包含字母
            include_numbers: 是否包含数字
            
        Returns:
            str: 生成的随机SKU
        """
        if length <= len(prefix):
            raise ValueError("SKU长度必须大于前缀长度")
        
        # 构建字符集
        chars = ""
        if include_letters:
            chars += string.ascii_uppercase
        if include_numbers:
            chars += string.digits
        
        if not chars:
            raise ValueError("必须至少包含字母或数字")
        
        # 生成随机后缀
        suffix_length = length - len(prefix)
        random_suffix = ''.join(random.choices(chars, k=suffix_length))
        
        return f"{prefix}{random_suffix}"
    
    @staticmethod
    def generate_barcode_sku(barcode: str, prefix: str = "PRD") -> str:
        """基于条码生成SKU
        
        Args:
            barcode: 商品条码
            prefix: SKU前缀
            
        Returns:
            str: 生成的SKU
        """
        # 提取条码中的数字
        digits = re.sub(r'\D', '', barcode)
        if len(digits) >= 8:
            # 使用条码的后8位
            suffix = digits[-8:]
        else:
            # 如果条码数字不足8位，用随机数补充
            suffix = digits + ''.join(random.choices(string.digits, k=8-len(digits)))
        
        return f"{prefix}{suffix}"
    
    @staticmethod
    def validate_sku(sku: str) -> bool:
        """验证SKU格式
        
        Args:
            sku: 要验证的SKU
            
        Returns:
            bool: SKU是否有效
        """
        if not sku:
            return False
        
        # SKU基本规则：
        # 1. 长度在6-50字符之间
        # 2. 只包含字母、数字和连字符
        # 3. 不能以连字符开头或结尾
        if len(sku) < 6 or len(sku) > 50:
            return False
        
        if not re.match(r'^[A-Za-z0-9][A-Za-z0-9\-]*[A-Za-z0-9]$', sku):
            return False
        
        # 不能包含连续的连字符
        if '--' in sku:
            return False
        
        return True
    
    @staticmethod
    def _simplify_attribute_value(value: str) -> str:
        """简化属性值为代码
        
        Args:
            value: 属性值
            
        Returns:
            str: 简化后的代码
        """
        # 移除空格和特殊字符，转换为大写
        code = re.sub(r'[^A-Za-z0-9]', '', value).upper()
        
        # 如果代码太长，截取前6位
        if len(code) > 6:
            code = code[:6]
        
        # 如果代码为空，使用随机代码
        if not code:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=3))
        
        return code
    
    @staticmethod
    def extract_base_sku(variant_sku: str) -> str:
        """从变体SKU中提取基础SKU
        
        Args:
            variant_sku: 变体SKU，格式如 "PRD123456-RED-L"
            
        Returns:
            str: 基础SKU，如 "PRD123456"
        """
        # 查找第一个连字符的位置
        first_dash = variant_sku.find('-')
        if first_dash == -1:
            # 没有连字符，说明这就是基础SKU
            return variant_sku
        
        return variant_sku[:first_dash]
    
    @staticmethod
    def extract_variant_attributes(variant_sku: str) -> Dict[str, str]:
        """从变体SKU中提取属性信息
        
        Args:
            variant_sku: 变体SKU，格式如 "PRD123456-RED-L"
            
        Returns:
            Dict[str, str]: 属性字典（简化版，只返回属性代码列表）
        """
        first_dash = variant_sku.find('-')
        if first_dash == -1:
            return {}
        
        # 提取属性部分
        attr_part = variant_sku[first_dash + 1:]
        attr_codes = attr_part.split('-')
        
        # 返回属性代码列表（实际应用中可能需要映射回具体属性值）
        result = {}
        for i, code in enumerate(attr_codes):
            result[f"attr_{i+1}"] = code
        
        return result
