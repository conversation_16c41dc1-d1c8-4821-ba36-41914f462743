"""
库存数据访问层。
负责库存模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""

from typing import List, Optional, Tuple

from sqlalchemy import and_, asc, desc, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from svc.apps.products.models.inventory import Inventory, InventoryStatus
from svc.apps.products.schemas.inventory import (InventoryCreate,
                                                 InventoryUpdate)
from svc.core.repositories import BaseRepository
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class InventoryRepository(BaseRepository[Inventory, InventoryCreate, InventoryUpdate]):
    """库存仓库类，提供库存数据访问方法
    
    该仓库类实现了Inventory模型的数据访问操作，
    包括库存的基本CRUD操作以及特定的数据查询和统计功能。
    """
    
    def __init__(self, db: AsyncSession):
        """初始化库存仓库"""
        super().__init__(db, Inventory)
    
    async def get_inventories(
        self,
        skip: int = 0,
        limit: int = 10,
        product_id: Optional[int] = None,
        variant_id: Optional[int] = None,
        status: Optional[str] = None,
        warehouse_location: Optional[str] = None,
        supplier_id: Optional[int] = None,
        low_stock_only: Optional[bool] = None,
        expired_only: Optional[bool] = None,
        order_by: str = "created_at",
        order_desc: bool = True
    ) -> Tuple[List[Inventory], int]:
        """获取库存列表及总数
        
        Args:
            skip: 跳过记录数
            limit: 返回记录数
            product_id: 商品ID筛选
            variant_id: 变体ID筛选
            status: 库存状态筛选
            warehouse_location: 仓库位置筛选
            supplier_id: 供应商ID筛选
            low_stock_only: 仅显示低库存
            expired_only: 仅显示过期库存
            order_by: 排序字段
            order_desc: 是否降序排序
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总记录数
        """
        query = select(self.model)
        conditions = []
        
        # 添加过滤条件
        if product_id:
            conditions.append(self.model.product_id == product_id)
        if variant_id:
            conditions.append(self.model.variant_id == variant_id)
        if status:
            conditions.append(self.model.status == status)
        if warehouse_location:
            conditions.append(self.model.warehouse_location == warehouse_location)
        if supplier_id:
            conditions.append(self.model.supplier_id == supplier_id)
        
        # 低库存筛选
        if low_stock_only:
            conditions.append(self.model.available_quantity <= 10)  # 可配置阈值
        
        # 过期库存筛选
        if expired_only:
            now = get_utc_now_without_tzinfo()
            conditions.append(
                and_(
                    self.model.expiry_date.is_not(None),
                    self.model.expiry_date <= now
                )
            )
        
        if conditions:
            query = query.where(and_(*conditions))
        
        # 添加排序
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        
        # 添加分页
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        inventories = result.scalars().all()
        
        # 获取总数的查询
        count_query = select(func.count()).select_from(self.model)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return inventories, total
    
    async def get_by_product_id(
        self, 
        product_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Inventory], int]:
        """获取指定商品的所有库存记录
        
        Args:
            product_id: 商品ID
            skip: 跳过记录数
            limit: 返回记录数
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总数
        """
        conditions = [self.model.product_id == product_id]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        inventories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return inventories, total
    
    async def get_by_variant_id(
        self, 
        variant_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Inventory], int]:
        """获取指定变体的所有库存记录
        
        Args:
            variant_id: 变体ID
            skip: 跳过记录数
            limit: 返回记录数
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总数
        """
        conditions = [self.model.variant_id == variant_id]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(desc(self.model.created_at)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        inventories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return inventories, total
    
    async def get_available_inventory(
        self, 
        product_id: int, 
        variant_id: Optional[int] = None
    ) -> List[Inventory]:
        """获取可用库存
        
        Args:
            product_id: 商品ID
            variant_id: 变体ID（可选）
            
        Returns:
            List[Inventory]: 可用库存列表
        """
        conditions = [
            self.model.product_id == product_id,
            self.model.status == InventoryStatus.AVAILABLE,
            self.model.available_quantity > 0
        ]
        
        if variant_id:
            conditions.append(self.model.variant_id == variant_id)
        
        # 排除过期库存
        now = get_utc_now_without_tzinfo()
        conditions.append(
            or_(
                self.model.expiry_date.is_(None),
                self.model.expiry_date > now
            )
        )
        
        query = select(self.model).where(*conditions).order_by(asc(self.model.created_at))
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def get_expired_inventory(
        self, 
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Inventory], int]:
        """获取过期库存
        
        Args:
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总数
        """
        now = get_utc_now_without_tzinfo()
        conditions = [
            self.model.expiry_date.is_not(None),
            self.model.expiry_date <= now,
            self.model.status != InventoryStatus.EXPIRED
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.expiry_date)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        inventories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return inventories, total
    
    async def get_low_stock_inventory(
        self, 
        threshold: int = 10,
        limit: int = 10, 
        skip: int = 0
    ) -> Tuple[List[Inventory], int]:
        """获取低库存记录
        
        Args:
            threshold: 低库存阈值
            limit: 返回结果数量限制
            skip: 结果偏移量
            
        Returns:
            Tuple[List[Inventory], int]: 库存列表和总数
        """
        conditions = [
            self.model.status == InventoryStatus.AVAILABLE,
            self.model.available_quantity <= threshold,
            self.model.available_quantity > 0
        ]
        
        # Get paginated list
        query = select(self.model).where(
            *conditions
        ).order_by(asc(self.model.available_quantity)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        inventories = result.scalars().all()
        
        # Get total count
        count_query = select(func.count()).select_from(self.model).where(*conditions)
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        
        return inventories, total
    
    async def reserve_inventory(
        self, 
        inventory_id: int, 
        quantity: int
    ) -> Optional[Inventory]:
        """预留库存
        
        Args:
            inventory_id: 库存ID
            quantity: 预留数量
            
        Returns:
            Optional[Inventory]: 更新后的库存对象，不存在或预留失败则返回None
        """
        inventory = await self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        if inventory.available_quantity >= quantity:
            update_data = {
                "reserved_quantity": inventory.reserved_quantity + quantity,
                "available_quantity": inventory.available_quantity - quantity
            }
            return await self.update(inventory, update_data)
        
        return None
    
    async def release_reservation(
        self, 
        inventory_id: int, 
        quantity: int
    ) -> Optional[Inventory]:
        """释放预留库存
        
        Args:
            inventory_id: 库存ID
            quantity: 释放数量
            
        Returns:
            Optional[Inventory]: 更新后的库存对象，不存在或释放失败则返回None
        """
        inventory = await self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        if inventory.reserved_quantity >= quantity:
            update_data = {
                "reserved_quantity": inventory.reserved_quantity - quantity,
                "available_quantity": inventory.available_quantity + quantity
            }
            return await self.update(inventory, update_data)
        
        return None
    
    async def adjust_inventory(
        self, 
        inventory_id: int, 
        quantity_change: int,
        reason: str = ""
    ) -> Optional[Inventory]:
        """调整库存数量
        
        Args:
            inventory_id: 库存ID
            quantity_change: 数量变化（正数为增加，负数为减少）
            reason: 调整原因
            
        Returns:
            Optional[Inventory]: 更新后的库存对象，不存在则返回None
        """
        inventory = await self.get_by_id(inventory_id)
        if not inventory:
            return None
        
        new_quantity = max(0, inventory.quantity + quantity_change)
        new_available = max(0, inventory.available_quantity + quantity_change)
        
        update_data = {
            "quantity": new_quantity,
            "available_quantity": new_available,
            "notes": f"{inventory.notes or ''}\n调整: {quantity_change}, 原因: {reason}".strip()
        }
        return await self.update(inventory, update_data)
