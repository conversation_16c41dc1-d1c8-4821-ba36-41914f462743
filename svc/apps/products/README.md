# 商品管理模块

基于市场模块模板创建的商品管理模块，提供完整的商品、分类、规格和库存管理功能。

## 模块结构

```
svc/apps/products/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── main.py                     # 主路由文件
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── product.py             # 商品模型
│   ├── category.py            # 分类模型
│   ├── specs.py               # 商品规格相关模型
│   └── inventory.py           # 库存模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   ├── product.py             # 商品模式
│   ├── category.py            # 分类模式
│   ├── specs.py               # 规格模式
│   └── inventory.py           # 库存模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   ├── product.py             # 商品仓库
│   ├── category.py            # 分类仓库
│   ├── specs.py               # 规格仓库
│   └── inventory.py           # 库存仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── product.py             # 商品服务
│   ├── category.py            # 分类服务
│   ├── specs.py               # 规格服务
│   └── inventory.py           # 库存服务
├── routers/                    # API路由
│   ├── __init__.py
│   ├── product.py             # 商品路由
│   ├── category.py            # 分类路由
│   ├── specs.py               # 规格路由
│   └── inventory.py           # 库存路由
├── events/                     # 事件处理器
│   ├── __init__.py
│   ├── product_events.py      # 商品事件
│   ├── category_events.py     # 分类事件
│   └── inventory_events.py    # 库存事件
└── utils/                      # 工具函数
    ├── README.md
    ├── __init__.py
    ├── inventory_calculator.py # 库存计算器
    ├── price_calculator.py     # 价格计算器
    ├── product_validator.py    # 商品验证器
    └── sku_generator.py        # SKU生成器
```

## 核心功能

### 1. 商品管理 (Product)

-   商品的创建、查询、更新、删除
-   商品状态管理（草稿、上架、下架、停产）
-   商品分类关联
-   商品属性和元数据管理
-   SEO 信息管理
-   商品统计（浏览量、销量、评分）

### 2. 分类管理 (Category)

-   分类的创建、查询、更新、删除
-   层级分类结构支持
-   分类树形结构查询
-   分类状态管理
-   SEO 信息管理

### 3. 商品规格 (Specs)

-   支持商品的多规格（如颜色、尺寸等）
-   规格-规格值-规格组合三层建模
-   规格组合独立的价格和库存
-   规格模板与批量 SKU 生成
-   规格属性管理
-   默认规格组合设置

### 4. 库存管理 (Inventory)

-   库存记录的创建和管理
-   库存调整（增加/减少）
-   库存预留和释放
-   批次和过期日期管理
-   仓库位置管理
-   低库存和过期库存监控

## API 端点

### 商品 API

-   `GET /products/` - 获取商品列表
-   `GET /products/{id}` - 获取商品详情
-   `GET /products/featured` - 获取推荐商品
-   `GET /products/category/{category_id}` - 获取分类商品
-   `POST /products/admin/` - 创建商品（管理端）
-   `PUT /products/admin/{id}` - 更新商品（管理端）
-   `DELETE /products/admin/{id}` - 删除商品（管理端）

### 分类 API

-   `GET /categories/` - 获取分类列表
-   `GET /categories/{id}` - 获取分类详情
-   `GET /categories/tree` - 获取分类树
-   `GET /categories/featured` - 获取推荐分类
-   `POST /categories/admin/` - 创建分类（管理端）
-   `PUT /categories/admin/{id}` - 更新分类（管理端）

### 规格 API

-   `GET /specs/product/{product_id}/specs` - 获取商品规格及规格值
-   `POST /specs/product/{product_id}/specs` - 新增商品规格
-   `POST /specs/product/{product_id}/specs/{spec_id}/options` - 新增商品规格值
-   `POST /specs/product/{product_id}/combinations/generate` - 生成商品所有规格组合
-   `GET /specs/product/{product_id}/combinations` - 获取商品所有规格组合
-   `PUT /specs/product/{product_id}/combinations/{combination_id}` - 更新规格组合（价格、库
    存等）

### 库存 API

-   `GET /inventory/admin/` - 获取库存列表（管理端）
-   `GET /inventory/admin/{id}` - 获取库存详情（管理端）
-   `POST /inventory/admin/` - 创建库存记录（管理端）
-   `POST /inventory/admin/{id}/adjust` - 调整库存（管理端）
-   `POST /inventory/admin/{id}/reserve` - 预留库存（管理端）

## 使用示例

### 1. 创建商品

```python
from svc.apps.products.schemas.product import ProductCreate
from svc.apps.products.services.product import ProductService

# 创建商品数据
product_data = ProductCreate(
    name="iPhone 15 Pro",
    description="苹果最新旗舰手机",
    sku="IPHONE15PRO001",
    price=7999.00,
    category_id=1,
    is_featured=True
)

# 创建商品
product_service = ProductService()
result = await product_service.create_product(product_data)
```

### 2. 配置商品规格

```python
from svc.apps.products.schemas.specs import SpecCreate, SpecOptionCreate
from svc.apps.products.services.specs import SpecsService

# 新增规格
spec_data = SpecCreate(name="颜色")
spec_service = SpecsService()
spec_result = await spec_service.create_spec(product_id=1, spec=spec_data)

# 新增规格值
option_data = SpecOptionCreate(spec_id=spec_result.id, value="钛原色")
option_result = await spec_service.create_spec_option(product_id=1, option=option_data)
```

### 3. 生成商品所有规格组合

```python
from svc.apps.products.services.specs import SpecsService

# 生成所有规格组合（如颜色*容量*版本的笛卡尔积）
combination_result = await spec_service.generate_combinations(product_id=1)
```

### 4. 查询商品规格组合

```python
# 查询所有可售单元（规格组合）
combinations = await spec_service.get_combinations(product_id=1)
```

## 事件系统

模块集成了事件系统，支持以下事件：

### 商品事件

-   `products:product:created` - 商品创建
-   `products:product:updated` - 商品更新
-   `products:product:deleted` - 商品删除
-   `products:product:status_changed` - 商品状态变更

### 分类事件

-   `products:category:created` - 分类创建
-   `products:category:updated` - 分类更新
-   `products:category:deleted` - 分类删除

### 规格事件

-   `products:spec:created` - 规格创建
-   `products:spec:option:created` - 规格值创建
-   `products:spec:combination:generated` - 规格组合生成
-   `products:spec:combination:updated` - 规格组合更新

### 库存事件

-   `products:inventory:created` - 库存创建
-   `products:inventory:adjusted` - 库存调整
-   `products:inventory:reserved` - 库存预留
-   `products:inventory:released` - 库存释放

## 缓存策略

-   商品信息缓存：1 小时
-   分类信息缓存：1 小时
-   规格信息缓存：1 小时
-   库存信息缓存：30 分钟（较短，保证实时性）

## 权限控制

模块集成了基于角色的权限控制：

-   `product:read` - 商品查看权限
-   `product:create` - 商品创建权限
-   `product:update` - 商品更新权限
-   `product:delete` - 商品删除权限
-   `category:*` - 分类相关权限
-   `spec:*` - 规格相关权限
-   `inventory:*` - 库存相关权限

## 数据库迁移

创建数据库表的迁移脚本：

```sql
-- 创建规格表
CREATE TABLE specs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '规格名称'
);

-- 创建规格值表
CREATE TABLE spec_options (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    spec_id BIGINT NOT NULL,
    value VARCHAR(100) NOT NULL COMMENT '规格值',
    FOREIGN KEY (spec_id) REFERENCES specs(id)
);

-- 商品与规格关联表
CREATE TABLE product_specs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    spec_id BIGINT NOT NULL,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (spec_id) REFERENCES specs(id)
);

-- 商品与规格值关联表
CREATE TABLE product_spec_options (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_spec_id BIGINT NOT NULL,
    spec_option_id BIGINT NOT NULL,
    FOREIGN KEY (product_spec_id) REFERENCES product_specs(id),
    FOREIGN KEY (spec_option_id) REFERENCES spec_options(id)
);

-- 商品规格组合表（可售单元）
CREATE TABLE product_spec_combinations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    sku VARCHAR(100) NOT NULL UNIQUE COMMENT 'SKU',
    price FLOAT COMMENT '价格',
    stock_quantity INT DEFAULT 0 COMMENT '库存数量',
    spec_option_ids JSON COMMENT '该组合包含的所有规格值ID',
    FOREIGN KEY (product_id) REFERENCES products(id)
);
```

## 注意事项

1. **SKU 唯一性**：确保商品和规格组合的 SKU 在系统中唯一
2. **库存一致性**：库存操作需要考虑并发安全
3. **分类层级**：分类删除时需要处理子分类和商品
4. **缓存更新**：数据变更时及时更新缓存
5. **事件处理**：确保事件处理器的异常不影响主业务流程
