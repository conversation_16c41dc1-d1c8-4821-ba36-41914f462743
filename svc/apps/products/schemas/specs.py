from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional
from svc.core.schemas.base import PaginatedResponse
from svc.apps.albums.schemas.album import AlbumResponse
from svc.core.models.base import CamelCaseModel

class SpecBase(CamelCaseModel):
    name: str = Field(..., max_length=100, description="规格名称")

class SpecOptionBase(CamelCaseModel):
    value: str = Field(..., max_length=100, description="规格选项值")
    spec_id: int = Field(..., description="所属规格ID")

class SpecOptionCreate(SpecOptionBase):
    pass

class SpecOptionUpdate(CamelCaseModel):
    value: Optional[str] = Field(None, max_length=100, description="规格选项值")

class SpecOptionResponse(SpecOptionBase):
    id: int = Field(..., description="规格选项ID")
    model_config = ConfigDict(
        from_attributes=True,
    )

class SpecCreate(SpecBase):
    options: List[str] = Field(..., description="规格选项值列表")

class SpecUpdate(CamelCaseModel):
    name: Optional[str] = Field(None, max_length=100, description="规格名称")

class SpecResponse(SpecBase):
    id: int = Field(..., description="规格ID")
    options: List[SpecOptionResponse] = Field(..., description="规格选项列表")
    model_config = ConfigDict(
        from_attributes=True,
    )

class ProductSpecBase(CamelCaseModel):
    product_id: int = Field(..., description="商品ID")
    spec_id: int = Field(..., description="规格ID")

class ProductSpecCreate(ProductSpecBase):
    pass

class ProductSpecResponse(ProductSpecBase):
    id: int
    model_config = ConfigDict(
        from_attributes=True,
    )

class ProductSpecOptionBase(CamelCaseModel):
    product_spec_id: int = Field(..., description="商品规格关联ID")
    spec_option_id: int = Field(..., description="规格值ID")

class ProductSpecOptionCreate(ProductSpecOptionBase):
    pass

class ProductSpecOptionResponse(ProductSpecOptionBase):
    id: int
    model_config = ConfigDict(
        from_attributes=True,
    )

class ProductSpecCombinationBase(CamelCaseModel):
    product_id: int = Field(..., description="商品ID")
    sku: str = Field(..., description="SKU")
    price: Optional[float] = Field(None, description="价格")
    stock_quantity: int = Field(0, description="库存数量")
    spec_option_ids: List[int] = Field(..., description="该组合包含的所有规格值ID")
    album_id: Optional[int] = Field(default=None, description="关联图册ID")

class ProductSpecCombinationCreate(ProductSpecCombinationBase):
    pass

class ProductSpecCombinationUpdate(CamelCaseModel):
    price: Optional[float] = Field(None, description="价格")
    stock_quantity: Optional[int] = Field(None, description="库存数量")

class ProductSpecCombinationResponse(ProductSpecCombinationBase):
    id: int
    album: Optional[AlbumResponse] = Field(default=None, description="关联图册")
    model_config = ConfigDict(
        from_attributes=True,
    )

class SpecListResponse(PaginatedResponse[SpecResponse]):
    """规格分页列表响应模型"""
    pass

class SpecOptionListResponse(PaginatedResponse[SpecOptionResponse]):
    """规格值分页列表响应模型"""
    pass

class ProductSpecCombinationListResponse(PaginatedResponse[ProductSpecCombinationResponse]):
    """规格组合分页列表响应模型"""
    pass