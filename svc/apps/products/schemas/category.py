from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel

class CategoryBase(CamelCaseModel):
    """分类基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "智能手机",
                "description": "各品牌智能手机产品",
                "slug": "smartphones",
                "parent_id": None,
                "level": 0,
                "sort_order": 1,
                "is_featured": True,
                "image_url": "https://example.com/category/smartphones.jpg",
                "icon": "smartphone",
                "attributes": {"display_type": "grid"},
                "seo_title": "智能手机 - 在线商城",
                "seo_description": "购买最新智能手机，品牌齐全",
                "seo_keywords": "智能手机,手机,移动设备"
            }
        }
    )
    
    name: str = Field(..., max_length=100, description="分类名称")
    description: Optional[str] = Field(None, max_length=500, description="分类描述")
    slug: str = Field(..., description="分类别名")
    parent_id: Optional[int] = Field(None, description="父分类ID")
    level: int = Field(default=0, description="分类层级")
    sort_order: int = Field(default=0, description="排序顺序")
    is_featured: bool = Field(default=False, description="是否为推荐分类")
    image_url: Optional[str] = Field(default=None, description="分类图片URL")
    icon: Optional[str] = Field(default=None, description="分类图标")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="分类属性")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")

class CategoryCreate(CategoryBase):
    """分类创建模型"""
    pass

class CategoryUpdate(CamelCaseModel):
    """分类更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "智能手机与配件",
                "status": "active",
                "sort_order": 2
            }
        }
    )
    
    name: Optional[str] = Field(None, max_length=100, description="分类名称")
    description: Optional[str] = Field(None, max_length=500, description="分类描述")
    slug: Optional[str] = Field(default=None, description="分类别名")
    parent_id: Optional[int] = Field(None, description="父分类ID")
    level: Optional[int] = Field(default=None, description="分类层级")
    sort_order: Optional[int] = Field(default=None, description="排序顺序")
    status: Optional[str] = Field(default=None, description="分类状态")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐分类")
    image_url: Optional[str] = Field(default=None, description="分类图片URL")
    icon: Optional[str] = Field(default=None, description="分类图标")
    attributes: Optional[Dict[str, Any]] = Field(default=None, description="分类属性")
    seo_title: Optional[str] = Field(default=None, description="SEO标题")
    seo_description: Optional[str] = Field(default=None, description="SEO描述")
    seo_keywords: Optional[str] = Field(default=None, description="SEO关键词")

class CategoryResponse(CategoryBase):
    """分类响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "status": "active",
                "product_count": 150,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(..., description="分类ID")
    status: str = Field(description="分类状态")
    product_count: int = Field(description="商品数量")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class CategoryTreeResponse(CategoryResponse):
    """分类树响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True
    )
    
    children: List['CategoryTreeResponse'] = Field(default_factory=list, description="子分类列表")

class CategoryListResponse(PaginatedResponse[CategoryResponse]):
    """分类列表响应模型"""
    pass

class GetCategoriesParams(BaseModel):
    """获取分类列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="分类状态")
    parent_id: Optional[int] = Field(default=None, description="父分类ID")
    level: Optional[int] = Field(default=None, description="分类层级")
    is_featured: Optional[bool] = Field(default=None, description="是否为推荐分类")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_desc: Optional[bool] = Field(default=False, description="是否降序")

class UserCategoryResponse(BaseModel):
    """用户端分类响应模型，仅用于用户端接口，减少冗余字段传输"""
    id: int = Field(..., description="分类ID")
    name: str = Field(..., description="分类名称")
    description: Optional[str] = Field(default=None, description="分类描述")
    slug: str = Field(..., description="分类别名")
    image_url: Optional[str] = Field(default=None, description="分类图片URL")
    icon: Optional[str] = Field(default=None, description="分类图标")
    is_featured: bool = Field(default=False, description="是否为推荐分类")
    attributes: Dict[str, Any] = Field(default_factory=dict, description="分类属性")

class UserCategoryListResponse(PaginatedResponse[UserCategoryResponse]):
    """用户端分类分页响应模型"""
    pass
