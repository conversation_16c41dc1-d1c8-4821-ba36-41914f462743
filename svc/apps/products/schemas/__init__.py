"""
商品管理模块请求/响应模式

该模块包含商品管理相关的Pydantic模式定义：
- Product相关模式
- Category相关模式
- ProductVariant相关模式
- Inventory相关模式
"""

from .product import *
from .category import *
from .inventory import *
from .specs import *

__all__ = [
    # Product schemas
    "ProductBase",
    "ProductCreate", 
    "ProductUpdate",
    "ProductResponse",
    "ProductListResponse",
    "GetProductsParams",
    
    # Category schemas
    "CategoryBase",
    "CategoryCreate",
    "CategoryUpdate", 
    "CategoryResponse",
    "CategoryListResponse",
    "GetCategoriesParams",
    
    # Inventory schemas
    "InventoryBase",
    "InventoryCreate",
    "InventoryUpdate",
    "InventoryResponse",
    "InventoryListResponse",
    "GetInventoriesParams",

    # Specs schemas
    "SpecBase",
    "SpecCreate",
    "SpecUpdate",
    "SpecResponse",
    "SpecOptionBase",
    "SpecOptionCreate",
    "SpecOptionUpdate",
    "SpecOptionResponse",
    "ProductSpecCombinationBase",
]
