from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel

class InventoryBase(CamelCaseModel):
    """库存基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "product_id": 1,
                "variant_id": 1,
                "quantity": 100,
                "reserved_quantity": 10,
                "available_quantity": 90,
                "status": "available",
                "warehouse_location": "仓库A",
                "shelf_location": "A-01-001",
                "batch_number": "BATCH20240324001",
                "lot_number": "LOT001",
                "expiry_date": "2025-12-31T23:59:59",
                "unit_cost": 500000,
                "total_cost": 50000000,
                "supplier_id": 1,
                "purchase_order_id": 1001,
                "notes": "新到货商品"
            }
        }
    )
    
    product_id: int = Field(..., description="商品ID")
    variant_id: Optional[int] = Field(default=None, description="变体ID")
    quantity: int = Field(..., description="库存数量")
    reserved_quantity: int = Field(default=0, description="预留数量")
    available_quantity: int = Field(default=0, description="可用数量")
    status: str = Field(default="available", description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    shelf_location: Optional[str] = Field(default=None, description="货架位置")
    batch_number: Optional[str] = Field(default=None, description="批次号")
    lot_number: Optional[str] = Field(default=None, description="批号")
    expiry_date: Optional[datetime] = Field(default=None, description="过期日期")
    unit_cost: Optional[int] = Field(default=None, description="单位成本(分)")
    total_cost: Optional[int] = Field(default=None, description="总成本(分)")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")
    purchase_order_id: Optional[int] = Field(default=None, description="采购订单ID")
    notes: Optional[str] = Field(default=None, description="备注")

class InventoryCreate(InventoryBase):
    """库存创建模型"""
    pass

class InventoryUpdate(CamelCaseModel):
    """库存更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "quantity": 150,
                "available_quantity": 140,
                "status": "available",
                "warehouse_location": "仓库B",
                "notes": "库存调整"
            }
        }
    )
    
    quantity: Optional[int] = Field(default=None, description="库存数量")
    reserved_quantity: Optional[int] = Field(default=None, description="预留数量")
    available_quantity: Optional[int] = Field(default=None, description="可用数量")
    status: Optional[str] = Field(default=None, description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    shelf_location: Optional[str] = Field(default=None, description="货架位置")
    batch_number: Optional[str] = Field(default=None, description="批次号")
    lot_number: Optional[str] = Field(default=None, description="批号")
    expiry_date: Optional[datetime] = Field(default=None, description="过期日期")
    unit_cost: Optional[int] = Field(default=None, description="单位成本(分)")
    total_cost: Optional[int] = Field(default=None, description="总成本(分)")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")
    purchase_order_id: Optional[int] = Field(default=None, description="采购订单ID")
    notes: Optional[str] = Field(default=None, description="备注")

class InventoryResponse(InventoryBase):
    """库存响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="库存ID")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class InventoryListResponse(PaginatedResponse[InventoryResponse]):
    """库存列表响应模型"""
    pass

class GetInventoriesParams(BaseModel):
    """获取库存列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    product_id: Optional[int] = Field(default=None, description="商品ID")
    variant_id: Optional[int] = Field(default=None, description="变体ID")
    status: Optional[str] = Field(default=None, description="库存状态")
    warehouse_location: Optional[str] = Field(default=None, description="仓库位置")
    supplier_id: Optional[int] = Field(default=None, description="供应商ID")
    low_stock_only: Optional[bool] = Field(default=None, description="仅显示低库存")
    expired_only: Optional[bool] = Field(default=None, description="仅显示过期库存")
    order_by: Optional[str] = Field(default="created_at", description="排序字段")
    order_desc: Optional[bool] = Field(default=True, description="是否降序")

class InventoryAdjustmentRequest(BaseModel):
    """库存调整请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "adjustment_type": "increase",
                "quantity": 50,
                "reason": "新进货",
                "notes": "供应商A发货"
            }
        }
    )
    
    adjustment_type: str = Field(..., description="调整类型: increase/decrease")
    quantity: int = Field(..., description="调整数量")
    reason: str = Field(..., description="调整原因")
    notes: Optional[str] = Field(default=None, description="备注")

class InventoryReservationRequest(BaseModel):
    """库存预留请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "quantity": 5,
                "order_id": 1001,
                "notes": "订单预留"
            }
        }
    )
    
    quantity: int = Field(..., description="预留数量")
    order_id: Optional[int] = Field(default=None, description="订单ID")
    notes: Optional[str] = Field(default=None, description="备注")
