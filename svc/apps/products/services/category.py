from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.products.models.category import Category, CategoryStatus
from svc.apps.products.repositories.category import CategoryRepository
from svc.apps.products.schemas.category import (CategoryCreate,
                                                CategoryListResponse,
                                                CategoryResponse,
                                                CategoryTreeResponse,
                                                CategoryUpdate,
                                                GetCategoriesParams,
                                                UserCategoryResponse,
                                                UserCategoryListResponse)
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result

# 缓存配置
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）

class CategoryService(BaseService[Category, Result[CategoryResponse]]):
    """分类服务类，提供分类的创建、查询和管理功能"""
    
    # 设置资源类型名称
    resource_type = "category"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None, 
        category_repo: Optional[CategoryRepository] = None
    ):
        """初始化分类服务"""
        super().__init__(redis)
        self.category_repo = category_repo
    
    async def get_resource_by_id(self, category_id: int) -> Optional[Category]:
        """获取指定ID的分类资源"""
        return await self.category_repo.get_by_id(category_id)
    
    async def get_category(self, category_id: int) -> Result[CategoryResponse]:
        """获取分类信息"""
        try:
            self.logger.info(f"获取分类: id={category_id}")
            
            # 先尝试从缓存获取
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(category_id)
                    cached_category = await self.get_cached_resource(
                        cache_key,
                        lambda data: CategoryResponse.model_validate(data)
                    )
                    if cached_category:
                        self.logger.debug(f"从缓存获取到分类: id={category_id}")
                        return self.create_success_result(cached_category)
                except Exception as e:
                    self.logger.warning(f"从缓存获取分类失败: id={category_id}, 错误={str(e)}")
            
            # 查询数据库
            category = await self.get_resource_by_id(category_id)
            if not category:
                self.logger.warning(f"分类不存在: id={category_id}")
                return self.resource_not_found_result(category_id)
            
            # 构建分类响应
            category_response = CategoryResponse.model_validate(category.to_dict())
            
            # 缓存分类
            await self._cache_category(category_id, category_response)
            
            return self.create_success_result(category_response)
        except Exception as e:
            self.logger.error(f"获取分类失败: id={category_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.NOT_FOUND,
                error_message=f"获取分类失败: {str(e)}"
            )
    
    async def get_categories(self, params: GetCategoriesParams, user_mode: bool = False):
        try:
            page_num = params.page_num
            page_size = params.page_size
            skip = (page_num - 1) * page_size
            self.logger.info(f"获取分类列表: page={page_num}, size={page_size}")
            categories, total = await self.category_repo.get_categories(
                skip=skip,
                limit=page_size,
                status=params.status,
                parent_id=params.parent_id,
                level=params.level,
                is_featured=params.is_featured,
                search_term=params.search_term,
                order_by=params.order_by,
                order_desc=params.order_desc
            )
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            if user_mode:
                user_categories = []
                for category in categories:
                    user_categories.append(UserCategoryResponse(
                        id=category.id,
                        name=category.name,
                        description=category.description,
                        slug=category.slug,
                        image_url=getattr(category, 'image_url', None),
                        icon=getattr(category, 'icon', None),
                        is_featured=category.is_featured,
                        attributes=getattr(category, 'attributes', {})
                    ))
                return self.create_success_result(
                    UserCategoryListResponse(
                        items=user_categories,
                        total=total,
                        page_num=page_num,
                        page_size=page_size,
                        page_count=total_pages
                    )
                )
            category_responses = []
            for category in categories:
                category_response = CategoryResponse.model_validate(category.to_dict())
                category_responses.append(category_response)
            paginated_response = CategoryListResponse(
                items=category_responses,
                total=total,
                page_num=page_num,
                page_size=page_size,
                page_count=total_pages
            )
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取分类列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取分类列表失败: {str(e)}"
            )
    
    async def create_category(self, params: CategoryCreate) -> Result[CategoryResponse]:
        """创建分类"""
        try:
            self.logger.info(f"创建分类: name={params.name}, slug={params.slug}")
            
            # 检查slug是否已存在
            existing_category = await self.category_repo.get_by_slug(params.slug)
            if existing_category:
                self.logger.warning(f"分类别名已存在: {params.slug}")
                return self.create_error_result(
                    error_code=ErrorCode.ALREADY_EXISTS,
                    error_message=f"分类别名 {params.slug} 已被使用"
                )
            
            # 创建分类
            category = await self.category_repo.create(**params.model_dump())
            
            # 构建分类响应
            category_response = CategoryResponse.model_validate(category.to_dict())
            
            # 缓存分类
            await self._cache_category(category.id, category_response)
            
            # 触发分类创建事件
            event_data = category_response.model_dump()
            dispatch("products:category:created", payload=event_data)
            
            self.logger.info(f"分类创建成功: id={category.id}, name={category.name}")
            return self.create_success_result(category_response)
        except Exception as e:
            self.logger.error(f"创建分类失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建分类失败: {str(e)}"
            )
    
    async def update_category(self, category_id: int, params: CategoryUpdate) -> Result[CategoryResponse]:
        """更新分类"""
        try:
            self.logger.info(f"更新分类: id={category_id}")
            
            # 获取分类
            category = await self.get_resource_by_id(category_id)
            if not category:
                self.logger.warning(f"分类不存在: {category_id}")
                return self.resource_not_found_result(category_id)
            
            # 如果更新slug，检查是否已存在
            if params.slug is not None and params.slug != category.slug:
                existing_category = await self.category_repo.get_by_slug(params.slug)
                if existing_category and existing_category.id != category.id:
                    self.logger.warning(f"分类别名已存在: {params.slug}")
                    return self.create_error_result(
                        error_code=ErrorCode.ALREADY_EXISTS,
                        error_message=f"分类别名 {params.slug} 已被使用"
                    )
            
            # 更新分类
            update_data = params.model_dump(exclude_unset=True)
            category = await self.category_repo.update(category, data=update_data)
            
            # 构建分类响应
            category_response = CategoryResponse.model_validate(category.to_dict())
            
            # 缓存分类
            await self._cache_category(category.id, category_response)
            
            # 触发分类更新事件
            event_data = category_response.model_dump()
            event_data["updated_fields"] = list(update_data.keys())
            dispatch("products:category:updated", payload=event_data)
            
            self.logger.info(f"分类更新成功: id={category.id}, name={category.name}")
            return self.create_success_result(category_response)
        except Exception as e:
            self.logger.error(f"更新分类失败: id={category_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.INTERNAL_ERROR,
                error_message=f"更新分类失败: {str(e)}"
            )
    
    async def get_category_tree(self, parent_id: Optional[int] = None) -> Result[List[CategoryTreeResponse]]:
        """获取分类树结构"""
        try:
            self.logger.info(f"获取分类树: parent_id={parent_id}")
            
            categories = await self.category_repo.get_category_tree(parent_id)
            
            # 构建树形响应
            tree_responses = []
            for category in categories:
                tree_response = CategoryTreeResponse.model_validate(category.to_dict())
                # 递归获取子分类
                children = await self.get_category_tree(category.id)
                if children.is_success:
                    tree_response.children = children.data
                tree_responses.append(tree_response)
            
            return self.create_success_result(tree_responses)
        except Exception as e:
            self.logger.error(f"获取分类树失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取分类树失败: {str(e)}"
            )
    
    async def _cache_category(self, category_id: int, response: CategoryResponse) -> None:
        """缓存分类信息"""
        if not self.redis:
            self.logger.debug(f"Redis未配置，跳过缓存分类: category_id={category_id}")
            return
            
        try:
            # 缓存分类数据
            key = self._get_resource_cache_key(category_id)
            await self.cache_resource(key, response, CACHE_TTL)
            self.logger.debug(f"分类缓存成功: key={key}")
        except Exception as e:
            # 缓存失败不应影响主要业务逻辑
            self.logger.warning(f"缓存分类失败: category_id={category_id}, 错误={str(e)}")
