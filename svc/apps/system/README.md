# 系统管理模块

基于产品模块模板创建的系统管理模块，提供系统审计日志、健康检查、系统信息等功能。

## 模块结构

```
svc/apps/system/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   └── audit_log.py           # 审计日志模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   └── audit_log.py           # 审计日志模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   └── audit_log.py           # 审计日志仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── audit_log.py           # 审计日志服务
│   └── monitor.py             # 系统监控服务
├── routers/                    # API路由
│   ├── __init__.py
│   ├── audit.py               # 审计日志路由
│   ├── health.py              # 健康检查路由
│   └── info.py                # 系统信息路由
├── events/                     # 事件处理器
│   ├── __init__.py
│   ├── audit_log_handlers.py  # 审计日志事件
│   └── system_handlers.py     # 系统事件
└── utils/                      # 工具函数
    └── __init__.py
```

## 核心功能

### 1. 审计日志管理 (Audit Log)

-   审计日志的记录、查询、导出
-   日志类型（登录、操作、异常等）
-   日志检索与过滤
-   日志归档与清理

### 2. 健康检查 (Health Check)

-   系统运行状态检测
-   依赖服务健康检查（数据库、缓存等）
-   监控指标采集

### 3. 系统信息 (System Info)

-   系统版本、配置信息查询
-   运行环境信息展示

## API 端点

### 审计日志 API

-   `GET /audit/logs/` - 获取审计日志列表
-   `GET /audit/logs/{id}` - 获取审计日志详情
-   `POST /audit/logs/export` - 导出审计日志

### 健康检查 API

-   `GET /health/` - 获取系统健康状态

### 系统信息 API

-   `GET /info/` - 获取系统信息

## 事件系统

模块集成了事件系统，支持以下事件：

### 审计日志事件

-   `system:audit_log:created` - 审计日志创建
-   `system:audit_log:archived` - 审计日志归档
-   `system:audit_log:deleted` - 审计日志删除

### 系统事件

-   `system:status:changed` - 系统状态变更

## 缓存策略

-   审计日志缓存：30 分钟
-   系统信息缓存：1 小时

## 权限控制

模块集成了基于角色的权限控制：

-   `audit_log:read` - 审计日志查看权限
-   `audit_log:export` - 审计日志导出权限
-   `system:info:read` - 系统信息查看权限

## 数据库迁移

```sql
-- 创建审计日志表
CREATE TABLE audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(100),
    target_id BIGINT,
    status VARCHAR(20) DEFAULT 'success',
    ip_address VARCHAR(50),
    user_agent VARCHAR(255),
    detail TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 注意事项

1. 审计日志量大时需定期归档和清理，避免影响性能。
2. 健康检查接口应简洁高效，避免阻塞主流程。
3. 事件处理器异常不影响主业务流程。
4. 权限校验和数据一致性需重点关注。
