"""
Applications package.

This file can be used to import all application models to ensure they are discoverable
by tools like Alembic for database migrations.
"""

# Import models from each application to make them discoverable
# It's a common practice to ensure that SQLAlchemy metadata is populated.

# Example for auth (if not already handled elsewhere):
# from .auth import models as auth_models

# Import for the new shops module
from .shops import models as shops_models

# You might want to list them or just import them for side effects (metadata registration)
__all__ = [
    # "auth_models",
    "shops_models",
]