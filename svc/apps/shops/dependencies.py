"""
门店管理模块依赖注入

该模块提供门店管理相关的依赖注入函数，
用于在FastAPI路由中注入服务和仓库实例。
"""
from typing import Optional

from fastapi import Depends
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.shops.repositories.shop import ShopRepository
from svc.apps.shops.services.shop import ShopService
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route # 使用核心的 get_db
from svc.core.dependencies.auth import configure_auth_dependencies
from svc.apps.albums.services.album import AlbumService
from svc.apps.albums.dependencies import get_album_service

# === 仓库依赖注入 ===

async def get_shop_repository(
    db: AsyncSession = Depends(get_session_for_route()) # 使用核心的 get_db
) -> ShopRepository:
    """获取门店仓库实例"""
    return ShopRepository(db)

# === 服务依赖注入 ===

async def get_shop_service(
    db: AsyncSession = Depends(get_session_for_route()), # 确保服务层也能拿到db session
    redis: Redis = Depends(get_redis), # 确保服务层也能拿到redis
    shop_repo: ShopRepository = Depends(get_shop_repository),
    album_service: AlbumService = Depends(get_album_service)
) -> ShopService:
    """获取门店服务实例

    Args:
        db: 数据库会话，通过 get_db 注入
        redis: Redis客户端，可选
        shop_repo: 门店仓库实例
        album_service: 图册服务实例

    Returns:
        ShopService: 门店服务实例
    """
    service = ShopService(redis=redis, shop_repo=shop_repo, album_service=album_service)
    # 确保服务内的仓库与当前请求的db会话一致
    # ShopService 内部的 _ensure_repo 会处理这个问题
    # 或者在这里显式传递 db 给 ShopService 的构造函数（如果它接受的话）
    # 例如: service = ShopService(db=db, redis=redis, shop_repo=shop_repo)
    # 但当前 ShopService 的 __init__ 不直接接受 db，它通过 shop_repo 间接使用
    # 重要的是 shop_repo 是用正确的 db session 初始化的
    return service

# === 组合服务依赖注入（为后续扩展预留） ===

class ShopManagementServices:
    """门店管理服务集合类
    方便在需要多个服务协作的场景中使用。
    """
    def __init__(self, shop_service: ShopService):
        self.shop = shop_service

async def get_shop_management_services(
    shop_service: ShopService = Depends(get_shop_service)
) -> ShopManagementServices:
    """获取门店管理服务集合
    Args:
        shop_service: 门店服务实例
    Returns:
        ShopManagementServices: 门店管理服务集合实例
    """
    return ShopManagementServices(shop_service=shop_service)

# === 模块设置函数 ===

def setup_shops_dependencies():
    """
    设置门店管理依赖项
    注册门店管理相关的资源类型并配置全局认证依赖项。
    应在应用启动时调用此函数。
    """
    try:
        from svc.apps.shops.models import Shop
        configure_auth_dependencies(
            resources={
                "shop": Shop
            }
        )
    except ImportError:
        import logging
        logger = logging.getLogger(__name__)
        logger.warning("未能注册门店管理模块资源类型，请检查模型是否正确定义")

# 在模块导入时自动调用设置函数
try:
    setup_shops_dependencies()
except Exception as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.error(f"设置门店管理依赖项失败: {str(e)}")