# 店铺管理模块

基于商品管理模块模板创建的店铺管理模块，提供完整的店铺（Shop）管理功能。

## 模块结构

```
svc/apps/shops/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   └── shop.py                # 店铺模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   └── shop.py                # 店铺模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   └── shop.py                # 店铺仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   └── shop.py                # 店铺服务
├── routers/                    # API路由
│   ├── __init__.py
│   └── shop.py                # 店铺路由（管理端+用户端）
├── events/                     # 事件处理器
│   ├── __init__.py
│   └── shop_events.py         # 店铺事件
└── utils/                      # 工具函数
    ├── __init__.py
    └── shop_utils.py          # 店铺相关工具
```

## 核心功能

-   店铺的创建、查询、更新、删除
-   店铺状态管理（营业中、休息中、已关闭、已删除）
-   店铺地址、联系方式、营业时间等信息管理
-   店铺元数据管理
-   店铺统计（如浏览量、评分等）

## API 端点

### 管理端接口（/shops/admin/）

-   `GET /shops/admin/` - 获取店铺列表（管理端，支持高级筛选、分页）
-   `GET /shops/admin/{id}` - 获取店铺详情（管理端）
-   `POST /shops/admin/` - 创建店铺（管理端）
-   `PUT /shops/admin/{id}` - 更新店铺（管理端）
-   `DELETE /shops/admin/{id}` - 删除店铺（管理端）

> 管理端接口需登录并具备相应权限（如
> `shop:read`、`shop:create`、`shop:update`、`shop:delete`）

### 用户端接口（/shops/）

-   `GET /shops/` - 获取公开店铺列表
-   `GET /shops/{id}` - 获取店铺详情

> 用户端接口为公开接口或基础校验

## 权限控制

-   管理端接口需登录并具备相应权限
-   用户端接口可公开访问

## 事件系统

-   `shops:shop:created` - 店铺创建
-   `shops:shop:updated` - 店铺更新
-   `shops:shop:deleted` - 店铺删除
-   `shops:shop:status_changed` - 店铺状态变更

## 缓存策略

-   店铺信息缓存：1 小时（可配置）

## 数据库迁移

```sql
-- 创建店铺表
CREATE TABLE shops (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    zip_code VARCHAR(20),
    country VARCHAR(100),
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    opening_hours VARCHAR(255),
    status VARCHAR(20) DEFAULT 'open',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    meta_data JSON,
    view_count INT DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INT DEFAULT 0,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 注意事项

1. 管理端和用户端接口严格分层，权限和数据范围不同。
2. 数据变更时及时更新或清除缓存。
3. 事件处理器异常不影响主业务流程。
4. 数据一致性和权限校验需重点关注。
