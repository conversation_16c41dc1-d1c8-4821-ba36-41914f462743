"""
门店数据访问层，仅负责门店模型的数据库访问。
"""
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, func, or_
from sqlalchemy.orm import selectinload

from svc.core.repositories import BaseRepository
from svc.apps.shops.models.shop import Shop, ShopStatus
from svc.apps.shops.schemas.shop import ShopCreate, ShopUpdate, GetShopsParams
from svc.apps.albums.models.album import Album

class ShopRepository(BaseRepository[Shop, ShopCreate, ShopUpdate]):
    """门店仓库：仅实现门店相关的数据访问方法。"""
    def __init__(self, db: AsyncSession):
        super().__init__(db, Shop)

    async def get_by_id(self, id: int) -> Optional[Shop]:
        """通过ID获取门店，并预加载关联的图册和封面图"""
        query = select(self.model).where(self.model.id == id).options(
            selectinload(self.model.album).selectinload(Album.cover_image)
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def get_by_name_and_city(self, name: str, city: str) -> Optional[Shop]:
        result = await self.db.execute(
            select(self.model).filter(self.model.name == name, self.model.city == city)
        )
        return result.scalars().first()

    async def create_with_album(self, shop_data: ShopCreate, album_id: int) -> Shop:
        """创建门店并关联图册ID"""
        new_shop = Shop(**shop_data.model_dump(), album_id=album_id)
        self.db.add(new_shop)
        await self.db.flush()
        await self.db.refresh(new_shop)
        return new_shop

    async def get_shops(self, params: GetShopsParams) -> Tuple[List[Shop], int]:
        query = select(self.model).options(
            selectinload(self.model.album).selectinload(Album.cover_image)
        )
        count_query = select(func.count()).select_from(self.model)
        conditions = []
        if params.status:
            conditions.append(self.model.status == params.status)
        if params.city:
            conditions.append(self.model.city.ilike(f"%{params.city}%"))
        if params.country:
            conditions.append(self.model.country == params.country)
        if params.is_franchise is not None:
            conditions.append(self.model.is_franchise == params.is_franchise)
        if params.search_term:
            search_conditions = [
                self.model.name.ilike(f"%{params.search_term}%"),
                self.model.address_line1.ilike(f"%{params.search_term}%"),
                self.model.description.ilike(f"%{params.search_term}%")
            ]
            conditions.append(or_(*search_conditions))
        if conditions:
            query = query.filter(and_(*conditions))
            count_query = count_query.filter(and_(*conditions))
        total_count_result = await self.db.execute(count_query)
        total_count = total_count_result.scalar_one_or_none() or 0
        order_column = getattr(self.model, "created_at", self.model.id)
        query = query.order_by(desc(order_column))
        query = query.offset((params.page_num - 1) * params.page_size).limit(params.page_size)
        result = await self.db.execute(query)
        items = result.scalars().all()
        return list(items), total_count

    async def update_shop_status(self, shop_id: int, status: ShopStatus) -> Optional[Shop]:
        shop = await self.get_by_id(shop_id)
        if shop:
            shop.status = status
            await self.db.commit()
            await self.db.refresh(shop)
        return shop