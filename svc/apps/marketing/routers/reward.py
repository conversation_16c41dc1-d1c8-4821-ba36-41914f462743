"""
奖励相关的API路由
"""
from fastapi import APIRouter, Depends, Query, Path
from typing import List, Optional, Dict, Any


from svc.core.services.result import Result
from svc.apps.auth.dependencies import get_current_active_user, has_permission,get_current_superuser
from svc.apps.auth.models.user import User
from svc.apps.marketing.schemas import RewardStatsResponse, RewardRecordListResponse, RewardStrategyListResponse, GetRewardRecordsParams
from svc.apps.marketing.dependencies import get_reward_strategy_service, get_reward_record_service
from svc.apps.marketing.services.reward import RewardStrategyService, RewardRecordService
from svc.core.exceptions import (
    handle_route_errors,
    REWARD_ERROR_MAPPING
)
# from svc.apps.marketing.schemas.reward import RewardRecordListResponse, RewardStrategyListResponse # 重复导入

router = APIRouter(
    tags=["奖励"]
)

# === 管理端路由 (Admin Routes) ===
# Paths prefixed with /admin within this router

@router.get("/admin/records/list", response_model=Result[RewardRecordListResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def admin_list_reward_records(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=1000, description="每页数量"),
    campaign_id: Optional[int] = Query(None, description="活动ID过滤"),
    user_id: Optional[int] = Query(None, description="用户ID过滤"),
    reward_type: Optional[str] = Query(None, description="奖励类型过滤"),
    is_issued: Optional[bool] = Query(None, description="是否已发放过滤"),
    strategy_id: Optional[int] = Query(None, description="奖励策略ID过滤"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward:read"))
) -> Result[RewardRecordListResponse]:
    """获取奖励记录列表 (管理端)"""
    params = GetRewardRecordsParams(
        page_num=page_num,
        page_size=page_size,
        campaign_id=campaign_id,
        user_id=user_id,
        reward_type=reward_type,
        is_issued=is_issued,
        strategy_id=strategy_id
    )
    result = await reward_record_service.get_reward_records(params=params)
    return result

@router.get("/admin/strategies/", response_model=Result[RewardStrategyListResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def admin_get_campaign_strategies(
    campaign_id: int = Query(..., description="活动ID"),
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(100, ge=1, le=100, description="每页记录数"),
    for_inviter: Optional[bool] = Query(None, description="筛选邀请人策略"),
    for_invitee: Optional[bool] = Query(None, description="筛选被邀请人策略"),
    reward_strategy_service: RewardStrategyService = Depends(get_reward_strategy_service),
    current_user: User = Depends(get_current_superuser),
    _: bool = Depends(lambda: has_permission("reward_strategy:read")) # Assuming permission exists
) -> Result[RewardStrategyListResponse]:
    """获取活动的奖励策略列表 (管理端)"""
    result = await reward_strategy_service.get_campaign_strategies(
        campaign_id=campaign_id,
        for_inviter=for_inviter,
        for_invitee=for_invitee,
        page_num=page_num,
        page_size=page_size
    )
    return result

# === 客户端路由 (Client Routes) ===
# Paths relative to /rewards

@router.get("/mine", response_model=Result[RewardRecordListResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_my_rewards(
    page_num: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页记录数"),
    campaign_id: Optional[int] = Query(None, description="按活动ID过滤"),
    is_issued: Optional[bool] = Query(None, description="按发放状态过滤"),
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[RewardRecordListResponse]:
    """获取当前用户的奖励列表 (客户端)"""
    result = await reward_record_service.get_user_rewards(
        user_id=current_user.id, 
        campaign_id=campaign_id,
        is_issued=is_issued,
        page_num=page_num,
        page_size=page_size
    )
    return result

@router.get("/stats", response_model=Result[RewardStatsResponse])
@handle_route_errors(REWARD_ERROR_MAPPING)
async def get_my_reward_stats(
    campaign_id: Optional[int] = None,
    reward_record_service: RewardRecordService = Depends(get_reward_record_service),
    current_user: User = Depends(get_current_active_user)
) -> Result[RewardStatsResponse]:
    """获取当前用户的奖励统计 (客户端)"""
    stats = await reward_record_service.get_reward_stats(current_user.id, campaign_id)
    return stats

