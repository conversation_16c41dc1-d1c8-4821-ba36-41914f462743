"""
营销模块
包含营销活动、邀请和奖励功能

本模块提供了以下主要功能：
1. 营销活动管理：创建、配置和管理营销活动
2. 邀请系统：生成邀请链接、追踪邀请流程
3. 奖励机制：定义奖励策略、计算和发放奖励

主要组件：
- Campaign: 营销活动模型
- Invitation: 邀请记录模型
- RewardStrategy: 奖励策略模型
- RewardRecord: 奖励记录模型
"""

# 导出ORM模型
from svc.apps.marketing.models import (
    Campaign,
    Invitation,
    RewardRecord,
    RewardStrategy
)

# 导出Schema模型
from svc.apps.marketing.schemas import (
    # 营销活动相关模型
    CampaignBase,
    CampaignCreate,
    CampaignUpdate,
    CampaignResponse,
    CampaignListResponse,
    # 邀请相关模型
    InvitationResponse,
    InvitationListResponse,
    # 奖励相关模型
    RewardStrategyBase,
    RewardStrategyCreate,
    RewardStrategyResponse,
    RewardStrategyListResponse,
    RewardRecordResponse,
    # 添加 GetInvitationsParams, GetRewardRecordsParams 等如果需要导出
    GetInvitationsParams,
    GetRewardRecordsParams,
    InvitationStatsResponse, 
)

# 导出服务类
from svc.apps.marketing.services import (
    CampaignService,
    InvitationService,
    RewardStrategyService,
    RewardRecordService,
)

# 导出依赖函数
from svc.apps.marketing.dependencies import (
    get_campaign_service,
    get_invitation_service,
    get_reward_strategy_service,
    get_reward_record_service
)

# 导出工具函数
from svc.apps.marketing.utils import (
    generate_unique_code,
    # is_valid_invitation_code, # These might be internal to InvitationService now
    # parse_invitation_code
)

from svc.apps.marketing.events import (
    invitation_handlers,
    reward_handlers,
    campaign_handlers,
    lead_handlers
)



__all__ = [
    # ORM模型
    "Campaign",
    "Invitation",
    "RewardRecord",
    "RewardStrategy",
    # 营销活动相关Schema
    "CampaignBase",
    "CampaignCreate",
    "CampaignUpdate",
    "CampaignResponse",
    "CampaignListResponse",
    # 邀请相关Schema
    "InvitationResponse",
    "InvitationListResponse",
    "GetInvitationsParams",
    "InvitationStatsResponse",
    # 奖励相关Schema
    "RewardStrategyBase",
    "RewardStrategyCreate",
    "RewardStrategyResponse",
    "RewardStrategyListResponse",
    "RewardRecordResponse",
    "GetRewardRecordsParams",
    # 服务类
    "CampaignService",
    "InvitationService",
    "RewardStrategyService",
    "RewardRecordService",
    # 依赖函数
    "get_campaign_service",
    "get_invitation_service",
    "get_reward_strategy_service",
    "get_reward_record_service",
    # 工具函数
    "generate_unique_code",
    # "is_valid_invitation_code",
    # "parse_invitation_code",
    # "marketing_app_router" # 移除 router 导出
    # 事件
    "invitation_handlers",
    "reward_handlers",
    "campaign_handlers",
    "lead_handlers"
]
