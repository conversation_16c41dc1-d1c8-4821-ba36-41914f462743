"""
奖励模型定义，包含奖励策略和奖励记录
"""
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Integer, Text, Float, ForeignKey
from sqlalchemy.orm import relationship
from enum import Enum
from typing import Optional

from svc.core.models.base import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 奖励类型枚举
class RewardType(str, Enum):
    FIXED = "fixed"           # 固定奖励
    PERCENTAGE = "percentage" # 百分比奖励
    TIERED = "tiered"         # 阶梯奖励
    PROGRESSIVE = "progressive" # 累进式奖励

class RewardStrategy(Base):
    """
    奖励策略模型，定义奖励的计算规则
    """
    __tablename__ = "reward_strategies"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    campaign_id = Column(BigInteger, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=False)
    
    name = Column(String(100), nullable=False, comment="策略名称")
    description = Column(Text, nullable=True, comment="策略描述")
    
    # 奖励规则
    reward_type = Column(String(20), nullable=False, comment="奖励类型")
    is_for_inviter = Column(Boolean, default=True, comment="是否给邀请人的奖励")
    is_for_invitee = Column(Boolean, default=False, comment="是否给被邀请人的奖励")
    
    # 奖励数值
    base_reward = Column(Float, nullable=False, default=0, comment="基础奖励值")
    percentage_rate = Column(Float, nullable=True, comment="百分比率")
    
    # 阶梯奖励配置 - 使用JSON存储阶梯配置
    tiered_config = Column(Text, nullable=True, comment="阶梯奖励配置，JSON格式")
    
    # 限制条件
    min_invitations = Column(Integer, nullable=True, comment="最小邀请数量")
    max_rewards = Column(Integer, nullable=True, comment="最大奖励次数")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    campaign = relationship("Campaign", back_populates="reward_strategies")
    
    def calculate_reward(self, invitation_count: int) -> float:
        """
        根据策略计算奖励值
        
        Args:
            invitation_count: 当前邀请数量
        
        Returns:
            float: 计算后的奖励值
        """
        if self.min_invitations and invitation_count < self.min_invitations:
            return 0
            
        if self.reward_type == RewardType.FIXED:
            return self.base_reward
            
        elif self.reward_type == RewardType.PERCENTAGE:
            if not self.percentage_rate:
                return self.base_reward
            return self.base_reward * self.percentage_rate
            
        elif self.reward_type == RewardType.TIERED:
            if not self.tiered_config:
                return self.base_reward
                
            import json
            tiers = json.loads(self.tiered_config)
            
            # 寻找匹配的档位
            reward_value = self.base_reward
            for tier in tiers:
                if invitation_count >= tier.get("min", 0) and invitation_count <= tier.get("max", float("inf")):
                    reward_value = tier.get("value", self.base_reward)
                    break
                    
            return reward_value
            
        elif self.reward_type == RewardType.PROGRESSIVE:
            if not self.tiered_config:
                return self.base_reward * invitation_count
                
            import json
            tiers = json.loads(self.tiered_config)
            
            # 计算累进奖励
            reward_value = 0
            remaining_count = invitation_count
            
            for tier in sorted(tiers, key=lambda x: x.get("min", 0)):
                tier_min = tier.get("min", 0)
                tier_max = tier.get("max", float("inf"))
                tier_value = tier.get("value", self.base_reward)
                
                if remaining_count <= 0:
                    break
                    
                # 计算当前档位内的邀请数
                count_in_tier = min(remaining_count, tier_max - tier_min + 1)
                reward_value += count_in_tier * tier_value
                remaining_count -= count_in_tier
                
            return reward_value
            
        return self.base_reward

class RewardRecord(Base):
    """
    奖励记录模型，记录用户获得的奖励
    """
    __tablename__ = "reward_records"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    campaign_id = Column(BigInteger, ForeignKey("campaigns.id", ondelete="CASCADE"), nullable=False)
    invitation_id = Column(BigInteger, ForeignKey("invitations.id", ondelete="SET NULL"), nullable=True)
    strategy_id = Column(BigInteger, ForeignKey("reward_strategies.id", ondelete="SET NULL"), nullable=True)
    
    # 奖励接收者
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    
    # 奖励信息
    reward_type = Column(String(20), nullable=False, comment="奖励类型")
    reward_value = Column(Float, nullable=False, comment="奖励值")
    reward_description = Column(String(200), nullable=True, comment="奖励描述")
    
    # 奖励状态
    is_issued = Column(Boolean, default=False, comment="是否已发放")
    issued_at = Column(DateTime, nullable=True, comment="发放时间")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    campaign = relationship("Campaign")
    invitation = relationship("Invitation", back_populates="reward_records")
    user = relationship("User")
    strategy = relationship("RewardStrategy")

    def to_dict(self):
        return {
            "id": self.id,
            "campaign_id": self.campaign_id,
            "invitation_id": self.invitation_id,
            "strategy_id": self.strategy_id,
            "user_id": self.user_id,
            "reward_type": self.reward_type,
            "reward_value": self.reward_value,
            "reward_description": self.reward_description,
            "is_issued": self.is_issued,
            "issued_at": self.issued_at,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }