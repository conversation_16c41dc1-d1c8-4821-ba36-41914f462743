"""
营销模块的数据库模型初始化。

定义了营销模块的核心数据模型，包括：
- 活动模型 (Campaign)
- 邀请模型 (Invitation)
- 奖励策略模型 (RewardStrategy)
- 奖励记录模型 (RewardRecord)
"""

from svc.apps.marketing.models.campaign import Campaign, CampaignStatus, AntiAbuseStrategy
from svc.apps.marketing.models.invitation import Invitation
from svc.apps.marketing.models.reward import RewardStrategy, RewardType, RewardRecord

__all__ = [
    "Campaign", "CampaignStatus", "AntiAbuseStrategy",
    "Invitation", 
    "RewardStrategy", "RewardType", 
    "RewardRecord"
] 