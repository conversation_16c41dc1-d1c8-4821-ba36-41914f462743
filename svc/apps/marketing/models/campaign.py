"""
营销活动模型，定义活动的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Boolean, Integer, Text, Float, ForeignKey, Enum
from sqlalchemy.orm import relationship
from typing import List, Optional, Dict

from svc.core.models.base import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 活动状态枚举
class CampaignStatus(str, enum.Enum):
    DRAFT = "draft"        # 草稿状态，尚未发布
    ACTIVE = "active"      # 活动中
    INACTIVE = "inactive"  # 已暂停
    ENDED = "ended"        # 已结束
    DELETED = "deleted"    # 已删除

# 防刷策略枚举
class AntiAbuseStrategy(str, enum.Enum):
    NONE = "none"             # 不做限制
    IP_LIMIT = "ip_limit"     # IP限制
    DEVICE_LIMIT = "device_limit" # 设备限制
    BOTH = "both"             # IP和设备都限制

class Campaign(Base):
    """
    营销活动模型，定义活动的基本属性和规则
    """
    __tablename__ = "campaigns"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    name = Column(String(100), nullable=False, comment="活动名称")
    description = Column(Text, nullable=True, comment="活动描述")
    creator_id = Column(BigInteger, ForeignKey("users.id"), nullable=False, comment="创建人ID")
    
    # 活动时间范围
    start_date = Column(DateTime, nullable=True, comment="活动开始时间")
    end_date = Column(DateTime, nullable=True, comment="活动结束时间")
    
    # 活动状态
    status = Column(String(20), nullable=False, default=CampaignStatus.DRAFT, comment="活动状态")
    
    # 活动规则
    max_participants = Column(Integer, nullable=True, comment="最大参与人数，不设置则无限制")
    max_rewards_per_user = Column(Integer, nullable=True, comment="每个用户可获得的最大奖励次数")
    
    # 防刷规则
    anti_abuse_strategy = Column(String(20), default=AntiAbuseStrategy.NONE, comment="防刷策略")
    allow_multi_invitations = Column(Boolean, default=True, comment="是否允许多次邀请")
    invitation_limit_per_user = Column(Integer, nullable=True, comment="每用户邀请次数上限")
    
    # 统计数据
    participant_count = Column(Integer, default=0, comment="参与人数")
    invitation_count = Column(Integer, default=0, comment="邀请次数")
    reward_count = Column(Integer, default=0, comment="奖励发放次数")
    total_reward_value = Column(Float, default=0.0, comment="总奖励价值")
    
    # 创建和更新时间
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, 
                        onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    invitations = relationship("Invitation", back_populates="campaign", cascade="all, delete-orphan", lazy="dynamic")
    reward_strategies = relationship("RewardStrategy", back_populates="campaign", cascade="all, delete-orphan", lazy="selectin")
    
    def is_valid(self) -> bool:
        """判断活动是否有效"""
        if self.status != CampaignStatus.ACTIVE:
            return False
            
        now = get_utc_now_without_tzinfo()
        if self.start_date and self.start_date > now:
            return False
            
        if self.end_date and self.end_date < now:
            return False
            
        if self.max_participants and self.participant_count >= self.max_participants:
            return False
            
        return True
        
    def to_dict(self) -> Dict:
        """将活动对象转换为字典，用于JSON序列化和缓存
        
        警告：在异步上下文中直接访问关系属性可能会导致MissingGreenlet错误
        
        Returns:
            dict: 活动的字典表示
        """
        # 不包含关系数据，关系数据应通过专门的异步方法获取
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "creator_id": self.creator_id,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "status": self.status,
            "max_participants": self.max_participants,
            "max_rewards_per_user": self.max_rewards_per_user,
            "anti_abuse_strategy": self.anti_abuse_strategy,
            "allow_multi_invitations": self.allow_multi_invitations,
            "invitation_limit_per_user": self.invitation_limit_per_user,
            "participant_count": self.participant_count,
            "invitation_count": self.invitation_count,
            "reward_count": self.reward_count,
            "total_reward_value": self.total_reward_value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 