from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel

class RewardBase(CamelCaseModel):
    user_id: int = Field(..., description="用户ID")
    campaign_id: int = Field(..., description="活动ID")
    reward_type: str = Field(..., description="奖励类型 (e.g., points, discount)")
    value: float = Field(..., description="奖励值")
    is_claimed: bool = Field(False, description="是否已领取")

class RewardCreate(RewardBase):
    pass

class RewardUpdate(CamelCaseModel):
    is_claimed: Optional[bool] = Field(None, description="是否已领取")

class RewardResponse(RewardBase):
    id: int = Field(..., description="奖励ID")
    claimed_at: Optional[datetime] = Field(None, description="领取时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

class RewardRecordResponse(BaseModel):
    """奖励记录响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "campaign_id": 1,
                "invitation_id": None,
                "strategy_id": None,
                "user_id": 1,
                "reward_type": "cash",
                "reward_value": 100.0,
                "reward_description": "邀请奖励",
                "is_issued": False,
                "issued_at": None,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="奖励记录ID")
    campaign_id: int = Field(description="活动ID")
    invitation_id: Optional[int] = Field(default=None, description="邀请记录ID")
    strategy_id: Optional[int] = Field(default=None, description="奖励策略ID")
    user_id: int = Field(description="用户ID")
    reward_type: str = Field(description="奖励类型")
    reward_value: float = Field(description="奖励值")
    reward_description: Optional[str] = Field(default=None, description="奖励描述")
    is_issued: bool = Field(description="是否已发放")
    issued_at: Optional[datetime] = Field(default=None, description="发放时间")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class RewardRecordCreate(BaseModel):
    """奖励记录创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    campaign_id: int = Field(description="活动ID")
    user_id: int = Field(description="用户ID")
    reward_type: str = Field(description="奖励类型")
    reward_value: float = Field(description="奖励值")
    invitation_id: Optional[int] = Field(default=None, description="邀请记录ID")
    strategy_id: Optional[int] = Field(default=None, description="奖励策略ID")
    reward_description: Optional[str] = Field(default=None, description="奖励描述")
    is_issued: Optional[bool] = Field(default=False, description="是否已发放")
    issued_at: Optional[datetime] = Field(default=None, description="发放时间")

class RewardRecordUpdate(BaseModel):
    """奖励记录更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    reward_value: Optional[float] = Field(default=None, description="奖励值")
    reward_description: Optional[str] = Field(default=None, description="奖励描述")
    is_issued: Optional[bool] = Field(default=None, description="是否已发放")
    issued_at: Optional[datetime] = Field(default=None, description="发放时间")

class RewardStatsResponse(BaseModel):
    """奖励统计响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "total_rewards": 100,
                "total_value": 10000,
                "issued_count": 80,
                "pending_count": 20,
                "by_type": {
                    "cash": {
                        "count": 50,
                        "value": 5000.0
                    }
                }
            }
        }
    )
    
    total_rewards: int = Field(description="总奖励数")
    total_value: int = Field(description="总奖励值")
    issued_count: int = Field(description="已发放数量")
    pending_count: int = Field(description="待发放数量")
    by_type: Dict[str, Dict[str, Any]]= Field(description="按类型统计")

class RewardRecordListResponse(PaginatedResponse[RewardRecordResponse]):
    """奖励记录列表响应模型"""
    pass

class RewardStrategyResponse(BaseModel):
    """奖励策略响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "campaign_id": 1,
                "name": "基础邀请奖励",
                "description": "邀请新用户奖励策略",
                "reward_type": "cash",
                "is_for_inviter": True,
                "is_for_invitee": True,
                "base_reward": 100.0,
                "percentage_rate": None,
                "tiered_config": None,
                "min_invitations": None,
                "max_rewards": None,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(description="策略ID")
    campaign_id: int = Field(description="活动ID")
    name: str = Field(description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    reward_type: str = Field(description="奖励类型")
    is_for_inviter: bool = Field(description="是否适用于邀请人")
    is_for_invitee: bool = Field(description="是否适用于被邀请人")
    base_reward: float = Field(description="基础奖励值")
    percentage_rate: Optional[float] = Field(default=None, description="百分比率")
    tiered_config: Optional[str] = Field(default=None, description="阶梯配置")
    min_invitations: Optional[int] = Field(default=None, description="最小邀请数")
    max_rewards: Optional[int] = Field(default=None, description="最大奖励数")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")

class RewardStrategyListResponse(PaginatedResponse[RewardStrategyResponse]):
    """奖励策略列表响应模型"""
    pass

class RewardStrategyCreate(BaseModel):
    """奖励策略创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    name: str = Field(description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    reward_type: str = Field(description="奖励类型")
    is_for_inviter: bool = Field(description="是否适用于邀请人")
    is_for_invitee: bool = Field(description="是否适用于被邀请人")
    base_reward: float = Field(description="基础奖励值")
    percentage_rate: Optional[float] = Field(default=None, description="百分比率")
    tiered_config: Optional[str] = Field(default=None, description="阶梯配置")
    min_invitations: Optional[int] = Field(default=None, description="最小邀请数")
    max_rewards: Optional[int] = Field(default=None, description="最大奖励数")

class RewardStrategyUpdate(BaseModel):
    """奖励策略更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
    )
    
    name: Optional[str] = Field(default=None, description="策略名称")
    description: Optional[str] = Field(default=None, description="策略描述")
    reward_type: Optional[str] = Field(default=None, description="奖励类型")
    is_for_inviter: Optional[bool] = Field(default=None, description="是否适用于邀请人")
    is_for_invitee: Optional[bool] = Field(default=None, description="是否适用于被邀请人")
    base_reward: Optional[float] = Field(default=None, description="基础奖励值")
    percentage_rate: Optional[float] = Field(default=None, description="百分比率")
    tiered_config: Optional[str] = Field(default=None, description="阶梯配置")
    min_invitations: Optional[int] = Field(default=None, description="最小邀请数")
    max_rewards: Optional[int] = Field(default=None, description="最大奖励数")

class GetRewardRecordsParams(BaseModel):
    """获取奖励记录列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'  # 忽略未定义的查询参数
    )

    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    campaign_id: Optional[int] = Field(default=None, description="活动ID过滤")
    user_id: Optional[int] = Field(default=None, description="用户ID过滤")
    reward_type: Optional[str] = Field(default=None, description="奖励类型过滤")
    is_issued: Optional[bool] = Field(default=None, description="是否已发放过滤")
    strategy_id: Optional[int] = Field(default=None, description="奖励策略ID过滤")
    # 可以根据需要添加更多过滤和排序字段
    # order_by: Optional[str] = Field(default="created_at", description="排序字段")
    # order_desc: Optional[bool] = Field(default=True, description="是否降序")




