from pydantic import BaseModel, Field, ConfigDict, EmailStr
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel

class InvitationBase(CamelCaseModel):
    email: EmailStr = Field(..., description="被邀请人邮箱")
    status: str = Field("pending", description="邀请状态 (pending, accepted, expired)")

class InvitationCreate(InvitationBase):
    """邀请创建模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "campaign_id": 1,
                "inviter_id": 1,    
            }
        }
    )
    
    campaign_id: int = Field(description="活动ID")
    inviter_id: int = Field(..., description="邀请人ID")
    
    pass

class InvitationUpdate(CamelCaseModel):
    """邀请更新模型"""
    model_config = ConfigDict(      
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invitee_id": 2,
            }
        }
    )

    invitee_id: Optional[int] = Field(default=None, description="被邀请人ID")
    status: Optional[str] = Field(None, description="邀请状态")

class InvitationCodeResponse(BaseModel):
    """邀请码响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "code": "INV123456",
                "invitation_link": "https://example.com/invite/INV123456"
            }
        }
    )
    
    code: str = Field(description="邀请码")
    invitation_link: str = Field(description="邀请链接")

class InvitationResponse(InvitationBase):
    """邀请响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "campaign_id": 1,
                "inviter_id": 1,
                "invitee_id": None,
                "code": "INV123456",
                "is_used": False,
                "opened_count": 0,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(..., description="邀请ID")
    campaign_id: int = Field(description="活动ID")
    inviter_id: int = Field(..., description="邀请人ID")
    invitee_id: Optional[int] = Field(None, description="被邀请人ID（接受后）")
    code: str = Field(description="邀请码")
    is_used: bool = Field(description="是否已使用")
    opened_count: int = Field(description="打开次数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    expires_at: datetime = Field(..., description="过期时间")

class InvitationStatsResponse(BaseModel):
    """邀请统计响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "total_invitations": 100,
                "successful_invitations": 50,
                "pending_invitations": 50,
                "total_opened": 75,
                "conversion_rate": 0.5
            }
        }
    )
    
    total_invitations: int = Field(description="总邀请数")
    successful_invitations: int = Field(description="成功邀请数")
    pending_invitations: int = Field(description="待处理邀请数")
    total_opened: int = Field(description="总打开次数")
    conversion_rate: float = Field(description="转化率")

class InvitationListResponse(PaginatedResponse[InvitationResponse]):
    """邀请列表响应模型"""
    pass 

class GetInvitationsParams(BaseModel):
    """获取邀请列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'  # 忽略未定义的查询参数
    )
    
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    campaign_id: Optional[int] = Field(default=None, description="活动ID过滤")
    inviter_id: Optional[int] = Field(default=None, description="邀请人ID过滤")
    is_used: Optional[bool] = Field(default=None, description="是否已使用过滤")
    # 可以根据需要添加更多过滤和排序字段
    # order_by: Optional[str] = Field(default="created_at", description="排序字段")
    # order_desc: Optional[bool] = Field(default=True, description="是否降序") 