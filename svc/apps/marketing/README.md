# 营销管理模块

基于产品模块模板创建的营销管理模块，提供完整的营销活动、邀请、线索和奖励管理功能。

## 模块结构

```
svc/apps/marketing/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── campaign.py            # 活动模型
│   ├── invitation.py          # 邀请模型
│   └── reward.py              # 奖励模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   ├── campaign.py            # 活动模式
│   ├── invitation.py          # 邀请模式
│   └── reward.py              # 奖励模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   ├── campaign.py            # 活动仓库
│   ├── invitation.py          # 邀请仓库
│   └── reward.py              # 奖励仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── campaign.py            # 活动服务
│   ├── invitation.py          # 邀请服务
│   └── reward.py              # 奖励服务
├── routers/                    # API路由
│   ├── __init__.py
│   ├── campaign.py            # 活动路由
│   ├── invitation.py          # 邀请路由
│   └── reward.py              # 奖励路由
├── events/                     # 事件处理器
│   ├── __init__.py
│   ├── campaign_handlers.py   # 活动事件
│   ├── invitation_handlers.py # 邀请事件
│   └── reward_handlers.py     # 奖励事件
└── utils/                      # 工具函数
    ├── __init__.py
    └── helpers.py             # 营销相关工具
```

## 核心功能

### 1. 营销活动管理 (Campaign)

-   活动的创建、查询、更新、删除
-   活动状态管理（草稿、进行中、已结束、已取消）
-   活动类型（满减、折扣、赠品、积分等）
-   活动规则和条件配置
-   活动统计（参与人数、转化率、奖励发放等）

### 2. 邀请管理 (Invitation)

-   邀请码生成与管理
-   邀请记录查询
-   邀请奖励发放
-   邀请状态跟踪

### 3. 线索管理 (Lead)

-   线索的创建、分配、跟进、转化
-   线索状态管理
-   线索来源统计

### 4. 奖励管理 (Reward)

-   奖励类型（积分、优惠券、实物等）
-   奖励发放与核销
-   奖励统计与报表

## API 端点

### 活动 API

-   `GET /campaigns/` - 获取活动列表
-   `GET /campaigns/{id}` - 获取活动详情
-   `POST /campaigns/admin/` - 创建活动（管理端）
-   `PUT /campaigns/admin/{id}` - 更新活动（管理端）
-   `DELETE /campaigns/admin/{id}` - 删除活动（管理端）

### 邀请 API

-   `GET /invitations/` - 获取邀请列表
-   `GET /invitations/{id}` - 获取邀请详情
-   `POST /invitations/admin/` - 创建邀请（管理端）
-   `PUT /invitations/admin/{id}` - 更新邀请（管理端）

### 奖励 API

-   `GET /rewards/` - 获取奖励列表
-   `GET /rewards/{id}` - 获取奖励详情
-   `POST /rewards/admin/` - 创建奖励（管理端）
-   `PUT /rewards/admin/{id}` - 更新奖励（管理端）

## 事件系统

模块集成了事件系统，支持以下事件：

### 活动事件

-   `marketing:campaign:created` - 活动创建
-   `marketing:campaign:updated` - 活动更新
-   `marketing:campaign:deleted` - 活动删除
-   `marketing:campaign:status_changed` - 活动状态变更

### 邀请事件

-   `marketing:invitation:created` - 邀请创建
-   `marketing:invitation:updated` - 邀请更新
-   `marketing:invitation:deleted` - 邀请删除

### 奖励事件

-   `marketing:reward:created` - 奖励创建
-   `marketing:reward:updated` - 奖励更新
-   `marketing:reward:deleted` - 奖励删除

## 缓存策略

-   活动信息缓存：1 小时
-   邀请信息缓存：30 分钟
-   奖励信息缓存：30 分钟

## 权限控制

模块集成了基于角色的权限控制：

-   `campaign:read` - 活动查看权限
-   `campaign:create` - 活动创建权限
-   `campaign:update` - 活动更新权限
-   `campaign:delete` - 活动删除权限
-   `invitation:*` - 邀请相关权限
-   `reward:*` - 奖励相关权限

## 数据库迁移

```sql
-- 创建活动表
CREATE TABLE campaigns (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'draft',
    start_time DATETIME,
    end_time DATETIME,
    rules JSON,
    meta_data JSON,
    statistics JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建邀请表
CREATE TABLE invitations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(100) NOT NULL UNIQUE,
    inviter_id BIGINT NOT NULL,
    invitee_id BIGINT,
    status VARCHAR(20) DEFAULT 'pending',
    reward_id BIGINT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建奖励表
CREATE TABLE rewards (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type VARCHAR(50) NOT NULL,
    value VARCHAR(100),
    status VARCHAR(20) DEFAULT 'active',
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 注意事项

1. 活动、邀请、奖励等数据变更时需及时更新缓存。
2. 事件处理器异常不影响主业务流程。
3. 权限校验和数据一致性需重点关注。
4. 活动规则和奖励发放逻辑需灵活可配置。
