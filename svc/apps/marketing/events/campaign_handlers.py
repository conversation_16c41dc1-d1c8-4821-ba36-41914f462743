"""
营销活动相关的后台任务
"""

import logging
from typing import Any, Dict, Optional

from fastapi import Depends  # Added
from fastapi_events.dispatcher import dispatch
from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event  # Added

from svc.apps.marketing.dependencies import get_campaign_service
# 服务/仓库导入
from svc.apps.marketing.services.campaign import CampaignService
# 事件/任务名称 和 核心事件函数
from svc.core.events.event_names import \
    SYSTEM_AUDIT_LOG_RECORDED  # Changed from REQUESTED
from svc.core.events.event_names import (MARKETING_CAMPAIGN_CREATED,
                                         MARKETING_CAMPAIGN_STATUS_CHANGED,
                                         MARKETING_CAMPAIGN_UPDATED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED,
                                         SYSTEM_NOTIFICATION_SEND_REQUESTED,
                                         SYSTEM_STATS_UPDATE_REQUESTED)

logger = logging.getLogger(__name__)

# --- Tasks ---
# Registered in arq_worker.WorkerSettings
@local_handler.register(event_name=MARKETING_CAMPAIGN_CREATED)
async def handle_campaign_created(
    event: Event, # Changed signature
    campaign_service: CampaignService = Depends(get_campaign_service)
    # creator_id: Optional[int] = None, # Now in payload
    # ... other relevant data from creation ...
):
    """处理活动创建事件 (Arq Task): 可能用于缓存、统计、通知等 (使用 DI)。"""
    event_name, payload = event
    campaign_id = payload.get('campaign_id')
    creator_id = payload.get('creator_id')

    if not campaign_id:
        logger.error("处理活动创建事件失败: 缺少 campaign_id")
        return

    logger.info(f"[Arq Task] 处理活动创建事件: campaign_id={campaign_id}")

    try:

        # 1. 预热活动缓存 (示例)
        await campaign_service.warm_up_campaign_cache(campaign_id)
        logger.info(f"Campaign cache warmed up for campaign_id={campaign_id}")

        # 活动创建的审计日志由审计中间件记录，这里不再重复记录

        # 3. 触发统计更新任务 (示例)
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "campaign_total_count",
                "increment_value": 1
            }
        )

        # 4. 发送本地事件 (如果需要)
        # dispatch(SYSTEM_CACHE_INVALIDATION_REQUESTED, payload={'resource_type':'campaign', 'resource_id':campaign_id})

    except Exception as e:
        logger.error(f"处理活动创建事件失败: campaign_id={campaign_id}, 错误={str(e)}", exc_info=True)
        raise # Let arq handle retry

@local_handler.register(event_name=MARKETING_CAMPAIGN_UPDATED)
async def handle_campaign_updated(
    event: Event, # Changed signature
    campaign_service: CampaignService = Depends(get_campaign_service)
):
    """处理活动更新事件 (Arq Task): 主要用于缓存失效和审计 (使用 DI)。"""
    event_name, payload = event
    campaign_id = payload.get('campaign_id')
    updater_id = payload.get('updater_id')
    changed_fields = payload.get('changed_fields')

    if not campaign_id:
        logger.error("处理活动更新事件失败: 缺少 campaign_id")
        return

    logger.info(f"[Arq Task] 处理活动更新事件: campaign_id={campaign_id}")
    try:
        # 1. 清理相关缓存
        await campaign_service.delete_campaign_cache(campaign_id)
        logger.info(f"Campaign cache invalidated for campaign_id={campaign_id}")

        # 2. 发送本地事件用于其他可能的即时处理
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={
                'resource_type': 'campaign',
                'resource_id': campaign_id
            }
        )

        # 活动更新的审计日志由审计中间件记录，这里不再重复记录

    except Exception as e:
        logger.error(f"处理活动更新事件失败: campaign_id={campaign_id}, 错误={str(e)}", exc_info=True)
        raise # Let arq handle retry

@local_handler.register(event_name=MARKETING_CAMPAIGN_STATUS_CHANGED)
async def handle_campaign_status_changed(
    event: Event,
    campaign_service: CampaignService = Depends(get_campaign_service),
    # user_service: UserService = Depends(get_user_service) # Uncomment if needed
):
    """处理活动状态变更事件 (Arq Task): 缓存、审计、通知等 (使用 DI)。"""
    event_name, payload = event
    campaign_id = payload.get("campaign_id")
    old_status = payload.get("old_status")
    new_status = payload.get("new_status")
    updater_id = payload.get("updater_id")

    if not campaign_id or old_status is None or new_status is None:
        logger.error("处理活动状态变更事件失败: 缺少 campaign_id, old_status 或 new_status")
        return

    logger.info(f"[Arq Task] 处理活动状态变更: id={campaign_id}, {old_status} -> {new_status}")

    try:
        # 1. 清理/更新缓存
        await campaign_service.delete_campaign_cache(campaign_id)
        logger.info(f"Campaign cache invalidated due to status change for campaign_id={campaign_id}")
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={
                'resource_type': 'campaign',
                'resource_id': campaign_id
            }
        )

        # 活动状态变更的审计日志由审计中间件记录，这里不再重复记录

        # 3. 根据状态发送通知 (示例)
        if new_status == 'active':
            # 获取活动详情用于通知 (需要 campaign_service)
            campaign_result = await campaign_service.get_resource_by_id(campaign_id) 
            if campaign_result.is_success: 
                campaign = campaign_result.data
                dispatch(
                    SYSTEM_NOTIFICATION_SEND_REQUESTED,
                    payload={
                        "recipient_group": "marketing_team", # Example target
                        "title": f"活动已激活: {getattr(campaign, 'name', 'N/A')}", # Safely access name
                        "message": f"活动 '{getattr(campaign, 'name', 'N/A')}' (ID: {campaign_id}) 已由用户 {updater_id} 激活。",
                        "channel": "email" # Example channel
                    }
                )
            else:
                logger.warning(f"无法获取活动 {campaign_id} 详情以发送激活通知")
        elif new_status == 'inactive' or new_status == 'archived':
            # Maybe notify if campaign ended unexpectedly
            pass

        # 4. 更新统计 (示例)
        metric = None
        increment = 0
        if old_status != 'active' and new_status == 'active':
            metric = "campaign_active_count"
            increment = 1
        elif old_status == 'active' and new_status != 'active':
            metric = "campaign_active_count"
            increment = -1

        if metric:
            dispatch(
                SYSTEM_STATS_UPDATE_REQUESTED,
                payload={
                    "entity_type": "system",
                    "metric_type": metric,
                    "increment_value": increment
                }
            )

    except Exception as e:
        logger.error(f"处理活动状态变更事件失败: campaign_id={campaign_id}, 错误={str(e)}", exc_info=True)
        # Decide if retry is appropriate based on exception type
        raise # Let arq handle retry if appropriate