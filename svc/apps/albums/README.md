# 图册管理模块（Album Management Module）

本模块用于全局管理商品、营销等相关图片图册，支持分组（通过标签属性）、云存储、排序、事件通知
、权限控制等功能。

## 目录结构

```
svc/apps/albums/
├── __init__.py
├── README.md
├── dependencies.py
├── models/
│   ├── __init__.py
│   ├── album.py
│   └── album_image.py
├── schemas/
│   ├── __init__.py
│   ├── album.py
│   └── album_image.py
├── repositories/
│   ├── __init__.py
│   ├── album.py
│   └── album_image.py
├── services/
│   ├── __init__.py
│   ├── album.py
│   └── album_image.py
├── routers/
│   ├── __init__.py
│   ├── album.py
│   └── album_image.py
├── events/
│   ├── __init__.py
│   ├── album_events.py
│   └── album_image_events.py
├── utils/
│   ├── __init__.py
│   └── cloud_storage.py
```

## 数据模型说明

### Album（图册）

-   id: 主键
-   name: 图册名称
-   description: 图册描述
-   tags: 标签（List[str]，用于分组/分类/标记）
-   cover_image_id: 封面图片 ID
-   status: 状态（active/inactive/deleted）
-   sort_order: 排序
-   meta_data: 其他元数据（JSON）
-   created_at, updated_at, deleted_at: 时间戳

### AlbumImage（图册图片）

-   id: 主键
-   album_id: 所属图册 ID
-   url: 云存储图片 URL
-   file_name: 文件名
-   file_size: 文件大小
-   width, height: 图片尺寸
-   mime_type: 图片类型
-   is_cover: 是否为封面
-   sort_order: 排序
-   status: 状态（active/inactive/deleted）
-   meta_data: 其他元数据（JSON）
-   created_at, updated_at, deleted_at: 时间戳

## 主要 API（管理端）

所有管理端 API 均以`/admin/albums/`为前缀：

-   `GET /admin/albums/`：分页获取图册列表，支持标签筛选
-   `GET /admin/albums/{album_id}`：获取图册详情
-   `POST /admin/albums/`：创建图册
-   `PUT /admin/albums/{album_id}`：更新图册
-   `DELETE /admin/albums/{album_id}`：删除图册（软删除）
-   `GET /admin/albums/{album_id}/images/`：分页获取图册图片
-   `POST /admin/albums/{album_id}/images/`：上传图片（云存储）
-   `PUT /admin/albums/{album_id}/images/{image_id}`：更新图片元数据/排序/封面
-   `DELETE /admin/albums/{album_id}/images/{image_id}`：删除图片

## 权限控制

-   album:read, album:create, album:update, album:delete
-   album_image:read, album_image:create, album_image:update, album_image:delete

## 事件通知

-   album:created, album:updated, album:deleted
-   album_image:uploaded, album_image:updated, album_image:deleted

## 云存储

-   图片上传/删除通过云存储（如 OSS/S3），仅存储 URL 和元数据

## 排序与封面

-   图册和图片均支持 sort_order 字段排序
-   支持设置封面图片（is_cover/cover_image_id）

## 软删除与状态管理

-   支持软删除（deleted_at），状态管理（active/inactive/deleted）

## 注意事项

1. 图册分组通过 tags 属性实现，无需单独分组表
2. 图片上传需返回云存储 URL 及元数据
3. 删除操作为软删除，数据可恢复
4. 事件通知采用 fastapi-events
5. 权限需集成全局认证/权限系统
6. 支持批量操作、图片批量上传/排序

</rewritten_file>
