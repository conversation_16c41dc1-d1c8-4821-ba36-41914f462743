"""
图册图片数据访问层。
负责图册图片模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, asc, func, or_

from svc.core.repositories import BaseRepository
from svc.apps.albums.models.album_image import AlbumImage, AlbumImageStatus
from svc.apps.albums.schemas.album_image import AlbumImageCreate, AlbumImageUpdate

class AlbumImageRepository(BaseRepository[AlbumImage, AlbumImageCreate, AlbumImageUpdate]):
    """图册图片仓库类，提供图片数据访问方法"""
    def __init__(self, db: AsyncSession):
        super().__init__(db, AlbumImage)

    async def get_album_images(
        self,
        skip: int = 0,
        limit: int = 10,
        album_id: Optional[int] = None,
        status: Optional[str] = None,
        search_term: Optional[str] = None,
        order_by: str = "sort_order",
        order_desc: bool = False
    ) -> <PERSON><PERSON>[List[AlbumImage], int]:
        """获取图册图片列表及总数，支持album_id、状态、搜索、排序"""
        query = select(self.model)
        conditions = []
        if album_id:
            conditions.append(self.model.album_id == album_id)
        if status:
            conditions.append(self.model.status == status)
        if search_term:
            conditions.append(
                or_(
                    self.model.file_name.ilike(f"%{search_term}%"),
                    self.model.url.ilike(f"%{search_term}%")
                )
            )
        if conditions:
            query = query.where(and_(*conditions))
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        images = result.scalars().all()
        # 总数
        count_query = select(func.count()).select_from(self.model)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        return images, total 