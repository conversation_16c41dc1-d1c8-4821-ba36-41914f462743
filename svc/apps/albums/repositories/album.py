"""
图册数据访问层。
负责图册模型的数据库访问操作，实现数据访问与业务逻辑分离。
"""
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, desc, asc, func, or_, cast, String

from svc.core.repositories import BaseRepository
from svc.apps.albums.models.album import Album, AlbumStatus
from svc.apps.albums.schemas.album import AlbumCreate, AlbumUpdate

class AlbumRepository(BaseRepository[Album, AlbumCreate, AlbumUpdate]):
    """图册仓库类，提供图册数据访问方法"""
    def __init__(self, db: AsyncSession):
        super().__init__(db, Album)

    async def get_albums(
        self,
        skip: int = 0,
        limit: int = 10,
        status: Optional[str] = None,
        tags: Optional[List[str]] = None,
        search_term: Optional[str] = None,
        order_by: str = "sort_order",
        order_desc: bool = False
    ) -> Tu<PERSON>[List[Album], int]:
        """获取图册列表及总数，支持标签、状态、搜索、排序"""
        query = select(self.model)
        conditions = []
        if status:
            conditions.append(self.model.status == status)
        if tags:
            # JSON数组包含任一tag
            for tag in tags:
                conditions.append(cast(self.model.tags, String).ilike(f"%{tag}%"))
        if search_term:
            conditions.append(
                or_(
                    self.model.name.ilike(f"%{search_term}%"),
                    self.model.description.ilike(f"%{search_term}%")
                )
            )
        if conditions:
            query = query.where(and_(*conditions))
        if order_by and hasattr(self.model, order_by):
            order_column = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_column))
            else:
                query = query.order_by(asc(order_column))
        query = query.offset(skip).limit(limit)
        result = await self.db.execute(query)
        albums = result.scalars().all()
        # 总数
        count_query = select(func.count()).select_from(self.model)
        if conditions:
            count_query = count_query.where(and_(*conditions))
        count_result = await self.db.execute(count_query)
        total = count_result.scalar() or 0
        return albums, total 