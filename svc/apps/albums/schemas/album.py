from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import datetime
from svc.core.models.base import CamelCaseModel
from svc.apps.albums.schemas.album_image import AlbumImageResponse

# 分页基类（可复用产品模块的PaginatedResponse）
from svc.core.schemas.base import PaginatedResponse

class AlbumBase(CamelCaseModel):
    """图册基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "主图册",
                "description": "商品主图",
                "tags": ["main", "product"],
                "cover_image_id": 1,
                "status": "active",
                "sort_order": 1,
                "meta_data": {"scene": "product"}
            }
        }
    )
    name: str = Field(..., max_length=100, description="相册名称")
    description: Optional[str] = Field(None, description="相册描述")
    tags: List[str] = Field(default_factory=list, description="标签（分组/分类/标记）")
    cover_image_id: Optional[int] = Field(default=None, description="封面图片ID")
    status: str = Field(default="active", description="图册状态")
    sort_order: int = Field(default=0, description="排序顺序")
    meta_data: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    images: List[AlbumImageResponse] = Field([], description="相册图片列表")

class AlbumCreate(AlbumBase):
    """图册创建模型"""
    pass

class AlbumUpdate(CamelCaseModel):
    """图册更新模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "新主图册",
                "description": "更新描述",
                "tags": ["main"],
                "cover_image_id": 2,
                "status": "active",
                "sort_order": 2,
                "meta_data": {"scene": "product"}
            }
        }
    )
    name: Optional[str] = Field(None, max_length=100, description="相册名称")
    description: Optional[str] = Field(None, description="相册描述")
    tags: Optional[List[str]] = Field(default=None, description="标签")
    cover_image_id: Optional[int] = Field(default=None, description="封面图片ID")
    status: Optional[str] = Field(default=None, description="图册状态")
    sort_order: Optional[int] = Field(default=None, description="排序顺序")
    meta_data: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

class AlbumResponse(AlbumBase):
    """图册响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00",
                "deleted_at": None
            }
        }
    )
    id: int = Field(..., description="相册ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    deleted_at: Optional[datetime] = Field(default=None, description="删除时间")

class AlbumListResponse(PaginatedResponse[AlbumResponse]):
    """图册列表响应模型"""
    pass

class GetAlbumsParams(BaseModel):
    """获取图册列表的查询参数模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        extra='ignore'
    )
    page_num: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=1000, description="每页数量")
    status: Optional[str] = Field(default=None, description="图册状态")
    tags: Optional[List[str]] = Field(default=None, description="标签筛选")
    search_term: Optional[str] = Field(default=None, description="搜索关键词")
    order_by: Optional[str] = Field(default="sort_order", description="排序字段")
    order_desc: Optional[bool] = Field(default=False, description="是否降序")

class UserAlbumResponse(BaseModel):
    """用户端精简图册响应模型"""
    id: int
    name: str
    images: List[str] 