"""
图册管理模块依赖注入
"""
from fastapi import Depends
from typing import Optional
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.albums.repositories.album import AlbumRepository
from svc.apps.albums.repositories.album_image import AlbumImageRepository
from svc.apps.albums.services.album import AlbumService
from svc.apps.albums.services.album_image import AlbumImageService
from svc.core.cache.redis import get_redis
from svc.core.database import get_session_for_route

async def get_album_repository(
    db: AsyncSession = Depends(get_session_for_route())
) -> AlbumRepository:
    return AlbumRepository(db)

async def get_album_image_repository(
    db: AsyncSession = Depends(get_session_for_route())
) -> AlbumImageRepository:
    return AlbumImageRepository(db)

async def get_album_service(
    redis: Optional[Redis] = Depends(get_redis),
    album_repo: AlbumRepository = Depends(get_album_repository)
) -> AlbumService:
    return AlbumService(redis=redis, album_repo=album_repo)

async def get_album_image_service(
    redis: Optional[Redis] = Depends(get_redis),
    image_repo: AlbumImageRepository = Depends(get_album_image_repository)
) -> AlbumImageService:
    return AlbumImageService(redis=redis, image_repo=image_repo) 