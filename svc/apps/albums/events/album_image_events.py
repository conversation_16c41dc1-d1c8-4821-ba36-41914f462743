"""
图册图片事件处理器
"""
import logging
from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

logger = logging.getLogger(__name__)

class AlbumImageEventHandler:
    """图册图片事件处理器类"""

    @staticmethod
    @local_handler.register(event_name="album_image:uploaded")
    async def handle_album_image_uploaded(event: Event):
        try:
            logger.info(f"处理图片上传事件: {event}")
            image_data = event[1]
            logger.info(f"图片已上传: ID={image_data.get('id')}, 图册ID={image_data.get('album_id')}")
            # 可扩展：发送通知、更新缓存等
        except Exception as e:
            logger.error(f"处理图片上传事件失败: {str(e)}", exc_info=True)

    @staticmethod
    @local_handler.register(event_name="album_image:updated")
    async def handle_album_image_updated(event: Event):
        try:
            logger.info(f"处理图片更新事件: {event}")
            image_data = event[1]
            logger.info(f"图片已更新: ID={image_data.get('id')}, 图册ID={image_data.get('album_id')}")
            # 可扩展：清除缓存、发送通知等
        except Exception as e:
            logger.error(f"处理图片更新事件失败: {str(e)}", exc_info=True)

    @staticmethod
    @local_handler.register(event_name="album_image:deleted")
    async def handle_album_image_deleted(event: Event):
        try:
            logger.info(f"处理图片删除事件: {event}")
            image_data = event[1]
            logger.info(f"图片已删除: ID={image_data.get('id')}, 图册ID={image_data.get('album_id')}")
            # 可扩展：清除缓存、发送通知等
        except Exception as e:
            logger.error(f"处理图片删除事件失败: {str(e)}", exc_info=True) 