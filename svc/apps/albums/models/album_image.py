"""
图册图片模型，定义图册图片的基本属性和规则
"""
import enum
from sqlalchemy import Column, BigInteger, String, DateTime, Integer, Text, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from typing import Optional, Dict

from svc.core.models.base import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 图片状态枚举
class AlbumImageStatus(str, enum.Enum):
    ACTIVE = "active"      # 启用
    INACTIVE = "inactive"  # 禁用
    DELETED = "deleted"    # 已删除

class AlbumImage(Base):
    """
    图册图片模型，定义图册图片的基本属性和规则
    """
    __tablename__ = "album_images"

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    album_id = Column(BigInteger, ForeignKey("albums.id"), nullable=False, comment="所属图册ID")
    url = Column(String(500), nullable=False, comment="云存储图片URL")
    file_name = Column(String(200), nullable=True, comment="文件名")
    file_size = Column(Integer, nullable=True, comment="文件大小（字节）")
    width = Column(Integer, nullable=True, comment="图片宽度")
    height = Column(Integer, nullable=True, comment="图片高度")
    mime_type = Column(String(50), nullable=True, comment="图片类型")
    is_cover = Column(Boolean, default=False, comment="是否为封面")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    status = Column(String(20), nullable=False, default=AlbumImageStatus.ACTIVE, comment="图片状态")
    meta_data = Column(JSON, default=dict, comment="元数据")
    created_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    deleted_at = Column(DateTime, nullable=True, comment="删除时间（软删除）")

    # 关系
    album = relationship(
        "Album",
        back_populates="images",
        lazy="selectin",
        foreign_keys=[album_id]
    )

    def is_active(self) -> bool:
        """判断图片是否启用"""
        return self.status == AlbumImageStatus.ACTIVE

    def to_dict(self) -> Dict:
        """将图片对象转换为字典"""
        return {
            "id": self.id,
            "album_id": self.album_id,
            "url": self.url,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "width": self.width,
            "height": self.height,
            "mime_type": self.mime_type,
            "is_cover": self.is_cover,
            "sort_order": self.sort_order,
            "status": self.status,
            "meta_data": self.meta_data,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "deleted_at": self.deleted_at.isoformat() if self.deleted_at else None
        } 