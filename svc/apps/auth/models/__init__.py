# -*- coding: utf-8 -*-
"""
Auth模块的ORM模型。

提供认证与授权相关的数据库模型，包括：
- 用户模型
- 角色模型
- 权限模型
- 微信用户模型
- 用户角色关联模型
"""

# 按照依赖关系顺序导入，避免循环依赖
from .user_role import user_role
from .permission import Permission, role_permission_table
from .role import Role
from .user import User
from .wechat_user import WechatUser

__all__ = [
    'User',
    'Role',
    'Permission',
    'WechatUser',
    'user_role',
    'role_permission_table'
]
