"""
关联表模块。
定义了用户、角色等实体之间的关联关系表。
"""
from datetime import datetime

from sqlalchemy import Column, ForeignKey, Table, DateTime, BigInteger, Integer
from svc.core.database import Base
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 用户角色关联表
user_role = Table(
    "user_role",
    Base.metadata,
    Column(
        "user_id",
        BigInteger,
        ForeignKey("users.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "role_id",
        BigInteger,
        ForeignKey("roles.id", ondelete="CASCADE"),
        primary_key=True,
    ),
    Column(
        "created_at",
        DateTime,
        default=get_utc_now_without_tzinfo,
        nullable=False,
    ),
    extend_existing=True,  # 允许表定义扩展
)

