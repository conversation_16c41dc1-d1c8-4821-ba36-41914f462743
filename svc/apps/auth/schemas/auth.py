"""
认证模块的模型定义，包含认证、令牌和用户相关的所有模型。
采用函数式设计和RORO模式。
"""
from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any, Union

from pydantic import BaseModel, EmailStr, Field, field_validator, ConfigDict

from svc.core.models.base import CamelCaseModel


# -------------------- 基础模型 --------------------




# -------------------- 认证模型 --------------------

class AuthCredentials(CamelCaseModel):
    """基本认证凭据"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "password": "password123"
            }
        }
    )
    
    email: EmailStr = Field(description="用户邮箱")
    password: str = Field(description="用户密码")


# -------------------- 令牌模型 --------------------

class TokenData(CamelCaseModel):
    """访问令牌数据"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "token_type": "bearer",
                "expires_in": 3600,
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                "last_login": "2024-03-24T12:00:00",
                "user_id": 1
            }
        }
    )
    
    access_token: str = Field(description="访问令牌")
    token_type: str = Field(default="bearer", description="令牌类型")
    expires_in: int = Field(description="过期时间（秒）")
    refresh_token: Optional[str] = Field(default=None, description="刷新令牌")
    last_login: Optional[datetime] = Field(default=None, description="最后登录时间")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class TokenPayload(CamelCaseModel):
    """令牌载荷，用于JWT编码/解码"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "sub": 1,
                "exp": 1711267200.0,
                "scopes": ["user", "admin"]
            }
        }
    )
    
    sub: int = Field(description="用户ID")
    exp: float = Field(description="过期时间戳")
    scopes: List[str] = Field(default_factory=list, description="权限范围")


class WechatResponse(CamelCaseModel):
    """微信响应模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "tokenData": {  
                    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "tokenType": "bearer",
                    "expiresIn": 3600,
                    "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
                    "lastLogin": "2024-03-24T12:00:00"
                },
                "fromCode": "023BNwGa1CiTzU0mZHIa1wKLgE0BNwGL",
                "isNewUser": True,
                "userInfo": {
                    "nickName": "微信用户",
                    "avatarUrl": "https://example.com/avatar.jpg"
                }
            }
        }
    )
    token_data: TokenData = Field(description="令牌数据")
    from_code: str = Field(description="来源码")
    is_new_user: bool = Field(description="是否新用户")
    user_info: Dict[str, Any] = Field(description="用户信息")


class RefreshTokenRequest(CamelCaseModel):
    """刷新令牌请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
            }
        }
    )
    
    refresh_token: str = Field(description="刷新令牌")


# -------------------- 登录模型 --------------------

class LoginRequest(AuthCredentials):
    """登录请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "password": "password123",
                "remember_me": True
            }
        }
    )
    
    remember_me: bool = Field(default=False, description="记住我")


class LoginContext(CamelCaseModel):
    """登录上下文信息"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0...",
                "device_id": "device123"
            }
        }
    )
    
    ip_address: str = Field(default="127.0.0.1", description="IP地址")
    user_agent: str = Field(default="Unknown", description="用户代理")
    device_id: Optional[str] = Field(default=None, description="设备ID")


class LoginParams(LoginRequest, LoginContext):
    """登录参数，组合了请求和上下文"""
    pass


# -------------------- 密码重置模型 --------------------

class PasswordResetRequest(CamelCaseModel):
    """密码重置请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "email": "<EMAIL>"
            }
        }
    )
    
    email: EmailStr = Field(description="用户邮箱")


class PasswordResetParams(CamelCaseModel):
    """密码重置参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "token": "reset_token123",
                "new_password": "newpassword123"
            }
        }
    )
    
    token: str = Field(description="重置令牌")
    new_password: str = Field(description="新密码")
    
    @field_validator("new_password")
    def validate_password_strength(cls, value: str) -> str:
        """验证密码强度"""
        if len(value) < 8:
            raise ValueError("密码长度必须至少为8个字符")
        return value


class PasswordResetResponseData(CamelCaseModel):
    """密码重置响应数据"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "reset_completed": True,
                "message": "密码重置成功"
            }
        }
    )
    
    reset_completed: bool = Field(default=False, description="重置是否完成")
    message: Optional[str] = Field(default=None, description="提示信息")