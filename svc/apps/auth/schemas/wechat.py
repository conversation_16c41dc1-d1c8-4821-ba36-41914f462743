"""
微信小程序授权相关的模型定义。
"""
from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict, EmailStr, field_validator

from svc.core.models.base import CamelCaseModel

# -------------------- 微信API返回结构 --------------------

class WxCode2SessionResponse(BaseModel):
    """微信code2session接口返回结构"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "openid": "OPENID",
                "session_key": "SESSIONKEY",
                "unionid": "UNIONID",
                "errcode": 0,
                "errmsg": "ok"
            }
        }
    )
    
    openid: str = Field(description="用户唯一标识")
    session_key: str = Field(description="会话密钥")
    unionid: Optional[str] = Field(default=None, description="用户在开放平台的唯一标识")
    errcode: Optional[int] = Field(default=None, description="错误码")
    errmsg: Optional[str] = Field(default=None, description="错误信息")


# -------------------- 微信用户模型 --------------------

class WechatUserBase(BaseModel):
    """微信用户基本信息"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "nickname": "微信用户",
                "avatar_url": "https://example.com/avatar.jpg",
                "gender": 1,
                "country": "China",
                "province": "Guangdong",
                "city": "Shenzhen",
                "language": "zh_CN"
            }
        }
    )
    
    nickname: Optional[str] = Field(default=None, description="用户昵称")
    avatar_url: Optional[str] = Field(default=None, description="用户头像")
    gender: Optional[int] = Field(default=None, description="用户性别")
    country: Optional[str] = Field(default=None, description="用户所在国家")
    province: Optional[str] = Field(default=None, description="用户所在省份")
    city: Optional[str] = Field(default=None, description="用户所在城市")
    language: Optional[str] = Field(default=None, description="用户的语言")


class WechatUserInfo(WechatUserBase):
    """微信用户信息"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "nickName": "微信用户",
                "avatarUrl": "https://example.com/avatar.jpg",
                "gender": 1,
                "country": "China",
                "province": "Guangdong",
                "city": "Shenzhen",
                "language": "zh_CN"
            }
        }
    )
    
    nickName: Optional[str] = Field(default=None, description="用户昵称")
    avatarUrl: Optional[str] = Field(default=None, description="用户头像")


class WechatUserResponse(WechatUserBase):
    """微信用户返回模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "openid": "OPENID",
                "unionid": "UNIONID",
                "user_id": 1,
                "nickname": "微信用户",
                "avatar_url": "https://example.com/avatar.jpg",
                "gender": 1,
                "country": "China",
                "province": "Guangdong",
                "city": "Shenzhen",
                "language": "zh_CN",
                "is_active": True,
                "created_at": "2024-03-24T12:00:00Z",
                "updated_at": "2024-03-24T12:00:00Z",
                "last_login": "2024-03-24T12:00:00Z"
            }
        }
    )
    
    id: int = Field(description="微信用户ID")
    openid: str = Field(description="用户唯一标识")
    unionid: Optional[str] = Field(default=None, description="用户在开放平台的唯一标识")
    user_id: Optional[int] = Field(default=None, description="绑定的系统用户ID")
    is_active: bool = Field(description="是否活跃")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    last_login: Optional[datetime] = Field(default=None, description="最后登录时间")


# -------------------- 微信登录模型 --------------------

class WechatLoginRequest(CamelCaseModel):
    """微信登录请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "code": "023BNwGa1CiTzU0mZHIa1wKLgE0BNwGL",
                "user_info": {
                    "nickName": "微信用户",
                    "avatarUrl": "https://example.com/avatar.jpg",
                    "gender": 1,
                    "country": "China",
                    "province": "Guangdong",
                    "city": "Shenzhen",
                    "language": "zh_CN"
                }
            }
        }
    )
    
    code: str = Field(description="微信临时登录凭证")
    user_info: Optional[WechatUserInfo] = Field(default=None, description="用户信息")
    from_code:Optional[str]=Field(alias='fromCode', default='',description='来源码')


class WechatBindRequest(BaseModel):
    """微信绑定请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "wechat_user_id": 1,
                "user_id": 1
            }
        }
    )
    
    wechat_user_id: int = Field(description="微信用户ID")
    user_id: int = Field(description="系统用户ID")


# -------------------- 微信操作参数和结果模型 --------------------

class WechatLoginParams(BaseModel):
    """微信登录参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "code": "023BNwGa1CiTzU0mZHIa1wKLgE0BNwGL",
                "user_info": {
                    "nickName": "微信用户",
                    "avatarUrl": "https://example.com/avatar.jpg"
                },
                "ip_address": "127.0.0.1",
                "user_agent": "Mozilla/5.0"
            }
        }
    )
    
    code: str = Field(description="微信临时登录凭证")
    user_info: Optional[Dict[str, Any]] = Field(default=None, description="用户信息")
    ip_address: Optional[str] = Field(default="127.0.0.1", description="IP地址")
    user_agent: Optional[str] = Field(default="Unknown", description="用户代理") 
    from_code:Optional[str]=Field(default='',description='来源码')


# -------------------- 微信用户信息更新与查询模型 --------------------

class WechatUserUpdateRequest(CamelCaseModel):
    """微信用户信息更新请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "nickname": "新昵称",
                "avatar_url": "https://example.com/new_avatar.jpg",
                "gender": 1,
                "country": "China",
                "province": "Beijing",
                "city": "Beijing",
                "language": "zh_CN"
            }
        }
    )
    
    nickname: Optional[str] = Field(default=None, description="用户昵称")
    avatar_url: Optional[str] = Field(default=None, description="用户头像")
    gender: Optional[int] = Field(default=None, description="用户性别")
    country: Optional[str] = Field(default=None, description="用户所在国家")
    province: Optional[str] = Field(default=None, description="用户所在省份")
    city: Optional[str] = Field(default=None, description="用户所在城市")
    language: Optional[str] = Field(default=None, description="用户的语言")