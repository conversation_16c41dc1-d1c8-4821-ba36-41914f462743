# -------------------- 角色模型 --------------------

from datetime import datetime
from typing import List, Optional, Protocol
from pydantic import BaseModel, ConfigDict, Field, field_validator
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel

# 类型定义
class RoleProtocol(Protocol):
    """
    角色模型协议，定义角色对象必须实现的属性和方法
    """
    id: int
    name: str
    description: Optional[str]
    permissions: List[str]

class RoleBase(CamelCaseModel):
    """角色基本信息"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "admin",
                "description": "管理员角色"
            }
        }
    )
    
    name: str = Field(..., max_length=100, description="角色名称")
    description: Optional[str] = Field(None, max_length=500, description="角色描述")


class RoleCreate(RoleBase):
    """角色创建请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "admin",
                "description": "管理员角色",
                "permissions": ["user:read", "user:write"]
            }
        }
    )
    
    permissions: List[int] = Field([], description="权限ID列表")


class RoleUpdate(CamelCaseModel):
    """角色更新请求"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "admin",
                "description": "管理员角色",
                "permissions": ["user:read", "user:write"]
            }
        }
    )
    
    name: Optional[str] = Field(None, max_length=100, description="角色名称")
    description: Optional[str] = Field(None, max_length=500, description="角色描述")
    permissions: Optional[List[int]] = Field(None, description="权限ID列表")


class RoleResponse(RoleBase):
    """角色响应模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "admin",
                "description": "管理员角色",
                "permissions": ["user:read", "user:write"],
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(..., description="角色ID")
    name: str = Field(..., description="角色名称")
    description: Optional[str] = Field(..., description="角色描述")
    permissions: List[str] = Field(default_factory=list, description="权限列表")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    
    @field_validator("permissions", mode="before")
    @classmethod
    def convert_permissions_dict_to_list(cls, v):
        """将权限字典转换为列表"""
        if isinstance(v, dict):
            return [k for k, v in v.items() if v]
        return v


class RoleQueryParams(BaseModel):
    """角色查询参数"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
    )
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")
    search_term: str = Field(default="", description="搜索关键词")
    order_by: str = Field(default="created_at", description="排序字段")
    order_desc: bool = Field(default=True, description="是否降序")


class RoleListResponse(PaginatedResponse[RoleResponse]):
    """角色列表响应模型，继承自分页基类"""
    pass # 字段已在 PaginatedResponse 中定义
