"""
角色服务模块，提供面向对象形式的角色管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""
from typing import List, Optional, Dict, Any


from sqlalchemy import or_
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis

from svc.apps.auth.schemas.role import RoleCreate, RoleQueryParams, RoleUpdate
from svc.core.services import BaseService
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services.result import Result

from svc.apps.auth.models.role import Role
from svc.apps.auth.repositories import RoleRepository, UserRepository
from svc.apps.auth.schemas import (
    RoleResponse,
    RoleListResponse
)

# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

from fastapi_events.dispatcher import dispatch
from svc.core.events.event_names import AUTH_ROLE_CREATED, AUTH_ROLE_UPDATED, AUTH_ROLE_DELETED


class RoleService(BaseService[Role, Result[RoleResponse]]):
    """角色服务类，提供角色管理功能
    
    该服务类负责：
    1. 角色的创建、查询、更新和删除
    2. 角色相关事件的触发
    
    服务类依赖RoleRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "role"
    
    def __init__(
        self, 
        redis: Optional[Redis] = None,
        role_repo: Optional[RoleRepository] = None,
        user_repo: Optional[UserRepository] = None
    ):
        """初始化角色服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端，用于缓存
            role_repo: 角色仓库实例，不提供则创建新实例
            user_repo: 用户仓库实例，不提供则创建新实例
        """
        super().__init__(redis)
        self.role_repo = role_repo 
        self.user_repo = user_repo
    
    async def get_resource_by_id(self, role_id: int) -> Optional[Role]:
        """获取指定ID的角色资源
        
        Args:
            role_id: 角色ID
            
        Returns:
            Optional[Role]: 角色对象，不存在时返回None
        """
        return await self.role_repo.get_by_id( role_id)
    
    async def get_role(self, role_id: int) -> Result[RoleResponse]:
        """获取角色
        
        Args:
            role_id: 角色ID
            
        Returns:
            Result[RoleResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取角色: id={role_id}")
            
            # 先尝试从缓存获取
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(role_id)
                    cached_role = await self.get_cached_resource(
                        cache_key,
                        lambda data: RoleResponse.model_validate(data)
                    )
                    if cached_role:
                        self.logger.debug(f"从缓存获取到角色: id={role_id}")
                        return self.create_success_result(cached_role)
                except Exception as e:
                    self.logger.warning(f"从缓存获取角色失败: id={role_id}, 错误={str(e)}")
            
            role = await self.get_resource_by_id(role_id)
            if not role:
                self.logger.warning(f"角色 {role_id} 不存在")
                return self.resource_not_found_result(role_id)
            
            # 获取角色的权限列表
            permissions = await self.role_repo.get_permission_names( role)
            
            # 创建一个包含权限的字典，用于模型验证
            role_dict = {
                "id": role.id,
                "name": role.name,
                "description": role.description,
                "permissions": list(permissions),
                "created_at": role.created_at,
                "updated_at": role.updated_at
            }
            
            # 构建角色响应
            role_response = RoleResponse.model_validate(role_dict)
            
            # 缓存角色
            await self._cache_role(role.id, role_response)
            
            return self.create_success_result(role_response)
        except Exception as e:
            self.logger.error(f"获取角色失败: id={role_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.ROLE_NOT_FOUND,
                error_message=f"获取角色失败: {str(e)}"
            )
    
    async def get_roles(self, params: RoleQueryParams) -> Result[RoleListResponse]:
        """获取角色列表 (所有角色，分页)
        
        Args:
            params: 查询参数 (包含分页、搜索、排序)
            
        Returns:
            Result[RoleListResponse]: 包含分页信息的角色列表结果
        """
        try:
            self.logger.info(f"获取角色列表: page_num={params.page_num}, page_size={params.page_size}, search='{params.search_term}'")
            
            filters = {}
            if params.search_term:
                search_like = f"%{params.search_term}%"
                filters['search'] = or_(Role.name.ilike(search_like), Role.description.ilike(search_like))

            roles, total = await self.role_repo.get_paginated(
                
                page=params.page_num,
                page_size=params.page_size,
                order_by=params.order_by,
                order_direction="desc" if params.order_desc else "asc",
                **filters # Pass the constructed filters
            )
            
            # 构建角色响应列表
            role_responses = []
            for role in roles:
                permissions = await self.role_repo.get_permission_names( role)
                role_dict = {
                    "id": role.id,
                    "name": role.name,
                    "description": role.description,
                    "permissions": list(permissions),
                    "created_at": role.created_at,
                    "updated_at": role.updated_at
                }
                role_response = RoleResponse.model_validate(role_dict)
                role_responses.append(role_response)
            
            # 计算总页数
            total_pages = (total + params.page_size - 1) // params.page_size if params.page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = RoleListResponse(
                items=role_responses,
                total=total,
                page_num=params.page_num,
                page_size=params.page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取角色列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.ROLE_NOT_FOUND, # Consider a more generic error code like OPERATION_FAILED
                error_message=f"获取角色列表失败: {str(e)}"
            )
    
    async def get_current_user_roles(self, user_id: int) -> Result[List[RoleResponse]]:
        """获取指定用户的角色列表
        
        Args:
            user_id: 用户ID
            
        Returns:
            Result[List[RoleResponse]]: 结果对象，包含角色响应列表
        """
        try:
            self.logger.info(f"获取用户角色: user_id={user_id}")
            
            # 获取用户对象 (需要确保 user_repo 存在)
            if not self.user_repo:
                 # This should ideally not happen if initialized correctly
                 raise ValueError("UserRepository dependency not available in RoleService")
                 
            user = await self.user_repo.get_by_id( user_id)
            
            if not user:
                self.logger.warning(f"用户不存在: {user_id}")
                return self.create_error_result(
                    error_code=ErrorCode.USER_NOT_FOUND,
                    error_message=f"用户不存在: {user_id}"
                )
            
            # 获取用户角色
            roles = await self.user_repo.get_roles( user)
            role_responses = []
            for role in roles:
                # 获取角色的权限列表
                permissions = await self.role_repo.get_permission_names( role)
                # 创建一个包含权限的字典，用于模型验证
                role_dict = {
                    "id": role.id,
                    "name": role.name,
                    "description": role.description,
                    "permissions": list(permissions),
                    "created_at": role.created_at,
                    "updated_at": role.updated_at
                }
                role_response = RoleResponse.model_validate(role_dict)
                role_responses.append(role_response)

            # 返回角色响应列表
            return self.create_success_result(role_responses)
        except Exception as e:
            self.logger.error(f"获取用户角色失败: user_id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取用户角色失败: {str(e)}"
            )
    
    async def create_role(self, params: RoleCreate) -> Result:
        """创建角色
        
        Args:
            params: 角色创建参数
            
        Returns:
            Result[RoleResponse]: 结果对象
        """
        try:
            self.logger.info(f"创建角色: name={params.role_data.name}")
            
            # 检查角色名是否已存在
            existing_role = await self.role_repo.get_by_name( params.role_data.name)
            if existing_role:
                self.logger.warning(f"角色名已存在: {params.role_data.name}")
                return self.create_error_result(
                    error_code=ErrorCode.ROLE_EXISTS,
                    error_message=f"角色名 {params.role_data.name} 已存在"
                )
            
            # 创建角色
            role = await self.role_repo.create(
                
                name=params.role_data.name,
                description=params.role_data.description,
                permissions=params.role_data.permissions
            )
            
            # 构建角色响应
            role_response = RoleResponse.model_validate(role.to_dict())
            
            # 缓存角色
            await self._cache_role(role.id, role_response)
            
            # 触发角色创建事件
            event_data = {
                "role_id": role.id,
                "role_name": role.name,
                "description": role.description,
                "permissions": params.role_data.permissions, # 创建时传入的权限
                "created_by_user_id": params.user_id # 创建者ID
            }
            dispatch(AUTH_ROLE_CREATED, payload=event_data)
            
            self.logger.info(f"角色创建成功: id={role.id}, name={role.name}")
            return self.create_success_result(role_response)
        except Exception as e:
            self.logger.error(f"创建角色失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.ROLE_CREATE_FAILED,
                error_message=f"创建角色失败: {str(e)}"
            )
    
    async def update_role(self, user_id: int, role_id: int, params: RoleUpdate) -> Result:
        """更新角色信息
        
        Args:
            params: 角色更新参数
            
        Returns:
            Result: 结果对象
        """
        try:
            self.logger.info(f"更新角色: id={role_id}")
            
            # 获取角色
            role = await self.role_repo.get_by_id( role_id)
            if not role:
                self.logger.warning(f"角色不存在: {role_id}")
                return self.resource_not_found_result(role_id)
            
            # 如果更新角色名，检查是否已存在
            if params.name is not None and params.name != role.name:
                existing_role = await self.role_repo.get_by_name( params.name)
                if existing_role and existing_role.id != role.id:
                    self.logger.warning(f"角色名已存在: {params.name}")
                    return self.create_error_result(
                        error_code=ErrorCode.ROLE_EXISTS,
                        error_message=f"角色名 {params.name} 已被使用"
                    )
            
            # 更新角色
            role = await self.role_repo.update(
                
                role,
                name=params.name,
                description=params.description,
                permissions=params.permissions
            )
            
            # 构建角色响应
            role_response = RoleResponse.model_validate(role.to_dict())
            
            # 更新缓存
            await self._cache_role(role.id, role_response)
            
            # 触发角色更新事件
            event_data = {
                "role_id": role.id,
                "role_name": role.name,
                "updated_fields": list(params.model_dump(exclude_unset=True).keys()),
                "old_values": role.to_dict(),
                "permissions": list(params.permissions), # 更新后的权限列表
                "updated_by_user_id": user_id # 更新者ID
            }
            dispatch(AUTH_ROLE_UPDATED, payload=event_data)
            
            self.logger.info(f"角色 {role.id} ({role.name}) 更新成功")
            return self.create_success_result(role_response)
        except Exception as e:
            self.logger.error(f"更新角色失败: id={role_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.ROLE_UPDATE_FAILED,
                error_message=f"更新角色失败: {str(e)}"
            )
    
    async def delete_role(self, user_id: int, role_id: int) -> Result:
        """删除角色
        
        Args:
            params: 角色删除参数
            
        Returns:
            Result: 结果对象
        """
        try:
            self.logger.info(f"删除角色: id={role_id}")
            
            # 获取角色
            role = await self.role_repo.get_by_id( role_id)
            if not role:
                self.logger.warning(f"角色不存在: {role_id}")
                return self.resource_not_found_result(role_id)
            
            # 检查角色是否有关联用户 (移至事件处理器或前置检查逻辑)
            # users_count = await self.role_repo.count_users( role)
            # if users_count > 0:
            #     self.logger.warning(f"角色 {role.name} 仍有 {users_count} 个关联用户，无法删除")
            #     return self.create_error_result(
            #         error_code=ErrorCode.ROLE_HAS_USERS,
            #         error_message=f"角色 {role.name} 仍有 {users_count} 个关联用户，请先移除关联或使用force=true强制删除"
            #     )
            
            # 删除角色
            await self.role_repo.delete( role)
            
            # 触发角色删除事件
            event_data = {
                "role_id": role_id,
                "role_name": role.name, # 记录被删除的角色名
                "deleted_by_user_id": user_id # 删除者ID
            }
            dispatch(AUTH_ROLE_DELETED, payload=event_data)
            
            self.logger.info(f"角色 {role_id} 已成功删除")
            return self.create_success_result({"message": f"角色 {role_id} 已成功删除"})
        except Exception as e:
            self.logger.error(f"删除角色失败: id={role_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.ROLE_DELETE_FAILED,
                error_message=f"删除角色失败: {str(e)}"
            )
    
    async def _cache_role(self, role_id: int, role_response: RoleResponse) -> None:
        """缓存角色信息
        
        Args:
            role_id: 角色ID
            role_response: 角色响应对象
        """
        if self.redis:
            try:
                cache_key = self._get_resource_cache_key(role_id)
                await self.cache_resource(cache_key, role_response.model_dump(), CACHE_TTL)
                self.logger.debug(f"角色缓存成功: id={role_id}")
            except Exception as e:
                self.logger.warning(f"缓存角色失败: id={role_id}, 错误={str(e)}")
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"role:{resource_id}"