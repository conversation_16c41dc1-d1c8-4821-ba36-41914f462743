"""
用户服务模块，提供面向对象形式的用户管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""

from typing import Optional, List, Dict, Any, Union

from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis


from svc.apps.auth.schemas.role import RoleBase
from svc.apps.auth.schemas.user import UserCreate
from svc.core.services import BaseService
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services.result import Result

from svc.apps.auth.models.user import User
from svc.apps.auth.repositories import UserRepository, RoleRepository
from svc.apps.auth.schemas import (
    GetUsersParams,
    CreateUserParams,
    UpdateUserParams,
    DeleteUserParams,
    AssignRoleParams,
    RemoveRoleParams,
    UserResponse,
    UserWithRoles,
    UserListResponse
)
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo

# 缓存过期时间（秒）
CACHE_TTL = 3600  # 1小时

# Add new event imports
from fastapi_events.dispatcher import dispatch
from svc.core.events.event_names import (
    AUTH_USER_REGISTERED, 
    AUTH_USER_UPDATED, 
    AUTH_USER_DELETED, 
    AUTH_USER_ROLE_ASSIGNED, 
    AUTH_USER_ROLE_REMOVED
)


class UserService(BaseService[User, Result[UserResponse]]):
    """用户服务类，提供用户管理功能
    
    该服务类负责：
    1. 用户的创建、查询、更新和删除
    2. 用户角色的分配和移除
    3. 用户相关事件的触发
    
    服务类依赖UserRepository和RoleRepository进行数据访问，
    实现业务逻辑与数据访问的分离。
    """
    
    # 设置资源类型名称
    resource_type = "user"
    
    def __init__(
        self,  
        redis: Optional[Redis] = None,
        user_repo: Optional[UserRepository] = None,
        role_repo: Optional[RoleRepository] = None
    ):
        """初始化用户服务
        
        Args:
            db: 数据库会话
            redis: Redis客户端，用于缓存和分布式锁
            user_repo: 用户仓库实例，不提供则创建新实例
            role_repo: 角色仓库实例，不提供则创建新实例
        """
        super().__init__(redis)
        self.user_repo = user_repo 
        self.role_repo = role_repo 
    
    async def get_resource_by_id(self, user_id: int) -> Optional[User]:
        """获取指定ID的用户资源
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[User]: 用户对象，不存在时返回None
        """
        return await self.user_repo.get_by_id( user_id)
    
    async def get_user(self, user_id: int) -> Result[UserWithRoles]:
        """获取用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            Result[UserResponse]: 结果对象
        """
        try:
            self.logger.info(f"获取用户: id={user_id}")
            
            # 先尝试从缓存获取
            if self.redis:
                try:
                    cache_key = self._get_resource_cache_key(user_id)
                    cached_user = await self.get_cached_resource(
                        cache_key,
                        lambda data: UserResponse.model_validate(data)
                    )
                    if cached_user:
                        self.logger.debug(f"从缓存获取到用户: id={user_id}")
                        return self.create_success_result(cached_user)
                except Exception as e:
                    self.logger.warning(f"从缓存获取用户失败: id={user_id}, 错误={str(e)}")
            
            user = await self.get_resource_by_id(user_id)
            if not user:
                self.logger.warning(f"用户 {user_id} 不存在")
                return self.resource_not_found_result(user_id)
            
            # 获取用户角色，使用仓库类而不是直接调用模型类方法
            roles = await self.user_repo.get_roles( user)
            
            # 构建用户响应
            user_response = UserWithRoles.model_validate(user.to_dict())
            
            user_response.roles = [
                RoleBase(name=role.name, description=role.description)
                for role in roles
            ]
            
            # 缓存用户
            await self._cache_user(user_id, user_response)
            
            return self.create_success_result(user_response)
        except Exception as e:
            self.logger.error(f"获取用户失败: id={user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.USER_NOT_FOUND,
                error_message=f"获取用户失败: {str(e)}"
            )
    
    async def get_users(self, params: GetUsersParams) -> Result[UserListResponse]:
        """获取用户列表
        
        Args:
            params: 查询参数
            
        Returns:
            Result[UserListResponse]: 包含分页信息的用户列表结果
        """
        try:
            self.logger.info(f"获取用户列表: page_num={params.page_num}, page_size={params.page_size}")
            
            page_size = params.page_size
            skip = (params.page_num - 1) * page_size
            
            # 使用仓库类获取用户列表和总数
            users, total = await self.user_repo.get_users(
                
                skip=skip, # 使用计算得到的 skip
                limit=page_size, # 使用 page_size 作为 limit
                is_active=params.is_active,
                search_term=params.search_term,
                order_by=params.order_by,
                order_desc=params.order_desc
            )
            
            # 构建用户响应列表
            user_responses = []
            for user in users:
                user_response = UserResponse.model_validate(user.to_dict())
                user_responses.append(user_response)

            
            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0
            
            # 构建分页响应
            paginated_response = UserListResponse(
                items=user_responses,
                total=total,
                page_num=params.page_num,
                page_size=page_size,
                page_count=total_pages
            )
            
            return self.create_success_result(paginated_response)
        except Exception as e:
            self.logger.error(f"获取用户列表失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.USER_NOT_FOUND,
                error_message=f"获取用户列表失败: {str(e)}"
            )
    
    async def create_user(self, params: CreateUserParams) -> Result[UserResponse]:
        """创建用户
        
        Args:
            params: 用户创建参数
            
        Returns:
            Result[UserResponse]: 结果对象
        """
        try:
            self.logger.info(f"创建用户: email={params.user_data.email}")
            
            # 检查邮箱是否已存在
            existing_user = await self.user_repo.get_by_email( params.user_data.email)
            if existing_user:
                self.logger.warning(f"用户创建失败: 邮箱 {params.user_data.email} 已存在")
                return self.create_error_result(
                    error_code=ErrorCode.INVALID_EMAIL,
                    error_message=f"邮箱 {params.user_data.email} 已被使用"
                )
            
            # 如果提供了用户名，检查用户名是否已存在
            if params.user_data.username:
                existing_user = await self.user_repo.get_by_username( params.user_data.username)
                if existing_user:
                    self.logger.warning(f"用户名已存在: {params.user_data.username}")
                    return self.create_error_result(
                        result_code=ErrorCode.USER_USERNAME_EXISTS,
                        result_msg=f"用户名 {params.user_data.username} 已被使用"
                    )
            
            # 创建用户
            user = await self.user_repo.create(
                
                email=params.user_data.email,
                password=params.user_data.password,
                fullname=params.user_data.fullname,
                is_superuser=params.user_data.is_superuser,
                username=params.user_data.username,
            )
            # 注册成功后重置登录失败计数和锁定状态
            user.failed_login_attempts = 0
            user.locked_until = None
            await self.user_repo.save(user)
            
            # 如果指定了角色，分配角色
            if params.user_data.roles:
                for role_name in params.user_data.roles:
                    role = await self.role_repo.get_by_name(role_name)
                    if role:
                        await self.user_repo.add_role( user, role)
            
            # 获取用户角色
            roles = await self.user_repo.get_roles( user)
            
            # 构建用户响应
            user_response = UserWithRoles.model_validate(user.to_dict())
            
            # 将角色对象转换为角色名称列表(UserResponse与UserWithRoles不同，只需要角色名称)
            user_response.roles = [RoleBase(name=role.name, description=role.description) for role in roles]
            
            # 缓存用户
            await self._cache_user(user.id, user_response)
            
            # 处理邀请人ID（如果提供）
            if params.user_data.inviter_id:
                # 验证邀请人ID是否有效
                try:
                    inviter_id = int(params.user_data.inviter_id)
                    # 查询邀请人是否存在
                    inviter = await self.user_repo.get_by_id( inviter_id)
                    if inviter:
                        # 记录邀请信息
                        invitation_data = {
                            "inviter_id": inviter_id,
                            "invitee_id": user.id,
                            "client_ip": params.client_ip,
                            "client_device": params.client_device
                        }
                        self.logger.info(f"处理新用户 {user.id} 的邀请关系: 邀请人ID={inviter_id}")
                    else:
                        self.logger.warning(f"邀请人ID无效: {params.user_data.inviter_id}，该用户不存在")
                        invitation_data = None
                except ValueError:
                    self.logger.warning(f"邀请人ID格式无效: {params.user_data.inviter_id}")
                    invitation_data = None
            else:
                invitation_data = None
            
            # 触发用户创建事件
            payload = {
                "user_id": user.id,
                "email": user.email,
                "fullname": user.fullname,
                "username": user.username,
                "is_superuser": user.is_superuser,
                "roles": [role.name for role in roles],
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "inviter_id": params.user_data.inviter_id,
                "client_ip": params.client_ip,
                "client_device": params.client_device
            }
            dispatch(AUTH_USER_REGISTERED, payload=payload)
            
            self.logger.info(f"用户创建成功: id={user.id}, email={user.email}")
            return self.create_success_result(user_response)
        except Exception as e:
            self.logger.error(f"创建用户失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"创建用户失败: {str(e)}"
            )
    
    async def update_user(self, params: UpdateUserParams,allowed_by_admin:bool=False) -> Result[UserResponse]:
        """更新用户信息
        
        Args:
            params: 用户更新参数
            
        Returns:
            Result[UserResponse]: 结果对象
        """
        try:
            self.logger.info(f"更新用户: id={params.user_id}")
            
            # 获取用户
            user = await self.user_repo.get_by_id( params.user_id)
            if not user:
                self.logger.warning(f"用户不存在: {params.user_id}")
                return self.resource_not_found_result(params.user_id)
            
            # 如果更新邮箱，检查是否已存在
            if params.user_data.email is not None and params.user_data.email != user.email:
                existing_user = await self.user_repo.get_by_email( params.user_data.email)
                if existing_user and existing_user.id != user.id:
                    self.logger.warning(f"用户邮箱已存在: {params.user_data.email}")
                    return self.create_error_result(
                        error_code=ErrorCode.INVALID_EMAIL,
                        error_message=f"邮箱 {params.user_data.email} 已被使用"
                    )
            
            # 如果更新用户名，检查是否已存在
            if params.user_data.username is not None and params.user_data.username != user.username:
                existing_user = await self.user_repo.get_by_username( params.user_data.username)
                if existing_user and existing_user.id != user.id:
                    self.logger.warning(f"用户名已存在: {params.user_data.username}")
                    return self.create_error_result(
                        error_code=ErrorCode.USER_EXISTS,
                        error_message=f"用户名 {params.user_data.username} 已被使用"
                    )
            
            # 更新用户
            user = await self.user_repo.update(
                
                user,
                email=params.user_data.email,
                fullname=params.user_data.fullname,
                password=params.user_data.password,
                is_active=params.user_data.is_active,
                is_superuser=params.user_data.is_superuser,
                username=params.user_data.username,
            )
            
            # 获取用户角色
            roles = await self.user_repo.get_roles( user)
            
            # 构建用户响应
            user_response = UserWithRoles.model_validate(user.to_dict())

            user_response.roles = [RoleBase(name=role.name, description=role.description) for role in roles]
            
            # 更新缓存
            await self._cache_user(user.id, user_response)
            
            # 触发用户更新事件
            event_data = {
                "user_id": user.id,
                "updated_fields": list(params.user_data.dict().keys()),
                "old_values": {k: getattr(user, k) for k in params.user_data.dict() if k != 'password'},
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
                "updater_id": params.updater_id # 更新者ID
            }
            dispatch(AUTH_USER_UPDATED, payload=event_data)
            
            self.logger.info(f"用户 {user.id} 更新成功")
            return self.create_success_result(user_response)
        except Exception as e:
            self.logger.error(f"更新用户失败: id={params.user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"更新用户失败: {str(e)}"
            )
    
    async def delete_user(self, params: DeleteUserParams) -> Result[Dict[str, Any]]:
        """删除用户
        
        Args:
            params: 用户删除参数
            
        Returns:
            Result[Dict[str, Any]]: 结果对象
        """
        try:
            self.logger.info(f"删除用户: id={params.user_id}")
            
            # 获取用户
            user = await self.user_repo.get_by_id( params.user_id)
            if not user:
                self.logger.warning(f"用户不存在: {params.user_id}")
                return self.resource_not_found_result(params.user_id)
            
            # 删除用户
            await self.user_repo.delete(user)
            
            # 清除缓存
            await self.invalidate_resource_cache(params.user_id)
            
            # 触发用户删除事件
            event_data = {
                "user_id": user.id,
                "email": user.email, # 记录被删除用户的邮箱
                "deleted_at": get_utc_now_without_tzinfo().isoformat(),
                "deleter_id": params.executor_id # 删除者ID
            }
            dispatch(AUTH_USER_DELETED, payload=event_data)
            
            self.logger.info(f"用户 {params.user_id} 已成功删除")
            return self.create_success_result({"message": f"用户 {params.user_id} 已成功删除"})
        except Exception as e:
            self.logger.error(f"删除用户失败: id={params.user_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"删除用户失败: {str(e)}"
            )
    
    async def assign_role(self, params: AssignRoleParams) -> Result[Dict[str, Any]]:
        """为用户分配角色
        
        Args:
            params: 角色分配参数
            
        Returns:
            Result[Dict[str, Any]]: 结果对象
        """
        try:
            self.logger.info(f"为用户分配角色: user_id={params.user_id}")
            
            # 获取用户
            user = await self.user_repo.get_by_id( params.user_id)
            if not user:
                self.logger.warning(f"用户不存在: {params.user_id}")
                return self.resource_not_found_result(params.user_id)
            
            # 获取角色
            role = await self.role_repo.get_by_id( params.role_id)
            if not role:
                self.logger.warning(f"角色不存在: {role.name}")
                return self.create_error_result(
                    error_code=ErrorCode.ROLE_NOT_FOUND,
                    error_message=f"角色 {role.name} 不存在"
                )
            
            # 检查用户是否已有该角色
            roles = await self.user_repo.get_roles( user)
            if any(r.id == role.id for r in roles):
                self.logger.info(f"用户已有该角色: user_id={params.user_id}, role_name={role.name}")
                return self.create_success_result({"message": f"用户已有角色 {role.name}"})
            
            # 分配角色
            await self.user_repo.add_role( user, role)
            
            # 更新缓存
            # await self.invalidate_resource_cache(params.user_id) # [Refactor] 移至事件处理器
            
            # 触发角色分配事件
            event_data = {
                "user_id": user.id,
                "role_name": role.name,
                "assigned_by_user_id": params.executor_id # 分配者ID
            }
            dispatch(AUTH_USER_ROLE_ASSIGNED, payload=event_data)
            
            self.logger.info(f"为用户 {user.id} 分配角色 {role.name} 成功")
            return self.create_success_result({"message": f"角色 {role.name} 已成功分配给用户 {user.id}"})
        except Exception as e:
            self.logger.error(f"分配角色失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"分配角色失败: {str(e)}"
            )
    
    async def remove_role(self, params: RemoveRoleParams) -> Result[Dict[str, Any]]:
        """移除用户角色
        
        Args:
            params: 角色移除参数
            
        Returns:
            Result[Dict[str, Any]]: 结果对象
        """
        try:
            self.logger.info(f"移除用户角色: user_id={params.user_id}")
            
            # 获取用户
            user = await self.user_repo.get_by_id( params.user_id)
            if not user:
                self.logger.warning(f"用户不存在: {params.user_id}")
                return self.resource_not_found_result(params.user_id)
            
            # 获取角色
            role = await self.role_repo.get_by_id( params.role_id)
            if not role:
                self.logger.warning(f"角色不存在: {role.name}")
                return self.create_error_result(
                    error_code=ErrorCode.ROLE_NOT_FOUND,
                    error_message=f"角色 {role.name} 不存在"
                )
            
            # 检查用户是否有该角色
            roles = await self.user_repo.get_roles( user)
            if not any(r.id == role.id for r in roles):
                self.logger.info(f"用户没有该角色: user_id={params.user_id}, role_name={role.name}")
                return self.create_success_result({"message": f"用户没有角色 {role.name}"})
            
            # 移除角色
            await self.user_repo.remove_role( user, role)
            
            # 更新缓存
            # await self.invalidate_resource_cache(params.user_id) # [Refactor] 移至事件处理器
            
            # 触发角色移除事件
            event_data = {
                "user_id": user.id,
                "role_name": role.name,
                "removed_by_user_id": params.executor_id # 移除者ID
            }
            dispatch(AUTH_USER_ROLE_REMOVED, payload=event_data)
            
            self.logger.info(f"从用户 {user.id} 移除角色 {role.name} 成功")
            return self.create_success_result({"message": f"角色 {role.name} 已成功从用户 {user.id} 移除"})
        except Exception as e:
            self.logger.error(f"移除角色失败: 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"移除角色失败: {str(e)}"
            )
    
    async def _cache_user(self, user_id: int, user_response: Union[UserResponse, UserWithRoles]) -> None:
        """缓存用户信息
        
        Args:
            user_id: 用户ID
            user_response: 用户响应对象，可以是UserResponse或UserWithRoles
        """
        if self.redis:
            try:
                cache_key = self._get_resource_cache_key(user_id)
                await self.cache_resource(cache_key, user_response.model_dump(), CACHE_TTL)
                self.logger.debug(f"用户缓存成功: id={user_id}")
            except Exception as e:
                self.logger.warning(f"缓存用户失败: id={user_id}, 错误={str(e)}")
    
    def _get_resource_cache_key(self, resource_id: int) -> str:
        """获取资源缓存键
        
        Args:
            resource_id: 资源ID
            
        Returns:
            str: 缓存键
        """
        return f"user:{resource_id}"

    async def invalidate_resource_cache(self, resource_id: int) -> None:
        """清除资源缓存
        
        Args:
            resource_id: 资源ID
        """
        if self.redis:
            try:
                cache_key = self._get_resource_cache_key(resource_id)
                await self.redis.delete(cache_key)
                self.logger.debug(f"用户缓存已清除: id={resource_id}")
            except Exception as e:
                self.logger.warning(f"清除用户缓存失败: id={resource_id}, 错误={str(e)}") 