# 认证与权限管理模块

基于产品模块模板创建的认证与权限管理模块，提供用户、角色、权限、认证等完整的管理功能。

## 模块结构

```
svc/apps/auth/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── user.py                # 用户模型
│   ├── role.py                # 角色模型
│   ├── user_role.py           # 用户角色关联模型
│   ├── permission.py          # 权限模型
│   └── wechat_user.py         # 微信用户模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   ├── user.py                # 用户模式
│   ├── role.py                # 角色模式
│   ├── auth.py                # 认证模式
│   └── wechat.py              # 微信用户模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   ├── user.py                # 用户仓库
│   ├── role.py                # 角色仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── user.py                # 用户服务
│   ├── role.py                # 角色服务
│   └── auth.py                # 认证服务
├── routers/                    # API路由
│   ├── __init__.py
│   ├── users.py               # 用户路由
│   ├── roles.py               # 角色路由
│   ├── auth.py                # 认证路由
│   └── wechat.py              # 微信用户路由
├── events/                     # 事件处理器
│   ├── __init__.py
│   ├── user_handlers.py       # 用户事件
│   ├── role_handlers.py       # 角色事件
│   └── auth_handlers.py       # 认证事件
└── utils/                      # 工具函数
    └── __init__.py
```

## 核心功能

### 1. 用户管理 (User)

-   用户的注册、登录、查询、更新、删除
-   用户状态管理（激活、禁用、注销）
-   用户角色分配
-   用户信息与安全设置

### 2. 角色管理 (Role)

-   角色的创建、查询、更新、删除
-   角色权限分配
-   角色状态管理

### 3. 权限管理 (Permission)

-   权限的创建、分配、回收
-   权限分组与层级管理
-   权限校验与控制

### 4. 认证管理 (Auth)

-   支持多种认证方式（账号密码、短信、微信等）
-   Token 生成与校验
-   登录日志与安全监控

## API 端点

### 用户 API

-   `GET /users/` - 获取用户列表
-   `GET /users/{id}` - 获取用户详情
-   `POST /users/` - 创建用户
-   `PUT /users/{id}` - 更新用户
-   `DELETE /users/{id}` - 删除用户

### 角色 API

-   `GET /roles/` - 获取角色列表
-   `GET /roles/{id}` - 获取角色详情
-   `POST /roles/` - 创建角色
-   `PUT /roles/{id}` - 更新角色
-   `DELETE /roles/{id}` - 删除角色

### 权限 API

-   `GET /permissions/` - 获取权限列表
-   `POST /permissions/` - 创建权限
-   `PUT /permissions/{id}` - 更新权限
-   `DELETE /permissions/{id}` - 删除权限

### 认证 API

-   `POST /auth/login` - 用户登录
-   `POST /auth/logout` - 用户登出
-   `POST /auth/refresh` - 刷新 Token
-   `POST /auth/wechat_login` - 微信登录

## 事件系统

模块集成了事件系统，支持以下事件：

### 用户事件

-   `auth:user:created` - 用户创建
-   `auth:user:updated` - 用户更新
-   `auth:user:deleted` - 用户删除
-   `auth:user:status_changed` - 用户状态变更

### 角色事件

-   `auth:role:created` - 角色创建
-   `auth:role:updated` - 角色更新
-   `auth:role:deleted` - 角色删除

### 认证事件

-   `auth:login:success` - 登录成功
-   `auth:login:failed` - 登录失败
-   `auth:logout` - 用户登出

## 缓存策略

-   用户信息缓存：1 小时
-   角色信息缓存：1 小时
-   权限信息缓存：1 小时

## 权限控制

模块集成了基于角色的权限控制：

-   `user:read` - 用户查看权限
-   `user:create` - 用户创建权限
-   `user:update` - 用户更新权限
-   `user:delete` - 用户删除权限
-   `role:*` - 角色相关权限
-   `permission:*` - 权限相关权限

## 数据库迁移

```sql
-- 创建用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    status VARCHAR(20) DEFAULT 'active',
    last_login DATETIME,
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active',
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建权限表
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    group_name VARCHAR(100),
    parent_id BIGINT,
    status VARCHAR(20) DEFAULT 'active',
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建用户角色关联表
CREATE TABLE user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 注意事项

1. 用户、角色、权限等数据变更时需及时更新缓存。
2. 事件处理器异常不影响主业务流程。
3. 权限校验和数据一致性需重点关注。
4. 认证相关接口需防止暴力破解和安全攻击。
