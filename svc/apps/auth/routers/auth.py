"""
认证相关的API路由
"""
from typing import Any, Dict

from fastapi import APIRouter, Depends, Request
from fastapi.security import OAuth2PasswordRequestForm

from svc.core.services.result import Result
from svc.apps.auth.schemas import (
    UserCreate, 
    UserResponse, 
    LoginParams,
    PasswordResetRequest,
    PasswordResetParams,
    CreateUserParams
)
from svc.apps.auth.services import (
    AuthService,
    UserService
)
from svc.apps.auth.dependencies import (
    get_auth_service,
    get_user_service
)
from svc.core.exceptions import (
    handle_route_errors,
    AUTH_ERROR_MAPPING
)

router = APIRouter(tags=["认证"])


@router.post("/login")
@handle_route_errors(AUTH_ERROR_MAPPING)
async def login_api(
    request: Request,
    form_data:  OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service),
) -> Any:
    """用户登录接口"""
    # 创建登录参数，从请求中获取IP和用户代理
    client_ip = request.client.host if request.client else "127.0.0.1"
    user_agent = request.headers.get("user-agent", "Unknown")
    
    login_params = LoginParams(
        email=form_data.username,
        password=form_data.password,
        ip_address=client_ip,
        user_agent=user_agent
    )
    
    # 调用登录服务
    result = await auth_service.login(login_params)
    # 统一对外提示
    if not result.is_success and result.result_code in [
        "3000",  # USER_NOT_FOUND
        "3005",  # INVALID_PASSWORD
        "3002",  # USER_INACTIVE
        "2004"   # ACCOUNT_LOCKED
    ]:
        request.app.logger.warning(
            f"登录失败: email={login_params.email}, code={result.result_code}, 原因={result.result_msg}, ip={login_params.ip_address}, ua={login_params.user_agent}"
        )
        return {
            "code": "2003",  # LOGIN_FAILED
            "message": "邮箱或密码不正确",
            "data": None
        }
    # 如果登录成功，确保返回格式符合OAuth2标准
    if result.is_success and result.data and "token_data" in result.data:
        token_data = result.data["token_data"]
        # 根据OAuth2标准格式化响应
        return {
                "access_token": token_data.access_token,
                "token_type": token_data.token_type,
                "expires_in": token_data.expires_in,
                "refresh_token": token_data.refresh_token,
                "user_id": result.data.get("user_id")
            }
    # 登录失败，直接返回原始结果
    return result


@router.post("/register", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def register(
    user_in: UserCreate,
    request: Request,
    user_service: UserService = Depends(get_user_service),
) -> Result[UserResponse]:
    """用户注册接口"""
    # 获取客户端信息
    client_ip = request.client.host if request.client else None
    client_device = request.headers.get("User-Agent", None)
    
    # 创建用户参数
    create_params = CreateUserParams(
        user_data=user_in,
        send_welcome_email=True,
        client_ip=client_ip,
        client_device=client_device
    )
    
    # 调用创建用户服务并直接返回结果
    return await user_service.create_user(create_params)


@router.post("/password-reset/request", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def request_password_reset(
    reset_data: PasswordResetRequest,
    auth_service: AuthService = Depends(get_auth_service),
) -> Result:
    """请求密码重置"""
    # 调用密码重置请求服务并直接返回结果
    return await auth_service.request_password_reset(reset_data)


@router.post("/password-reset", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def reset_password(
    reset_data: PasswordResetParams,
    auth_service: AuthService = Depends(get_auth_service),
) -> Result:
    """执行密码重置"""
    # 调用密码重置服务并直接返回结果
    return await auth_service.reset_password(reset_data) 