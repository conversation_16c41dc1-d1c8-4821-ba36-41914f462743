"""
微信小程序授权相关的API路由
"""
from typing import Any, Dict

from fastapi import APIRouter, Depends, Request

from svc.core.services.result import Result
from svc.apps.auth.schemas import (
    WechatLoginRequest, 
    WechatBindRequest,
    WechatLoginParams,
    WechatUserUpdateRequest
)
from svc.core.services.wechat import WechatAuthService
from svc.apps.auth.dependencies import (
    get_wechat_auth_service,
    get_current_active_user
)
from svc.core.exceptions import (
    handle_route_errors,
    AUTH_ERROR_MAPPING
)
from svc.core.exceptions import ErrorCode

router = APIRouter(tags=["微信授权"])


@router.post("/login", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def wechat_login_api(
    request: Request,
    login_data: WechatLoginRequest,
    wechat_auth_service: WechatAuthService = Depends(get_wechat_auth_service),
) -> Result:
    """微信小程序登录接口
    
    通过微信小程序临时登录凭证code登录，如果用户已绑定系统账号则返回对应用户的令牌，否则根据配置自动创建新用户或返回需要注册标志。
    """
    # 创建登录参数，从请求中获取IP和用户代理
    client_ip = request.client.host if request.client else "127.0.0.1"
    user_agent = request.headers.get("user-agent", "Unknown")
    # 准备微信用户信息
    user_info = None
    if login_data.user_info:
        user_info = login_data.user_info.model_dump()
    
    # 创建登录参数
    login_params = WechatLoginParams(
        code=login_data.code,
        user_info=user_info,
        ip_address=client_ip,
        user_agent=user_agent,
        from_code=login_data.from_code
    )
    
    # 调用微信登录服务
    return await wechat_auth_service.login(login_params)


@router.post("/bind", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def bind_wechat_user_api(
    bind_data: WechatBindRequest,
    current_user = Depends(get_current_active_user),
    wechat_auth_service: WechatAuthService = Depends(get_wechat_auth_service),
) -> Result:
    """绑定微信用户与系统用户接口
    
    将微信用户与系统用户进行绑定，需要用户登录后才能操作。
    """
    # 调用绑定服务
    return await wechat_auth_service.bind_wechat_user(
        wechat_user_id=bind_data.wechat_user_id,
        user_id=bind_data.user_id
    )


@router.get("/userinfo", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def get_wechat_userinfo_api(
    current_user = Depends(get_current_active_user),
    wechat_auth_service: WechatAuthService = Depends(get_wechat_auth_service),
) -> Result:
    """获取当前用户绑定的微信信息
    
    获取当前登录用户绑定的微信用户信息。
    """
    # 获取当前用户ID
    user_id = current_user.id
    
    # 从服务层获取微信用户信息
    wechat_user = await wechat_auth_service.get_wechat_user_by_user_id(user_id)
    
    if not wechat_user:
        return wechat_auth_service.resource_not_found_result(
            resource_id=user_id,
            result_code=str(ErrorCode.NOT_FOUND)
        )
    
    # 返回微信用户信息
    return wechat_auth_service.create_success_result({
        "wechat_user": wechat_user.to_dict()
    })


@router.put("/userinfo", response_model=Result)
@handle_route_errors(AUTH_ERROR_MAPPING)
async def update_wechat_userinfo_api(
    update_data: WechatUserUpdateRequest,
    current_user = Depends(get_current_active_user),
    wechat_auth_service: WechatAuthService = Depends(get_wechat_auth_service),
) -> Result:
    """更新当前用户绑定的微信信息
    
    更新当前登录用户绑定的微信用户信息。
    """
    # 获取当前用户ID
    user_id = current_user.id
    
    # 使用服务层方法更新微信用户信息
    update_dict = update_data.model_dump(exclude_unset=True)
    return await wechat_auth_service.update_wechat_user_info(user_id, update_dict) 