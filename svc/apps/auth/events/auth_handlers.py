"""
认证模块的事件处理器 (已重构支持 DI)
提供处理认证相关事件的功能，实现关注点分离和松耦合设计
"""

import logging
from typing import Any, Dict, Optional

# Import DI mechanism
from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

from svc.apps.auth.dependencies import get_auth_service, get_user_service
from svc.apps.auth.services import AuthService  # Example service imports
from svc.apps.auth.services import UserService
from svc.core.events.event_names import \
    SYSTEM_STATS_UPDATE_REQUESTED  # Internal events
from svc.core.events.event_names import (AUTH_PASSWORD_RESET_COMPLETED,
                                         AUTH_PASSWORD_RESET_REQUESTED,
                                         AUTH_USER_AUTHENTICATION_FAILED,
                                         AUTH_USER_AUTHENTICATION_SUCCESS,
                                         AUTH_USER_LOGGED_IN,
                                         AUTH_USER_LOGGED_OUT,
                                         SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_SECURITY_SUSPICIOUS_ACTIVITY,
                                         SYSTEM_USER_NOTIFICATION_REQUESTED)

logger = logging.getLogger(__name__)


@local_handler.register(event_name=AUTH_USER_LOGGED_IN)
async def handle_user_login(
    event: Event,
    user_service: UserService = Depends(get_user_service), # Inject UserService
    auth_service: AuthService = Depends(get_auth_service), # Inject AuthService
):
    """处理用户登录事件，记录登录信息、更新统计数据、更新最后登录时间 (使用 DI)"""
    event_name,payload=event
    user_id = payload.get("user_id")
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    last_login_time = payload.get("last_login_time")

    if not user_id:
        logger.error("处理用户登录事件失败: 缺少 user_id")
        return
    logger.info(f"用户 {user_id} 登录成功: IP={ip_address}, 设备={user_agent},最后登录时间={last_login_time}")

    try:
        # 获取用户信息用于审计日志
        user = await user_service.get_resource_by_id(user_id)
        username = user.email if user else None

        # 登录成功的审计日志由审计中间件记录，这里不再重复记录

        # 触发登录统计事件
        dispatch(SYSTEM_STATS_UPDATE_REQUESTED, payload={
            "entity_type": "user",
            "entity_id": user_id,
            "metric_type": "login_count",
            "increment_value": 1
        })

        # 如果需要，可以在这里添加安全检查逻辑 (使用注入的 AuthService)
        # if ip_address:
        #     # 检查异常登录地点 (假设 AuthService 有此方法)
        #     suspicious = await auth_service.check_suspicious_login(user_id, ip_address)
        #     if suspicious:
        #         logger.warning(f"用户 {user_id} 可能存在异常登录: IP={ip_address}")
        #         # 触发安全事件
        #         dispatch(SYSTEM_SECURITY_SUSPICIOUS_ACTIVITY, payload={
        #             "user_id": user_id,
        #             "activity_type": "suspicious_login",
        #             "ip_address": ip_address,
        #             "user_agent": user_agent
        #         })
    except Exception as e:
        logger.error(f"处理用户登录事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)


@local_handler.register(event_name=AUTH_PASSWORD_RESET_REQUESTED)
async def handle_password_reset_request(
    payload: Dict[str, Any] # FastAPI-Events passes the payload dictionary
):
    """处理密码重置请求事件"""
    user_id = payload.get("user_id")
    email = payload.get("email")
    reset_token = payload.get("reset_token")
    expires_at_str = payload.get("expires_at") # Comes as string potentially
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")

    if not user_id or not email:
        logger.error("处理密码重置请求事件失败: 缺少 user_id 或 email")
        return
        
    logger.info(f"用户 {user_id} ({email}) 请求密码重置")

    try:
        # 触发通知事件
        dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
            "user_id": user_id,
            "notification_type": "password_reset",
            "channel": "email",
            "data": {
                "user_id": user_id,
                "email": email,
                "reset_token": reset_token,
                "expires_at": expires_at_str # Pass the string representation
            }
        })

        # 记录安全审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": user_id,
            "action": "password_reset_requested",
            "resource_type": "user",
            "resource_id": user_id,
            "metadata": {
                "email": email,
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })
    except Exception as e:
        logger.error(f"处理密码重置请求事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)


@local_handler.register(event_name=AUTH_PASSWORD_RESET_COMPLETED)
async def handle_password_reset_completed(
    payload: Dict[str, Any], # FastAPI-Events passes the payload dictionary
    auth_service: AuthService = Depends(get_auth_service), # Inject AuthService
):
    """处理密码重置完成事件，发送通知、记录审计并清理令牌 (使用 DI)"""
    user_id = payload.get("user_id")
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    
    if not user_id:
        logger.error("处理密码重置完成事件失败: 缺少 user_id")
        return
        
    logger.info(f"用户 {user_id} 完成密码重置")

    try:
        # 清理相关令牌 (委托给 AuthService)
        try:
            await auth_service.clear_user_tokens_after_reset(user_id)
        except Exception as e:
            logger.warning(f"调用令牌清理服务时发生错误: user_id={user_id}, 错误={str(e)}") # Log handler context

        # 触发通知事件
        dispatch(SYSTEM_USER_NOTIFICATION_REQUESTED, payload={
            "user_id": user_id,
            "notification_type": "password_reset_completed",
            "channel": "email",
            "data": {
                "user_id": user_id
            }
        })

        # 记录安全审计日志
        dispatch(SYSTEM_AUDIT_LOG_RECORDED, payload={
            "user_id": user_id,
            "action": "password_reset_completed",
            "resource_type": "user",
            "resource_id": user_id,
            "metadata": {
                "ip_address": ip_address,
                "user_agent": user_agent
            }
        })
    except Exception as e:
        logger.error(f"处理密码重置完成事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)


@local_handler.register(event_name=AUTH_USER_LOGGED_OUT)
async def handle_user_logout(
    payload: Dict[str, Any], # FastAPI-Events passes the payload dictionary
    auth_service: AuthService = Depends(get_auth_service), # Inject AuthService
):
    """处理用户登出事件，记录审计并使令牌失效 (使用 DI)"""
    user_id = payload.get("user_id")
    token = payload.get("token") # Assuming token is in payload
    ip_address = payload.get("ip_address")
    user_agent = payload.get("user_agent")
    session_id = payload.get("session_id")
    
    if not user_id:
        logger.error("处理用户登出事件失败: 缺少 user_id")
        return
        
    logger.info(f"用户 {user_id} 登出系统")

    try:
        # 使令牌失效 (委托给 AuthService)
        if token:
            try:
                await auth_service.invalidate_token(token, user_id)
            except Exception as e:
                logger.warning(f"调用令牌失效服务时发生错误: user_id={user_id}, token={token[:5]}..., 错误={str(e)}")

        # 记录用户活动
        # 可以在这里添加会话记录相关逻辑

        # 登出的审计日志由审计中间件记录，这里不再重复记录
    except Exception as e:
        logger.error(f"处理用户登出事件失败: user_id={user_id}, 错误={str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_AUTHENTICATION_SUCCESS)
async def handle_user_authentication_success(
    event:Event,
    user_service: UserService = Depends(get_user_service), # Inject UserService
):
    """处理用户认证成功事件，重置登录失败计数 (使用 DI)"""
    event_name,payload=event
    user_id = payload.get("user_id")
    email = payload.get("email")
    
    if not user_id:
        logger.error(f"处理认证成功事件失败: 缺少 user_id")
        return
        
    logger.debug(f"用户 {user_id} ({email}) 认证成功，重置登录失败计数")
    # try:
    #     # 重置登录失败计数
    #     await user_service.reset_login_attempts(user_id)

    # except Exception as e:
    #     logger.error(f"重置用户 {user_id} 登录失败计数时出错: {str(e)}", exc_info=True)

@local_handler.register(event_name=AUTH_USER_AUTHENTICATION_FAILED)
async def handle_user_authentication_failure(
    payload: Dict[str, Any], # FastAPI-Events passes the payload dictionary
    user_service: UserService = Depends(get_user_service), # Inject UserService
):
    """处理用户认证失败事件，增加登录失败计数并检查锁定 (使用 DI)"""
    user_id = payload.get("user_id")
    email = payload.get("email")
    
    if not user_id: # User ID might be missing if lookup failed before auth
        logger.warning(f"认证失败事件，但未提供有效 user_id (可能是用户不存在): email={email}")
        # Optionally, try to find user by email if needed for some logic, but generally avoid
        return
        
    logger.debug(f"用户 {user_id} ({email}) 认证失败，增加登录失败计数")
    try:
        # 增加登录失败计数并检查锁定
        lock_result = await user_service.increment_login_attempts(user_id)
        if lock_result and lock_result.is_locked:
            logger.warning(f"用户 {user_id} 因登录失败次数过多已被锁定")
    
    except Exception as e:
        logger.error(f"增加用户 {user_id} 登录失败计数时出错: {str(e)}", exc_info=True)
