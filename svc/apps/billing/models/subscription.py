"""订阅模块的数据模型。

该模块定义了订阅相关的数据模型，包括：
- 订阅基本信息
- 订阅状态管理
- 订阅周期控制
- 计费相关属性

技术说明：
- 使用BigInteger自增ID作为主键
- 包含软删除支持
- 支持元数据扩展
- 包含审计字段(created_at, updated_at)
"""

# 标准库导入
from typing import Optional, Dict, Any
from datetime import  timedelta

# 第三方库导入
from sqlalchemy import Column, String, DateTime, ForeignKey, Boolean, Integer, BigInteger
from sqlalchemy.orm import relationship

# 项目内部导入
from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class Subscription(Base):
    """订阅模型类。
    
    该模型用于管理用户的订阅信息，包括：
    - 订阅计划关联
    - 订阅状态跟踪
    - 试用期管理
    - 计费周期控制
    - 取消策略支持
    
    属性:
        id (BigInteger): 订阅唯一标识
        user_id (int): 用户ID
        plan_id (BigInteger): 订阅计划ID
        status (str): 订阅状态，可选值：active, canceled, past_due, trialing
        current_period_start (datetime): 当前计费周期开始时间
        current_period_end (datetime): 当前计费周期结束时间
        cancel_at_period_end (bool): 是否在周期结束时取消
        canceled_at (datetime): 取消时间
        trial_start (datetime): 试用期开始时间
        trial_end (datetime): 试用期结束时间
        quantity (int): 订阅数量
        meta_data (dict): 扩展元数据
        created_at (datetime): 创建时间
        updated_at (datetime): 更新时间
    
    关系:
        plan: 关联的订阅计划
        invoices: 关联的账单列表
    """
    
    __tablename__ = "subscriptions"
    
    # 基本字段
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(BigInteger, nullable=False, index=True)
    plan_id = Column(BigInteger, ForeignKey("subscription_plans.id"), nullable=False)
    status = Column(String, nullable=False, default="active")
    current_period_start = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    current_period_end = Column(DateTime, nullable=False)
    cancel_at_period_end = Column(Boolean, default=False)
    canceled_at = Column(DateTime, nullable=True)
    trial_start = Column(DateTime, nullable=True)
    trial_end = Column(DateTime, nullable=True)
    quantity = Column(Integer, nullable=False, default=1)
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    
    # 审计字段
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系定义
    plan = relationship("SubscriptionPlan", back_populates="subscriptions", lazy="selectin")
    invoices = relationship("Invoice", back_populates="subscription", lazy="dynamic")
