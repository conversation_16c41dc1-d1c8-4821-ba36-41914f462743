import uuid
from typing import Optional, Dict, Any
from datetime import datetime

from sqlalchemy import Column, String, DateTime, ForeignKey, Float, Boolean, BigInteger
from sqlalchemy.orm import relationship

from svc.core.models.base import Base
from svc.core.models.custom_types import DatabaseCompatibleJSON
from svc.core.utils.datetime_utils import get_utc_now_without_tzinfo


class Invoice(Base):
    """账单模型"""
    __tablename__ = "invoices"
    
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    subscription_id = Column(BigInteger, ForeignKey("subscriptions.id"), nullable=False)
    amount = Column(Float, nullable=False)
    currency = Column(String, nullable=False, default="CNY")
    status = Column(String, nullable=False, default="pending")  # pending, paid, failed, canceled
    description = Column(String, nullable=True)
    invoice_number = Column(String, nullable=True, unique=True)
    invoice_date = Column(DateTime, nullable=False, default=get_utc_now_without_tzinfo)
    due_date = Column(DateTime, nullable=True)
    paid_at = Column(DateTime, nullable=True)
    is_paid = Column(Boolean, default=False)
    meta_data = Column(DatabaseCompatibleJSON, default=dict)
    created_at = Column(DateTime, default=get_utc_now_without_tzinfo)
    updated_at = Column(DateTime, default=get_utc_now_without_tzinfo, onupdate=get_utc_now_without_tzinfo)
    
    # 关系
    subscription = relationship("Subscription", back_populates="invoices")
    payments = relationship("Payment", back_populates="invoice") 