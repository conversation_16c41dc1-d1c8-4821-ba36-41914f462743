"""
订阅计划相关的后台任务
"""

import logging
from typing import Any, Dict

from fastapi import Depends
from fastapi_events.dispatcher import dispatch
from fastapi_events.handlers.local import local_handler
from fastapi_events.typing import Event

# 依赖项
from svc.apps.billing.dependencies import get_subscription_plan_service
from svc.apps.billing.services.subscription_plan import SubscriptionPlanService
from svc.core.events.event_names import (BILLING_SUBSCRIPTION_PLAN_CREATED,
                                         BILLING_SUBSCRIPTION_PLAN_DELETED,
                                         BILLING_SUBSCRIPTION_PLAN_UPDATED,
                                         SYSTEM_AUDIT_LOG_RECORDED,
                                         SYSTEM_CACHE_INVALIDATION_REQUESTED,
                                         SYSTEM_STATS_UPDATE_REQUESTED)

logger = logging.getLogger(__name__)


@local_handler.register(event_name=BILLING_SUBSCRIPTION_PLAN_CREATED)
async def handle_subscription_plan_created(
    event: Event,
    plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service)
):
    """处理订阅计划创建事件 (本地): 记录审计、更新统计、清理缓存。"""
    event_name, payload = event
    plan_id = payload.get('plan_id')
    name = payload.get('name')
    creator_id = payload.get('creator_id') # 需要服务层传递创建者 ID

    if not plan_id:
        logger.error("处理订阅计划创建事件失败: 缺少 plan_id")
        return

    logger.info(f"[Local Event] 订阅计划已创建: id={plan_id}, name={name}")

    # 订阅计划创建的审计日志由审计中间件记录，这里不再重复记录

    # 2. 更新统计 (后台任务)
    try:
        # 更新系统计划总数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscription_plans_total_count",
                "increment_value": 1,
                "metadata": {"plan_id": plan_id}
            }
        )
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for plan created {plan_id}: {e}", exc_info=True)

    # 3. 清理相关缓存 (后台任务)
    try:
        # 清理计划列表缓存
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription_plan_list'}
        )
        # 清理按名称查找的缓存
        if name:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'subscription_plan_by_name', 'name': name}
            )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for plan created {plan_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_SUBSCRIPTION_PLAN_UPDATED)
async def handle_subscription_plan_updated(
    event: Event,
    plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service)
):
    """处理订阅计划更新事件 (本地): 记录审计、清理缓存。"""
    event_name, payload = event
    plan_id = payload.get('plan_id')
    updater_id = payload.get('updater_id') # 需要服务层传递更新者 ID
    changed_fields = payload.get('changed_fields', [])
    old_name = payload.get('old_name') # 如果名称改变，需要清理旧名称缓存
    new_name = payload.get('new_name') # 如果名称改变，需要清理新名称缓存

    if not plan_id:
        logger.error("处理订阅计划更新事件失败: 缺少 plan_id")
        return

    logger.info(f"[Local Event] 订阅计划已更新: id={plan_id}")

    # 订阅计划更新的审计日志由审计中间件记录，这里不再重复记录

    # 2. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription_plan', 'resource_id': plan_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription_plan_list'}
        )
        if old_name:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'subscription_plan_by_name', 'name': old_name}
            )
        if new_name:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'subscription_plan_by_name', 'name': new_name}
            )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for plan updated {plan_id}: {e}", exc_info=True)


@local_handler.register(event_name=BILLING_SUBSCRIPTION_PLAN_DELETED)
async def handle_subscription_plan_deleted(
    event: Event,
    plan_service: SubscriptionPlanService = Depends(get_subscription_plan_service)
):
    """处理订阅计划删除事件 (本地): 记录审计、更新统计、清理缓存。"""
    event_name, payload = event
    plan_id = payload.get('plan_id')
    deleter_id = payload.get('deleter_id') # 需要服务层传递删除者 ID
    name = payload.get('name') # 需要计划名称以清理缓存

    if not plan_id:
        logger.error("处理订阅计划删除事件失败: 缺少 plan_id")
        return

    logger.info(f"[Local Event] 订阅计划已删除: id={plan_id}")

    # 订阅计划删除的审计日志由审计中间件记录，这里不再重复记录

    # 2. 更新统计 (后台任务)
    try:
        # 减少系统计划总数
        dispatch(
            SYSTEM_STATS_UPDATE_REQUESTED,
            payload={
                "entity_type": "system",
                "metric_type": "subscription_plans_total_count",
                "increment_value": -1,
                "metadata": {"plan_id": plan_id}
            }
        )
        # 可能需要检查是否有活跃订阅使用此计划，并进行处理或警告
    except Exception as e:
        logger.error(f"Failed to dispatch stats update for plan deleted {plan_id}: {e}", exc_info=True)

    # 3. 清理相关缓存 (后台任务)
    try:
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription_plan', 'resource_id': plan_id}
        )
        dispatch(
            SYSTEM_CACHE_INVALIDATION_REQUESTED,
            payload={'resource_type': 'subscription_plan_list'}
        )
        if name:
            dispatch(
                SYSTEM_CACHE_INVALIDATION_REQUESTED,
                payload={'resource_type': 'subscription_plan_by_name', 'name': name}
            )
    except Exception as e:
        logger.error(f"Failed to dispatch cache invalidation for plan deleted {plan_id}: {e}", exc_info=True)