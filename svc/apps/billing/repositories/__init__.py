"""
计费模块数据仓库层。
提供与计费相关的数据库交互抽象层，封装数据访问逻辑。
"""

# 未来可能从外部导入具体的资源库实现
# from .invoice import InvoiceRepository
# from .payment import PaymentRepository

from svc.apps.billing.repositories.subscription import SubscriptionRepository
from svc.apps.billing.repositories.invoice import InvoiceRepository
from svc.apps.billing.repositories.payment import PaymentRepository
from svc.apps.billing.repositories.subscription_plan import SubscriptionPlanRepository

__all__ = [
    "SubscriptionRepository",
    "InvoiceRepository",
    "PaymentRepository",
    "SubscriptionPlanRepository",
]
