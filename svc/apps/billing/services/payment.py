"""
支付服务模块，提供面向对象形式的支付管理功能。
使用Result模式处理错误，采用面向对象编程风格。
"""
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi_events.dispatcher import dispatch
from redis.asyncio import Redis
from sqlalchemy.ext.asyncio import AsyncSession

from svc.apps.billing.models.payment import Payment
from svc.apps.billing.repositories.payment import PaymentRepository
from svc.apps.billing.schemas.payment import (CreatePaymentParams,
                                              GetPaymentParams,
                                              GetPaymentsParams,
                                              MarkPaymentFailedParams,
                                              MarkPaymentSucceededParams,
                                              PaymentListResponse,
                                              PaymentResponse,
                                              ProcessPaymentCallbackParams,
                                              RefundPaymentParams,
                                              UpdatePaymentParams)
from svc.core.events import event_names
from svc.core.exceptions.error_codes import ErrorCode
from svc.core.services import BaseService
from svc.core.services.result import Result

# 缓存配置
CACHE_VERSION = "v1"  # 缓存版本号
CACHE_TTL = 3600  # 默认缓存过期时间（1小时）
CACHE_TTL_SHORT = 300  # 短期缓存过期时间（5分钟）
CACHE_TTL_LONG = 86400  # 长期缓存过期时间（24小时）


class PaymentService(BaseService[Payment, Result[PaymentResponse]]):
    """支付服务类，提供支付管理相关功能
    
    该服务类负责：
    1. 处理支付的业务逻辑
    2. 验证请求参数和权限
    3. 调用模型层方法进行数据库操作
    4. 触发相关事件
    5. 封装结果返回
    """
    
    # 设置资源类型名称
    resource_type = "payment"
    
    def __init__(self,  repository: PaymentRepository, redis: Optional[Redis] = None):
        """初始化支付服务
        
        Args:
            db: 数据库会话
            repository: 支付仓库实例
            redis: Redis客户端（可选）
        """
        super().__init__(redis)
        self.repository = repository
    
    async def get_resource_by_id(self, payment_id: int) -> Optional[Payment]:
        """获取指定ID的支付记录资源
        
        Args:
            payment_id: 支付记录ID
            
        Returns:
            Optional[Payment]: 支付记录对象，不存在时返回None
        """
        return await self.repository.get_by_id(self.db, payment_id)
    
    async def create_payment(self, params: CreatePaymentParams) -> Result[PaymentResponse]:
        """创建支付记录
        
        Args:
            params: 创建支付记录参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"开始创建支付记录: invoice_id={params.payment_data.invoice_id}, amount={params.payment_data.amount}")
        
        try:
            # 从Pydantic模型中提取数据，确保metadata字段被正确处理
            payment_in = params.payment_data
            payment_data = payment_in.model_dump()
            
            # 创建支付记录
            payment = await self.repository.create_payment(
                db=self.db,
                invoice_id=payment_data["invoice_id"],
                amount=payment_data["amount"],
                payment_method=payment_data["payment_method"],
                currency=payment_data.get("currency", "CNY"),
                external_payment_id=payment_data.get("payment_id"),
                metadata=payment_data.get("metadata", {}),
            )
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 缓存支付记录
            await self._cache_payment(payment.id, response)
            
            # 构建事件数据
            event_data = {
                "payment_id": payment.id,
                "invoice_id": payment.invoice_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "status": payment.status,
                "meta_data": payment.meta_data,
                "created_at": payment.created_at.isoformat() if payment.created_at else None
            }
            
            # 触发支付创建事件 (通常由系统触发，不直接关联用户操作者)
            dispatch(event_names.PAYMENT_CREATED, **event_data)
            
            self.logger.info(f"支付记录创建成功: id={payment.id}, invoice_id={payment.invoice_id}")
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"创建支付记录失败: invoice_id={payment_data['invoice_id']}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="CREATE_FAILED",
                error_message=f"创建支付记录失败: {str(e)}"
            )
    
    async def _check_payment_access(self, payment_id: int, user_id: Optional[int] = None) -> bool:
        """检查用户是否有权限访问支付记录
        
        Args:
            payment_id: 支付记录ID
            user_id: 用户ID（可选）
            
        Returns:
            bool: 是否有权限访问
        """
        if not user_id:
            return True
            
        try:
            # 获取支付记录
            payment = await self.get_resource_by_id(payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={payment_id}")
                return False
            
            # 获取发票信息
            invoice = await payment.awaitable_attrs.invoice
            if not invoice:
                self.logger.warning(f"支付记录关联的发票不存在: payment_id={payment_id}")
                return False
            
            # 获取订阅信息
            subscription = await invoice.awaitable_attrs.subscription
            if not subscription:
                self.logger.warning(f"发票关联的订阅不存在: invoice_id={invoice.id}")
                return False
            
            # 检查用户是否是订阅所有者
            if subscription.user_id == user_id:
                return True
            
            # 检查用户是否有管理员权限
            try:
                user_roles = await self.role_service.get_user_roles(user_id)
                if "admin" in user_roles or "billing_admin" in user_roles:
                    return True
            except Exception as e:
                self.logger.warning(f"检查用户角色失败: user_id={user_id}, 错误={str(e)}")
            
            self.logger.warning(f"用户权限不足: payment_id={payment_id}, user_id={user_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"检查支付记录访问权限失败: payment_id={payment_id}, user_id={user_id}, 错误={str(e)}", exc_info=True)
            return False

    async def get_payment(self, params: GetPaymentParams) -> Result[PaymentResponse]:
        """获取支付记录
        
        Args:
            params: 获取支付记录参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"获取支付记录: payment_id={params.payment_id}, user_id={params.user_id}")
        
        try:
            # 检查访问权限
            if not await self._check_payment_access(params.payment_id, params.user_id):
                return self.permission_denied_result(params.payment_id)
            
            # 先尝试从缓存获取
            cached_payment = await self._get_cached_payment(params.payment_id)
            if cached_payment:
                self.logger.debug(f"从缓存获取到支付记录: payment_id={params.payment_id}")
                return self.create_success_result(cached_payment)
            
            # 从数据库获取
            payment = await self.get_resource_by_id(params.payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={params.payment_id}")
                return self.resource_not_found_result(params.payment_id)
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 缓存支付记录
            await self._cache_payment(payment.id, response)
                
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"获取支付记录失败: payment_id={params.payment_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="GET_FAILED",
                error_message=f"获取支付记录失败: {str(e)}"
            )
    
    def _get_payment_cache_key(self, payment_id: int) -> str:
        """获取支付记录缓存键
        
        Args:
            payment_id: 支付记录ID
            
        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{self.CACHE_VERSION}:{payment_id}"
    
    def _get_invoice_payments_cache_key(self, invoice_id: int, page_num: int = 1, page_size: int = 10) -> str:
        """获取账单支付记录列表缓存键 (使用 page_num 和 page_size)

        Args:
            invoice_id: 账单ID
            page_num: 页码
            page_size: 每页数量

        Returns:
            str: 缓存键
        """
        return f"{self.resource_type}:{self.CACHE_VERSION}:invoice:{invoice_id}:list:page_{page_num}:size_{page_size}"
    
    def _get_cache_stats_key(self) -> str:
        """获取缓存统计信息的键
        
        Returns:
            str: 缓存统计键
        """
        return f"{self.resource_type}:{self.CACHE_VERSION}:stats"
    
    async def _increment_cache_stats(self, stat_type: str) -> None:
        """增加缓存统计计数
        
        Args:
            stat_type: 统计类型（hit/miss/error）
        """
        if not self.redis:
            return
            
        try:
            stats_key = self._get_cache_stats_key()
            await self.redis.hincrby(stats_key, stat_type, 1)
        except Exception as e:
            self.logger.warning(f"更新缓存统计失败: stat_type={stat_type}, 错误={str(e)}")
    
    async def _get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息
        
        Returns:
            Dict[str, int]: 缓存统计信息
        """
        if not self.redis:
            return {}
            
        try:
            stats_key = self._get_cache_stats_key()
            stats = await self.redis.hgetall(stats_key)
            return {k.decode(): int(v) for k, v in stats.items()}
        except Exception as e:
            self.logger.warning(f"获取缓存统计失败: 错误={str(e)}")
            return {}
    
    async def get_payments(self, params: GetPaymentsParams) -> Result[PaymentListResponse]:
        """获取支付记录列表（通用方法）

        Args:
            params: 获取支付记录列表参数 (包含过滤和分页信息)

        Returns:
            Result[PaymentListResponse]: 包含分页信息的支付记录列表结果
        """
        self.logger.info(f"获取支付记录列表: invoice_id={params.invoice_id}, user_id={params.user_id}, status={params.status}, page={params.page_num}, size={params.page_size}")

        try:
            page_size = params.page_size
            skip = (params.page_num - 1) * page_size

     
      
            payments, total = await self.repository.get_payments(
                invoice_id=params.invoice_id,
                user_id=params.user_id,
                status=params.status,
                skip=skip,
                limit=page_size
            )

            # 构建响应列表
            responses = [PaymentResponse.model_validate(p) for p in payments]

            # 计算总页数
            total_pages = (total + page_size - 1) // page_size if page_size > 0 else 0

            # 构建分页响应
            paginated_response = PaymentListResponse(
                items=responses,
                total=total,
                page_num=params.page_num,
                page_size=page_size,
                page_count=total_pages
            )

            # TODO: Implement caching for filtered/paginated lists if needed
            # if self.redis: ...

            return self.create_success_result(paginated_response)

        except Exception as e:
            self.logger.error(f"获取支付记录列表失败: {str(e)}", exc_info=True)
            return self.create_error_result(
                error_code=ErrorCode.OPERATION_FAILED,
                error_message=f"获取支付记录列表失败: {str(e)}"
            )
    
    async def update_payment(self, params: UpdatePaymentParams) -> Result[PaymentResponse]:
        """更新支付记录
        
        Args:
            params: 更新支付记录参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"更新支付记录: payment_id={params.payment_id}")
        
        try:
            # 获取支付记录
            payment = await self.get_resource_by_id(params.payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={params.payment_id}")
                return self.resource_not_found_result(params.payment_id)
            
            # 已完成的支付记录不能更新
            if payment.status in ["succeeded", "failed"]:
                self.logger.warning(f"已完成的支付记录不能更新: payment_id={params.payment_id}, status={payment.status}")
                return self.create_error_result(
                    error_code="PAYMENT_COMPLETED",
                    error_message=f"已{payment.status}的支付记录不能更新"
                )
            
            # 提取更新数据
            update_data = params.payment_data.model_dump(exclude_unset=True)
            if not update_data:
                self.logger.debug(f"没有需要更新的数据: payment_id={params.payment_id}")
                return self.create_success_result(PaymentResponse.model_validate(payment))
            
            # 更新支付记录
            updated_payment = await self.repository.update_by_id(self.db, params.payment_id, update_data)
            if not updated_payment:
                self.logger.error(f"更新支付记录失败（未找到或更新未执行）: payment_id={params.payment_id}")
                return self.create_error_result(
                    error_code="UPDATE_FAILED",
                    error_message="更新支付记录失败"
                )
            payment = updated_payment
            
            # 构建事件数据
            event_data = {
                "payment_id": payment.id,
                "invoice_id": payment.invoice_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "status": payment.status,
                "meta_data": payment.meta_data,
                "updated_fields": list(update_data.keys()),
                "updated_at": payment.updated_at.isoformat() if payment.updated_at else None
            }
            
            # 触发支付更新事件 (关联更新操作的用户)
            dispatch(event_names.PAYMENT_UPDATED, **event_data)
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 更新缓存
            await self._cache_payment(payment.id, response)
            
            self.logger.info(f"支付记录更新成功: payment_id={payment.id}, 更新字段={list(update_data.keys())}")
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"更新支付记录失败: payment_id={params.payment_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="UPDATE_FAILED",
                error_message=f"更新支付记录失败: {str(e)}"
            )
    
    async def process_payment_callback(self, params: ProcessPaymentCallbackParams) -> Result[PaymentResponse]:
        """处理支付回调
        
        Args:
            params: 处理支付回调参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"处理支付回调: payment_id={params.payment_id}")
        
        try:
            # 获取支付记录
            payment = await self.get_resource_by_id(params.payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={params.payment_id}")
                return self.resource_not_found_result(params.payment_id)
            
            # 已完成的支付记录不能重复处理
            if payment.status in ["succeeded", "failed"]:
                self.logger.warning(f"已完成的支付记录不能重复处理: payment_id={params.payment_id}, status={payment.status}")
                return self.create_error_result(
                    error_code="PAYMENT_COMPLETED",
                    error_message=f"已{payment.status}的支付记录不能重复处理"
                )
            
            # 记录回调时间
            callback_time = datetime.utcnow()
            
            # 存储回调数据
            if not payment.callback_data:
                payment.callback_data = {}
            payment.callback_data[callback_time.isoformat()] = params.callback_data
            
            # 根据回调数据更新支付状态
            if params.is_success:
                payment.status = "succeeded"
                payment.paid_at = callback_time
                payment.transaction_id = params.transaction_id
            else:
                payment.status = "failed"
                payment.failed_at = callback_time
                payment.failure_reason = params.failure_reason
            
            await self.db.commit()
            await self.db.refresh(payment)
            
            # 构建事件数据
            event_data = {
                "payment_id": payment.id,
                "invoice_id": payment.invoice_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "status": payment.status,
                "callback_time": callback_time.isoformat(),
                "callback_data": params.callback_data,
                "meta_data": payment.meta_data
            }
            
            # 根据状态添加额外字段
            if payment.status == "succeeded":
                event_data.update({
                    "transaction_id": payment.transaction_id,
                    "paid_at": payment.paid_at.isoformat() if payment.paid_at else None
                })
            elif payment.status == "failed":
                event_data.update({
                    "failure_reason": payment.failure_reason,
                    "failed_at": payment.failed_at.isoformat() if payment.failed_at else None
                })
            
            # 触发支付回调处理完成事件 (通常由系统回调触发，可能没有直接关联的用户)
            dispatch(event_names.PAYMENT_CALLBACK_PROCESSED, **event_data)
            
            self.logger.info(f"支付回调处理成功: id={payment.id}")
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 更新缓存
            await self._cache_payment(payment.id, response)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"处理支付回调失败: payment_id={params.payment_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="CALLBACK_FAILED",
                error_message=f"处理支付回调失败: {str(e)}"
            )
    
    async def mark_as_succeeded(self, params: MarkPaymentSucceededParams) -> Result[PaymentResponse]:
        """将支付记录标记为成功
        
        Args:
            params: 标记为成功参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"标记支付记录为成功: payment_id={params.payment_id}")
        
        try:
            # 获取支付记录
            payment = await self.get_resource_by_id(params.payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={params.payment_id}")
                return self.resource_not_found_result(params.payment_id, "PAYMENT_NOT_FOUND")
            
            # 已成功的支付不能重复标记
            if payment.status == "succeeded":
                self.logger.warning(f"支付记录已经是成功状态: payment_id={params.payment_id}")
                return self.create_error_result(
                    error_code="PAYMENT_ALREADY_SUCCEEDED",
                    error_message="支付记录已经是成功状态"
                )
            
            # 已退款的支付不能标记为成功
            if payment.status == "refunded":
                self.logger.warning(f"已退款的支付记录不能标记为成功: payment_id={params.payment_id}")
                return self.create_error_result(
                    error_code="PAYMENT_ALREADY_REFUNDED",
                    error_message="已退款的支付记录不能标记为成功"
                )
            
            # 标记为成功
            success_time = params.paid_at or datetime.utcnow()
            payment.status = "succeeded"
            payment.paid_at = success_time
            payment.transaction_id = params.transaction_id
            
            await self.db.commit()
            await self.db.refresh(payment)
            
            # 构建事件数据
            event_data = {
                "payment_id": payment.id,
                "invoice_id": payment.invoice_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "status": payment.status,
                "success_time": success_time.isoformat(),
                "transaction_id": payment.transaction_id,
                "paid_at": payment.paid_at.isoformat() if payment.paid_at else None,
                "meta_data": payment.meta_data
            }
            
            # 触发支付成功事件 (关联标记操作的用户)
            dispatch(event_names.PAYMENT_SUCCEEDED, **event_data)
            
            self.logger.info(f"支付记录 {payment.id} 标记为成功")
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 更新缓存
            await self._cache_payment(payment.id, response)
            
            return self.create_success_result(response)
        except Exception as e:
            self.logger.error(f"标记支付记录为成功失败: payment_id={params.payment_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="UPDATE_FAILED",
                error_message=f"标记支付记录为成功失败: {str(e)}"
            )
    
    async def mark_as_failed(self, params: MarkPaymentFailedParams) -> Result[PaymentResponse]:
        """将支付记录标记为失败
        
        Args:
            params: 标记支付失败参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"标记支付记录为失败: payment_id={params.payment_id}")
        
        try:
            # 获取支付记录
            payment = await self.get_resource_by_id(params.payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={params.payment_id}")
                return self.resource_not_found_result(params.payment_id)
            
            # 已完成的支付记录不能标记为失败
            if payment.status in ["succeeded", "failed"]:
                self.logger.warning(f"已完成的支付记录不能标记为失败: payment_id={params.payment_id}, status={payment.status}")
                return self.create_error_result(
                    error_code="PAYMENT_COMPLETED",
                    error_message=f"已{payment.status}的支付记录不能标记为失败"
                )
            
            # 标记为失败
            failure_time = datetime.utcnow()
            payment.status = "failed"
            payment.failed_at = failure_time
            payment.failure_reason = params.reason
            
            await self.db.commit()
            await self.db.refresh(payment)
            
            # 构建事件数据
            event_data = {
                "payment_id": payment.id,
                "invoice_id": payment.invoice_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "status": payment.status,
                "failure_reason": payment.failure_reason,
                "failed_at": payment.failed_at.isoformat() if payment.failed_at else None,
                "meta_data": payment.meta_data
            }
            
            # 触发支付失败事件 (关联标记操作的用户)
            dispatch(event_names.PAYMENT_FAILED, **event_data)
            
            self.logger.info(f"支付记录 {payment.id} 标记为失败")
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 更新缓存
            await self._cache_payment(payment.id, response)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"标记支付记录为失败失败: payment_id={params.payment_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="UPDATE_FAILED",
                error_message=f"标记支付记录为失败失败: {str(e)}"
            )
    
    async def refund_payment(self, params: RefundPaymentParams) -> Result[PaymentResponse]:
        """退款
        
        Args:
            params: 退款参数
            
        Returns:
            支付结果对象
        """
        self.logger.info(f"处理退款: payment_id={params.payment_id}, amount={params.amount}")
        
        try:
            # 获取支付记录
            payment = await self.get_resource_by_id(params.payment_id)
            if not payment:
                self.logger.warning(f"支付记录不存在: payment_id={params.payment_id}")
                return self.resource_not_found_result(params.payment_id)
            
            # 只有成功的支付才能退款
            if payment.status != "succeeded":
                self.logger.warning(f"只有成功的支付才能退款，当前状态: {payment.status}, payment_id={params.payment_id}")
                return self.create_error_result(
                    error_code="PAYMENT_NOT_SUCCEEDED",
                    error_message="只有成功的支付才能退款"
                )
            
            # 已退款的支付不能重复退款
            if payment.status == "refunded":
                self.logger.warning(f"支付记录已经退款: payment_id={params.payment_id}")
                return self.create_error_result(
                    error_code="PAYMENT_ALREADY_REFUNDED",
                    error_message="支付记录已经退款"
                )
            
            # 执行退款
            refund_time = datetime.utcnow()
            payment.status = "refunded"
            payment.refunded_at = refund_time
            payment.refund_amount = params.amount
            payment.refund_reason = params.reason
            
            await self.db.commit()
            await self.db.refresh(payment)
            
            # 构建事件数据
            event_data = {
                "payment_id": payment.id,
                "invoice_id": payment.invoice_id,
                "amount": payment.amount,
                "currency": payment.currency,
                "payment_method": payment.payment_method,
                "status": payment.status,
                "refund_amount": payment.refund_amount,
                "refund_reason": payment.refund_reason,
                "refunded_at": payment.refunded_at.isoformat() if payment.refunded_at else None,
                "paid_at": payment.paid_at.isoformat() if payment.paid_at else None,
                "meta_data": payment.meta_data
            }
            
            # 触发退款事件 (关联退款操作的用户)
            dispatch(event_names.PAYMENT_REFUNDED, **event_data)
            
            self.logger.info(f"支付记录 {payment.id} 退款成功")
            
            # 构建响应
            response = PaymentResponse.model_validate(payment)
            
            # 更新缓存
            await self._cache_payment(payment.id, response)
            
            return self.create_success_result(response)
            
        except Exception as e:
            self.logger.error(f"处理退款失败: payment_id={params.payment_id}, 错误={str(e)}", exc_info=True)
            return self.create_error_result(
                error_code="REFUND_FAILED",
                error_message=f"处理退款失败: {str(e)}"
            )
            
    async def _cache_payment(self, payment_id: int, response: PaymentResponse) -> None:
        """缓存支付记录信息
        
        Args:
            payment_id: 支付记录ID
            response: 支付记录响应对象
        """
        if not self.redis:
            return
            
        try:
            key = self._get_payment_cache_key(payment_id)
            
            # 根据支付状态设置不同的过期时间
            if response.status in ["succeeded", "refunded", "failed"]:
                # 已完成的支付使用长期缓存
                ttl = self.CACHE_TTL_LONG
            else:
                # 进行中的支付使用短期缓存
                ttl = self.CACHE_TTL_SHORT
            
            await self.cache_resource(key, response, ttl)
            self.logger.debug(f"支付记录缓存成功: payment_id={payment_id}, ttl={ttl}")
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"缓存支付记录失败: payment_id={payment_id}, 错误={str(e)}")
    
    async def _get_cached_payment(self, payment_id: int) -> Optional[PaymentResponse]:
        """从缓存获取支付记录信息
        
        Args:
            payment_id: 支付记录ID
            
        Returns:
            Optional[PaymentResponse]: 支付记录响应对象，如果不存在则返回None
        """
        if not self.redis:
            return None
            
        try:
            key = self._get_payment_cache_key(payment_id)
            cached_data = await self.get_cached_resource(
                key,
                lambda data: PaymentResponse.model_validate(data)
            )
            
            if cached_data:
                await self._increment_cache_stats("hit")
                self.logger.debug(f"从缓存获取支付记录成功: payment_id={payment_id}")
                return cached_data
            else:
                await self._increment_cache_stats("miss")
                return None
                
        except Exception as e:
            await self._increment_cache_stats("error")
            self.logger.warning(f"从缓存获取支付记录失败: payment_id={payment_id}, 错误={str(e)}")
            return None
    
    async def warm_up_cache(self, days: int = 7) -> None:
        """预热缓存
        
        Args:
            days: 预热最近几天的数据（默认7天）
        """
        if not self.redis:
            return
            
        try:
            self.logger.info(f"开始预热支付记录缓存: days={days}")
            
            # 获取最近的支付记录
            recent_payments = await Payment.get_recent(self.db, days=days)
            total = len(recent_payments)
            
            for i, payment in enumerate(recent_payments, 1):
                try:
                    response = PaymentResponse.model_validate(payment)
                    await self._cache_payment(payment.id, response)
                    if i % 100 == 0:
                        self.logger.info(f"缓存预热进度: {i}/{total}")
                except Exception as e:
                    self.logger.warning(f"预热单条支付记录失败: payment_id={payment.id}, 错误={str(e)}")
            
            self.logger.info(f"支付记录缓存预热完成: 总数={total}")
            
        except Exception as e:
            self.logger.error(f"支付记录缓存预热失败: 错误={str(e)}", exc_info=True) 