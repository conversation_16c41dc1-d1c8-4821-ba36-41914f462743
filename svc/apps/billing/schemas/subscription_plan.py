from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic import BaseModel, Field, field_validator, ConfigDict
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel


# 参数模型
class GetPlanParams(BaseModel):
    """获取订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 1,
                "user_id": None
            }
        }
    )
    
    plan_id: int = Field(description="订阅计划ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetPlanByNameParams(BaseModel):
    """通过名称获取订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "基础版"
            }
        }
    )
    
    name: str = Field(description="订阅计划名称")


class GetPlansParams(BaseModel):
    """获取订阅计划列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "page_num": 1,
                "page_size": 10,
                "active_only": False
            }
        }
    )
    
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")
    active_only: bool = Field(default=False, description="是否只返回激活的计划")


class CreatePlanParams(BaseModel):
    """创建订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_data": {}
            }
        }
    )
    
    plan_data: 'SubscriptionPlanCreate' = Field(description="订阅计划创建数据")


class UpdatePlanParams(BaseModel):
    """更新订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 1,
                "plan_data": {}
            }
        }
    )
    
    plan_id: int = Field(description="订阅计划ID")
    plan_data: 'SubscriptionPlanUpdate' = Field(description="订阅计划更新数据")


class DeletePlanParams(BaseModel):
    """删除订阅计划参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "plan_id": 1
            }
        }
    )
    
    plan_id: int = Field(description="订阅计划ID")


class SubscriptionPlanBase(CamelCaseModel):
    """订阅计划基础模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "基础版",
                "description": "基础版订阅计划",
                "price": 99.00,
                "currency": "CNY",
                "interval": "month",
                "interval_count": 1,
                "trial_period_days": 7,
                "features": {
                    "storage": "10GB",
                    "users": 5
                },
                "meta_data": {}
            }
        }
    )
    
    name: str = Field(..., max_length=100, description="套餐名称")
    description: Optional[str] = Field(None, description="套餐描述")
    price: float = Field(..., description="价格")
    currency: str = Field("CNY", description="货币")
    billing_cycle: str = Field(..., description="计费周期 (e.g., month, year)")
    features: List[str] = Field([], description="功能列表")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


class SubscriptionPlanCreate(SubscriptionPlanBase):
    """创建订阅计划请求模型"""
    pass


class SubscriptionPlanUpdate(CamelCaseModel):
    """更新订阅计划请求模型"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "name": "基础版",
                "price": 99.00,
                "is_active": True
            }
        }
    )
    
    name: Optional[str] = Field(None, max_length=100, description="套餐名称")
    description: Optional[str] = Field(None, description="套餐描述")
    price: Optional[float] = Field(None, description="价格")
    currency: Optional[str] = Field(None, description="货币")
    billing_cycle: Optional[str] = Field(None, description="计费周期")
    features: Optional[List[str]] = Field(None, description="功能列表")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


class SubscriptionPlanInDB(SubscriptionPlanBase):
    """数据库中的订阅计划模型"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "is_active": True,
                "created_at": "2024-03-24T12:00:00",
                "updated_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    id: int = Field(..., description="套餐ID")
    is_active: bool = Field(description="是否激活")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    @field_validator('meta_data', mode='before')
    @classmethod
    def validate_meta_data(cls, v, info):
        """将meta_data字段映射到meta_data"""
        if hasattr(info.data, 'meta_data'):
            return info.data.meta_data
        return v


class SubscriptionPlanResponse(SubscriptionPlanInDB):
    """订阅计划响应模型"""
    pass


class SubscriptionPlanListResponse(PaginatedResponse[SubscriptionPlanResponse]):
    """订阅计划列表响应模型"""
    pass 