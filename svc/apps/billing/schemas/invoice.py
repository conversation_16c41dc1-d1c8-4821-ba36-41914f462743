from typing import Optional, Dict, Any, List
from datetime import datetime

from pydantic import BaseModel, Field, field_validator, ConfigDict
from svc.core.schemas.base import PaginatedResponse
from svc.core.models.base import CamelCaseModel


# 参数模型
class GetInvoiceParams(BaseModel):
    """获取账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "user_id": None
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")


class GetInvoicesParams(BaseModel):
    """获取账单列表参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1,
                "user_id": 1,
                "status": "unpaid",
                "page_num": 1,
                "page_size": 10
            }
        }
    )
    
    subscription_id: Optional[int] = Field(default=None, description="订阅ID")
    user_id: Optional[int] = Field(default=None, description="用户ID")
    status: Optional[str] = Field(default=None, description="账单状态")
    page_num: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=10, ge=1, le=1000, description="每页数量")


class GetUnpaidInvoicesParams(BaseModel):
    """获取未支付账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "subscription_id": 1
            }
        }
    )
    
    subscription_id: int = Field(description="订阅ID")


class CreateInvoiceParams(BaseModel):
    """创建账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_data": {}
            }
        }
    )
    
    invoice_data: 'InvoiceCreate' = Field(description="账单创建数据")


class UpdateInvoiceParams(BaseModel):
    """更新账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "invoice_data": {}
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    invoice_data: 'InvoiceUpdate' = Field(description="账单更新数据")


class MarkAsPaidParams(BaseModel):
    """标记为已支付参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1,
                "paid_at": "2024-03-24T12:00:00"
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")
    paid_at: Optional[datetime] = Field(default=None, description="支付时间")


class MarkAsOverdueParams(BaseModel):
    """标记为逾期参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")


class CancelInvoiceParams(BaseModel):
    """取消账单参数"""
    model_config = ConfigDict(
        str_strip_whitespace=True,
        json_schema_extra={
            "example": {
                "invoice_id": 1
            }
        }
    )
    
    invoice_id: int = Field(description="账单ID")


# 为了导入兼容性，添加别名
MarkInvoicePaidParams = MarkAsPaidParams
MarkInvoiceOverdueParams = MarkAsOverdueParams


class InvoiceItem(CamelCaseModel):
    description: str = Field(..., description="项目描述")
    quantity: int = Field(..., description="数量")
    unit_price: float = Field(..., description="单价")
    amount: float = Field(..., description="金额")


class InvoiceBase(CamelCaseModel):
    user_id: int = Field(..., description="用户ID")
    subscription_id: Optional[int] = Field(None, description="订阅ID")
    status: str = Field(..., description="账单状态 (e.g., draft, open, paid, void, uncollectible)")
    due_date: datetime = Field(..., description="到期日期")
    total_amount: float = Field(..., description="总金额")
    currency: str = Field("CNY", description="货币")
    items: List[InvoiceItem] = Field([], description="账单项目")
    meta_data: Optional[Dict[str, Any]] = Field(None, description="元数据")


class InvoiceCreate(InvoiceBase):
    pass


class InvoiceUpdate(CamelCaseModel):
    status: Optional[str] = Field(None, description="账单状态")


class InvoiceResponse(InvoiceBase):
    id: int = Field(..., description="账单ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True


class InvoiceListResponse(PaginatedResponse[InvoiceResponse]):
    """账单列表响应模型"""
    pass 