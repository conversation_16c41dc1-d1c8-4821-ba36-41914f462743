"""
计费模块的Pydantic模型定义。
包含所有请求、响应和参数模型。
"""

# 基础模型导入


# 订阅计划相关模型导入
from svc.apps.billing.schemas.subscription_plan import (
    # 订阅计划基础模型
    SubscriptionPlanBase,
    SubscriptionPlanCreate,
    SubscriptionPlanUpdate,
    SubscriptionPlanResponse,
    SubscriptionPlanListResponse,
    
    # 操作参数
    GetPlanParams,
    GetPlansParams,
    CreatePlanParams,
    UpdatePlanParams,
    DeletePlanParams,
)

# 订阅相关模型导入
from svc.apps.billing.schemas.subscription import (
    # 订阅基础模型
    SubscriptionBase,
    SubscriptionCreate,
    SubscriptionUpdate,
    SubscriptionResponse,
    SubscriptionListResponse,
    SubscriptionCancelRequest,
    
    # 操作参数
    GetSubscriptionParams,
    GetSubscriptionsParams,
    CreateSubscriptionParams,
    UpdateSubscriptionParams,
    CancelSubscriptionParams,
    RenewSubscriptionParams,
)

# 发票相关模型导入
from svc.apps.billing.schemas.invoice import (
    # 发票基础模型
    InvoiceBase,
    InvoiceCreate,
    InvoiceUpdate,
    InvoiceResponse,
    InvoiceListResponse,
    
    # 操作参数
    GetInvoiceParams,
    GetInvoicesParams,
    CreateInvoiceParams,
    CancelInvoiceParams,
    UpdateInvoiceParams,
    MarkInvoicePaidParams,
    MarkInvoiceOverdueParams,
)

# 支付相关模型导入
from svc.apps.billing.schemas.payment import (
    # 支付基础模型
    PaymentBase,
    PaymentCreate,
    PaymentUpdate,
    PaymentResponse,
    PaymentListResponse,
    PaymentCallbackRequest,
    
    # 操作参数
    GetPaymentParams,
    GetPaymentsParams,
    CreatePaymentParams,
    UpdatePaymentParams,
    ProcessPaymentCallbackParams,
    MarkPaymentSucceededParams,
    MarkPaymentFailedParams,
    RefundPaymentParams,
)

__all__ = [
    
    # 订阅计划模型
    "SubscriptionPlanBase",
    "SubscriptionPlanCreate",
    "SubscriptionPlanUpdate",
    "SubscriptionPlanResponse",
    "SubscriptionPlanListResponse",
    
    # 订阅计划参数
    "GetPlanParams",
    "GetPlansParams",
    "CreatePlanParams",
    "UpdatePlanParams",
    "DeletePlanParams",
    
    # 订阅模型
    "SubscriptionBase",
    "SubscriptionCreate",
    "SubscriptionUpdate",
    "SubscriptionResponse",
    "SubscriptionListResponse",
    "SubscriptionCancelRequest",
    
    # 订阅参数
    "GetSubscriptionParams",
    "GetSubscriptionsParams",
    "CreateSubscriptionParams",
    "UpdateSubscriptionParams",
    "CancelSubscriptionParams",
    "RenewSubscriptionParams",
    
    # 发票模型
    "InvoiceBase",
    "InvoiceCreate",
    "InvoiceUpdate",
    "InvoiceResponse",
    "InvoiceListResponse",
    
    # 发票参数
    "GetInvoiceParams",
    "GetInvoicesParams",
    "CreateInvoiceParams",
    "CancelInvoiceParams",
    "UpdateInvoiceParams",
    "MarkInvoicePaidParams",
    "MarkInvoiceOverdueParams",
    
    # 支付模型
    "PaymentBase",
    "PaymentCreate",
    "PaymentUpdate",
    "PaymentResponse",
    "PaymentListResponse",
    "PaymentCallbackRequest",
    
    # 支付参数
    "GetPaymentParams",
    "GetPaymentsParams",
    "CreatePaymentParams",
    "UpdatePaymentParams",
    "ProcessPaymentCallbackParams",
    "MarkPaymentSucceededParams",
    "MarkPaymentFailedParams",
    "RefundPaymentParams",
]
