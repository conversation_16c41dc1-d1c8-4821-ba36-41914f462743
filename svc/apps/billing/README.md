# 账单管理模块

基于产品模块模板创建的账单管理模块，提供账单、支付、订阅、套餐等完整的管理功能。

## 模块结构

```
svc/apps/billing/
├── __init__.py                 # 模块初始化
├── README.md                   # 模块说明文档
├── dependencies.py             # 依赖注入
├── models/                     # 数据模型
│   ├── __init__.py
│   ├── invoice.py             # 账单模型
│   ├── payment.py             # 支付模型
│   ├── subscription.py        # 订阅模型
│   └── subscription_plan.py   # 套餐模型
├── schemas/                    # 请求/响应模式
│   ├── __init__.py
│   ├── invoice.py             # 账单模式
│   ├── payment.py             # 支付模式
│   ├── subscription.py        # 订阅模式
│   └── subscription_plan.py   # 套餐模式
├── repositories/               # 数据访问层
│   ├── __init__.py
│   ├── invoice.py             # 账单仓库
│   ├── payment.py             # 支付仓库
│   ├── subscription.py        # 订阅仓库
│   └── subscription_plan.py   # 套餐仓库
├── services/                   # 业务逻辑层
│   ├── __init__.py
│   ├── invoice.py             # 账单服务
│   ├── payment.py             # 支付服务
│   ├── subscription.py        # 订阅服务
│   └── subscription_plan.py   # 套餐服务
├── routers/                    # API路由
│   ├── __init__.py
│   ├── invoices.py            # 账单路由
│   ├── payments.py            # 支付路由
│   ├── subscriptions.py       # 订阅路由
│   └── subscription_plans.py  # 套餐路由
├── events/                     # 事件处理器
│   ├── __init__.py
│   ├── invoice_handlers.py    # 账单事件
│   ├── payment_handlers.py    # 支付事件
│   ├── subscription_handlers.py # 订阅事件
│   └── subscription_plan_handlers.py # 套餐事件
└── utils/                      # 工具函数
    └── __init__.py
```

## 核心功能

### 1. 账单管理 (Invoice)

-   账单的创建、查询、更新、删除
-   账单状态管理（待支付、已支付、已取消、已过期）
-   账单明细和发票管理
-   账单统计与报表

### 2. 支付管理 (Payment)

-   支付记录的创建、查询、更新
-   多种支付方式支持（微信、支付宝、银行卡等）
-   支付状态管理
-   支付回调与对账

### 3. 订阅管理 (Subscription)

-   订阅的创建、续费、取消
-   订阅状态管理（激活、暂停、过期、取消）
-   订阅周期和到期提醒

### 4. 套餐管理 (Subscription Plan)

-   套餐的创建、查询、更新、删除
-   套餐定价和周期配置
-   套餐权益管理

## API 端点

### 账单 API

-   `GET /invoices/` - 获取账单列表
-   `GET /invoices/{id}` - 获取账单详情
-   `POST /invoices/admin/` - 创建账单（管理端）
-   `PUT /invoices/admin/{id}` - 更新账单（管理端）
-   `DELETE /invoices/admin/{id}` - 删除账单（管理端）

### 支付 API

-   `GET /payments/` - 获取支付记录列表
-   `GET /payments/{id}` - 获取支付详情
-   `POST /payments/admin/` - 创建支付记录（管理端）
-   `PUT /payments/admin/{id}` - 更新支付记录（管理端）

### 订阅 API

-   `GET /subscriptions/` - 获取订阅列表
-   `GET /subscriptions/{id}` - 获取订阅详情
-   `POST /subscriptions/admin/` - 创建订阅（管理端）
-   `PUT /subscriptions/admin/{id}` - 更新订阅（管理端）

### 套餐 API

-   `GET /subscription_plans/` - 获取套餐列表
-   `GET /subscription_plans/{id}` - 获取套餐详情
-   `POST /subscription_plans/admin/` - 创建套餐（管理端）
-   `PUT /subscription_plans/admin/{id}` - 更新套餐（管理端）

## 事件系统

模块集成了事件系统，支持以下事件：

### 账单事件

-   `billing:invoice:created` - 账单创建
-   `billing:invoice:updated` - 账单更新
-   `billing:invoice:deleted` - 账单删除

### 支付事件

-   `billing:payment:created` - 支付创建
-   `billing:payment:updated` - 支付更新
-   `billing:payment:deleted` - 支付删除

### 订阅事件

-   `billing:subscription:created` - 订阅创建
-   `billing:subscription:renewed` - 订阅续费
-   `billing:subscription:cancelled` - 订阅取消

### 套餐事件

-   `billing:subscription_plan:created` - 套餐创建
-   `billing:subscription_plan:updated` - 套餐更新
-   `billing:subscription_plan:deleted` - 套餐删除

## 缓存策略

-   账单信息缓存：1 小时
-   支付信息缓存：30 分钟
-   订阅信息缓存：30 分钟
-   套餐信息缓存：1 小时

## 权限控制

模块集成了基于角色的权限控制：

-   `invoice:read` - 账单查看权限
-   `invoice:create` - 账单创建权限
-   `invoice:update` - 账单更新权限
-   `invoice:delete` - 账单删除权限
-   `payment:*` - 支付相关权限
-   `subscription:*` - 订阅相关权限
-   `subscription_plan:*` - 套餐相关权限

## 数据库迁移

```sql
-- 创建账单表
CREATE TABLE invoices (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT,
    amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    due_date DATETIME,
    paid_date DATETIME,
    cancelled_date DATETIME,
    details JSON,
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建支付表
CREATE TABLE payments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    invoice_id BIGINT NOT NULL,
    user_id BIGINT,
    amount DECIMAL(10,2) NOT NULL,
    method VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending',
    paid_at DATETIME,
    callback_data JSON,
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建订阅表
CREATE TABLE subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    plan_id BIGINT NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    start_date DATETIME,
    end_date DATETIME,
    renewal_date DATETIME,
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- 创建套餐表
CREATE TABLE subscription_plans (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration INT NOT NULL,
    duration_unit VARCHAR(20) NOT NULL,
    features JSON,
    status VARCHAR(20) DEFAULT 'active',
    meta_data JSON,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

## 注意事项

1. 账单、支付、订阅等数据变更时需及时更新缓存。
2. 事件处理器异常不影响主业务流程。
3. 权限校验和数据一致性需重点关注。
4. 支付回调和对账逻辑需保证幂等性和安全性。
