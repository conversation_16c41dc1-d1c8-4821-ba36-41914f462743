"""
计费模块
包含订阅计划、订阅管理、发票和支付等功能
"""

# 导出服务类
from svc.apps.billing.services import (
    SubscriptionPlanService,
    SubscriptionService,
    InvoiceService,
    PaymentService
)

# 导出依赖函数
from svc.apps.billing.dependencies import (
    get_subscription_plan_service,
    get_subscription_service,
    get_invoice_service,
    get_payment_service
)

# 导出错误处理函数
from svc.core.exceptions import (
    handle_route_errors,
    create_http_exception
)

# 导出错误码映射
from svc.core.exceptions import (
    ErrorCode
)

# 导出模型
from svc.apps.billing.models import (
    SubscriptionPlan,
    Subscription,
    Invoice,
    Payment
)

# 导出Schema
from svc.apps.billing.schemas import (
    # 订阅计划相关模型
    SubscriptionPlanCreate,
    SubscriptionPlanUpdate,
    SubscriptionPlanResponse,
    
    # 订阅相关模型
    SubscriptionCreate,
    SubscriptionUpdate,
    SubscriptionResponse,
    
    # 发票相关模型
    InvoiceCreate,
    InvoiceResponse,
    
    # 支付相关模型
    PaymentCreate,
    PaymentResponse
)

__all__ = [
    # 服务类
    "SubscriptionPlanService",
    "SubscriptionService",
    "InvoiceService",
    "PaymentService",
    
    # 依赖函数
    "get_subscription_plan_service",
    "get_subscription_service",
    "get_invoice_service",
    "get_payment_service",
    
    # 错误处理
    "handle_route_errors",
    "create_http_exception",
    "ErrorCode",
    
    # 模型
    "SubscriptionPlan",
    "Subscription",
    "Invoice", 
    "Payment",
    
    # Schema
    "SubscriptionPlanCreate",
    "SubscriptionPlanUpdate",
    "SubscriptionPlanResponse",
    "SubscriptionCreate",
    "SubscriptionUpdate",
    "SubscriptionResponse",
    "InvoiceCreate",
    "InvoiceResponse",
    "PaymentCreate",
    "PaymentResponse"
]
